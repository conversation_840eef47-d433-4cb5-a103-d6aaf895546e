#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking を使用した競馬予測システム（修正版）

主な修正点：
1. group_train/group_testの定義を修正（各サンプルのrace_idを保持）
2. データ整合性チェックを修正
3. TensorFlow Rankingのデータセット作成を修正
"""

import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse # argparseをインポート
import json
from datetime import datetime, timedelta

import pandas as pd
import numpy as np
import tensorflow as tf
import tensorflow_ranking as tfr
from sklearn.preprocessing import StandardScaler

# 再現性のためのシード設定
tf.random.set_seed(42)
np.random.seed(42)

# Mapping for Japanese column names to ASCII-safe names for TensorFlow layers
# This is necessary because TensorFlow layer/scope names have character restrictions.
JAPANESE_TO_ASCII_NAME_MAP = {
    '枠番': 'wakuban',
    '馬番': 'umaban',
    '性齢': 'seirei',
    '開催': 'kaisai',
    'jockey_id': 'jockey_id',
    'trainer_id': 'trainer_id',
    'owner_id': 'owner_id',
    'breeder_id': 'breeder_id',
    'father_id': 'father_id',
    'mother_father_id': 'mother_father_id',
}

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_prepare_data(data_path: str):
    """データの読み込みと前処理"""
    try:
        # データの読み込み
        logger.info("データを読み込み中...")
        comprehensive_df = pd.read_csv(data_path, low_memory=False)
        logger.info(f"データ読み込み完了: {comprehensive_df.shape}")

        # 日付列の変換
        comprehensive_df['date'] = pd.to_datetime(comprehensive_df['date'])

        # 基本的なデータクリーニング
        # 着順が数値でない行を除外
        comprehensive_df = comprehensive_df[comprehensive_df['着順'].astype(str).str.isdigit()]
        comprehensive_df['着順'] = comprehensive_df['着順'].astype(int)

        # 必要な列の存在確認
        required_columns = ['race_id', 'horse_id', 'jockey_id', 'trainer_id', '着順', 'date'] # Added jockey_id, trainer_id
        missing_columns = [col for col in required_columns if col not in comprehensive_df.columns]
        if missing_columns:
            raise ValueError(f"必要な列が不足しています: {missing_columns}")

        return comprehensive_df

    except Exception as e:
        logger.error(f"データ読み込みエラー: {e}")
        raise

def prepare_features_and_labels(df):
    """特徴量とラベルの準備"""
    numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
    # These are the columns we intend to embed.
    categorical_cols_to_embed = ['枠番', '馬番', '性齢', '開催', 'jockey_id', 'trainer_id', 'owner_id', 'breeder_id', 'father_id', 'mother_father_id']

    # 除外する列
    base_exclude_columns = [
        'index', '着順', 'race_id', 'horse_id', # '開催' is in categorical_cols_to_embed, so it's fine here.
        '騎手', '単勝', '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考', '調教師', '馬主', '賞金(万円)', 'レース名', 'date',
        'around', '調教師_horse_info', '馬主_horse_info', '生産者', '産地', 'セリ取引価格', '獲得賞金', '通算成績', '主な勝鞍', '近親馬', 'horse_name', 'trainer_id_horse_info',
        'father_name', 'mother_father_name', 'mother_name' ,'sibling_ids', '募集情報',
        'タイム', '着差', 'ﾀｲﾑ指数', '通過', '上り', '人気', 'weather', 'race_type', 'ground_state', 'mother_id' ,
    ]
    
    y_label_column_name = 'rank_score'
    # Create y_label_column_name first
    def calculate_relative_scores(group):
        # Ensure group is not empty and contains valid numbers
        if group.empty or not pd.api.types.is_numeric_dtype(group):
            return pd.Series([0] * len(group), index=group.index) # Return 0 for empty/non-numeric groups
        # Handle cases where all ranks are the same (e.g. single horse race, or all scratched)
        if group.nunique() == 1:
             return pd.Series([1] * len(group), index=group.index) # Assign a default score, e.g. 1
        max_rank = group.max()
        # Rank score: higher is better. 着順 1 -> max_rank, 着順 max_rank -> 1
        return max_rank + 1 - group

    df[y_label_column_name] = df.groupby('race_id')['着順'].transform(calculate_relative_scores)

    # Now define continuous_feature_columns, ensuring y_label_column_name is excluded
    continuous_feature_columns = [
        col for col in numeric_columns 
        if col not in base_exclude_columns and col not in categorical_cols_to_embed and col != y_label_column_name
    ]
    
    logger.info(f"連続特徴量数: {len(continuous_feature_columns)}")
    logger.info(f"連続特徴量 (最初の5): {continuous_feature_columns[:5]}...")
    logger.info(f"埋め込み用カテゴリカル特徴量: {categorical_cols_to_embed}")

    # Determine vocabulary sizes and ensure columns are integer
    vocab_sizes = {}
    # Store mappings for debugging or potential future use
    category_mappings = {} 

    for col in categorical_cols_to_embed:
        if col not in df.columns:
            logger.warning(f"カテゴリカル特徴量の列 '{col}' がデータフレームに存在しません。スキップします。")
            if col in continuous_feature_columns: # If it was accidentally in continuous
                continuous_feature_columns.remove(col)
            continue

        # Convert to string to handle mixed types and ensure NaNs are treated as a category.
        # Fill NaNs with a placeholder string before factorizing.
        df[col] = df[col].fillna("UNKNOWN_CAT").astype(str) # Changed "UNKNOWN" to "UNKNOWN_CAT" for clarity
        
        # Factorize the column. This maps each unique string to an integer from 0 to N-1.
        labels, uniques = pd.factorize(df[col])
        
        df[col] = labels # Replace original column with integer labels
        
        # The vocabulary size is the number of unique categories.
        # Keras Embedding layer expects input_dim = max_integer_index + 1.
        # Since factorize creates labels from 0 to N-1, N (len(uniques)) is the correct input_dim.
        vocab_sizes[col] = len(uniques) 
        category_mappings[col] = uniques # Store the mapping
        
        logger.info(f"'{col}' の語彙サイズ: {vocab_sizes[col]} (0から{len(uniques)-1}まで)")
        # If this column was mistakenly in continuous_feature_columns, remove it.
        if col in continuous_feature_columns:
            continuous_feature_columns.remove(col)

    return continuous_feature_columns, categorical_cols_to_embed, y_label_column_name, vocab_sizes


def split_data_by_date(df, test_ratio=0.2):
    """日付による学習・テストデータ分割"""
    df_sorted = df.sort_values('date')

    unique_dates = df_sorted['date'].unique()
    unique_dates = np.sort(unique_dates)

    # 分割点の計算
    total_dates = len(unique_dates)
    train_dates_count = int(total_dates * (1 - test_ratio))

    if train_dates_count >= total_dates:
        train_dates_count = total_dates - 1

    if train_dates_count <= 0 and total_dates > 0 : # ensure total_dates is not 0
        logger.warning("データが少なすぎるため、全データを学習用に使用します")
        return df_sorted.copy(), pd.DataFrame(), unique_dates[0]

    # 分割日の決定
    split_date = unique_dates[train_dates_count - 1]

    # データ分割
    train_df = df_sorted[df_sorted['date'] <= split_date].copy()
    test_df = df_sorted[df_sorted['date'] > split_date].copy()

    logger.info(f"分割日: {split_date}")
    logger.info(f"学習データ: {len(train_df)} 行, {train_df['race_id'].nunique()} レース")
    logger.info(f"テストデータ: {len(test_df)} 行, {test_df['race_id'].nunique()} レース")

    return train_df, test_df, split_date

def prepare_training_data(train_df, continuous_feature_columns, categorical_cols_to_embed, y_label_column_name):
    """学習データの準備"""
    if train_df.empty:
        logger.warning("学習データが空です")
        return None, None, None

    # 特徴量とラベルの抽出
    X_continuous_train_df = train_df[continuous_feature_columns].copy()
    X_continuous_train_np = X_continuous_train_df.fillna(0).astype(np.float32).values

    X_categorical_features_for_dict = {}
    for col_original_name in categorical_cols_to_embed:
        if col_original_name in train_df.columns: # Check if column exists (it should have been factorized)
            ascii_name = JAPANESE_TO_ASCII_NAME_MAP.get(col_original_name, col_original_name)
            # train_df[col_original_name] now holds integer labels from factorize
            X_categorical_features_for_dict[ascii_name] = train_df[col_original_name].values.astype(np.int32)
        else:
            logger.warning(f"学習データ準備: 列 '{col_original_name}' (ASCII: '{JAPANESE_TO_ASCII_NAME_MAP.get(col_original_name, col_original_name)}') が train_df に見つかりません。スキップします。")

    y_train = train_df[y_label_column_name].copy()
    group_train = train_df['race_id'].copy()

    # Combine features into a dictionary
    X_train_dict = {
        'continuous_features': X_continuous_train_np,
    }
    X_train_dict.update(X_categorical_features_for_dict)

    logger.info(f"学習データ準備完了:")
    logger.info(f"  X_continuous_train: {X_train_dict['continuous_features'].shape}")
    
    # Log using ASCII names as they are the keys in X_train_dict now
    processed_categorical_cols_in_dict = [JAPANESE_TO_ASCII_NAME_MAP.get(col, col) for col in categorical_cols_to_embed if col in train_df.columns]
    
    for ascii_name_key in X_train_dict.keys():
        if ascii_name_key == 'continuous_features':
            continue
        if ascii_name_key in processed_categorical_cols_in_dict: # Check if it's a known categorical feature
            logger.info(f"  X_{ascii_name_key}_train: {X_train_dict[ascii_name_key].shape}")
    logger.info(f"  y_train: {y_train.shape}")
    logger.info(f"  group_train: {group_train.shape}")
    logger.info(f"  ユニークレース数: {group_train.nunique()}")

    return X_train_dict, y_train, group_train

def prepare_test_data(test_df, continuous_feature_columns, categorical_cols_to_embed, y_label_column_name):
    """テストデータの準備"""
    if test_df.empty:
        logger.warning("テストデータが空です")
        return None, None, None

    X_continuous_test_df = test_df[continuous_feature_columns].copy()
    X_continuous_test_np = X_continuous_test_df.fillna(0).astype(np.float32).values

    X_categorical_features_for_dict = {}
    for col_original_name in categorical_cols_to_embed:
        if col_original_name in test_df.columns: # Check if column exists
            ascii_name = JAPANESE_TO_ASCII_NAME_MAP.get(col_original_name, col_original_name)
            X_categorical_features_for_dict[ascii_name] = test_df[col_original_name].values.astype(np.int32)
        else:
            logger.warning(f"テストデータ準備: 列 '{col_original_name}' (ASCII: '{JAPANESE_TO_ASCII_NAME_MAP.get(col_original_name, col_original_name)}') が test_df に見つかりません。スキップします。")

    y_test = test_df[y_label_column_name].copy()
    group_test = test_df['race_id'].copy()

    X_test_dict = {'continuous_features': X_continuous_test_np}
    X_test_dict.update(X_categorical_features_for_dict)
    y_test = y_test.fillna(0)
    
    logger.info(f"テストデータ準備完了:")
    logger.info(f"  X_continuous_test: {X_test_dict['continuous_features'].shape}")

    processed_categorical_cols_in_dict_test = [JAPANESE_TO_ASCII_NAME_MAP.get(col, col) for col in categorical_cols_to_embed if col in test_df.columns]
    for ascii_name_key in X_test_dict.keys():
        if ascii_name_key == 'continuous_features':
            continue
        if ascii_name_key in processed_categorical_cols_in_dict_test:
            logger.info(f"  X_{ascii_name_key}_test: {X_test_dict[ascii_name_key].shape}")
    logger.info(f"  y_test: {y_test.shape}")
    logger.info(f"  group_test: {group_test.shape}")
    return X_test_dict, y_test, group_test

def validate_data_consistency(X, y, groups, data_name="データ"):
    """データの整合性チェック (Xは辞書型を想定)"""
    if X is None or y is None or groups is None:
        logger.error(f"{data_name}: 一部のデータがNoneです")
        return False

    # X is a dictionary, check length based on one of its arrays, e.g., continuous_features
    # Assuming 'continuous_features' always exists if X is not None
    if 'continuous_features' not in X or X['continuous_features'] is None:
        logger.error(f"{data_name}: 'continuous_features' がXに含まれていないかNoneです。")
        return False
        
    num_samples_X = len(X['continuous_features'])

    if num_samples_X != len(y) or num_samples_X != len(groups):

        logger.error(f"{data_name}: データの行数が一致しません")
        logger.error(f"  X (continuous_features): {num_samples_X}, y: {len(y)}, groups: {len(groups)}")
        return False

    if num_samples_X == 0:
        logger.warning(f"{data_name}: データが空です")
        return False

    logger.info(f"{data_name}: 整合性チェック完了 - {num_samples_X} 行")
    return True

def create_ranking_dataset(X, y, groups, list_size: int):
    """ランキング学習用のデータセットをリスト形式で作成 (Xは辞書型を想定)

    Args:
        X (Dict[str, np.ndarray]): 特徴量の辞書。キーは特徴量名、値は (サンプル数, [特徴量数]) のNumpy配列。
                                     'continuous_features' キーは必須。
        y (pd.Series or np.ndarray): ラベルデータ (サンプル数)
        groups (pd.Series or np.ndarray): 各サンプルが属するグループID (サンプル数)
        list_size (int): 各グループのリストの固定長 (引数で受け取る)

    Returns:
        Tuple[Dict[str, np.ndarray], np.ndarray]: 整形済みの特徴量辞書とラベルデータ
    """
    unique_groups = np.unique(groups)
    # Initialize collectors for each feature type
    X_list_collectors = {key: [] for key in X.keys()}
    y_list_collector = [] # This will collect numpy arrays

    num_continuous_features = X['continuous_features'].shape[1]
    categorical_cols = [key for key in X.keys() if key != 'continuous_features']

    for group in unique_groups:
        group_mask = groups == group
        
        current_group_y_np = y[group_mask] # y is pd.Series, convert to np later if needed or use .values
        if isinstance(current_group_y_np, pd.Series):
            current_group_y_np = current_group_y_np.values

        if len(current_group_y_np) == 0:
            continue
        # Extract features for the group
        group_X_continuous = X['continuous_features'][group_mask]
        group_X_categoricals = {col: X[col][group_mask] for col in categorical_cols}

        current_list_len = len(current_group_y_np)

        # パディング処理（list_sizeに満たない場合）
        if current_list_len < list_size:
            pad_size = list_size - current_list_len
            
            # Pad continuous features
            pad_X_cont = np.zeros((pad_size, num_continuous_features), dtype=np.float32)
            padded_X_continuous = np.vstack([group_X_continuous, pad_X_cont])
            
            # Pad categorical features (pad with 0, assuming 0 is for padding/unknown)
            padded_X_categoricals = {}
            for col in categorical_cols:
                pad_X_cat = np.zeros(pad_size, dtype=np.int32) # Categorical IDs are int
                padded_X_categoricals[col] = np.concatenate([group_X_categoricals[col], pad_X_cat])
            
            # Pad labels
            pad_y = np.zeros(pad_size, dtype=np.float32)
            padded_y = np.concatenate([current_group_y_np, pad_y])


        elif current_list_len > list_size:
            # 上位list_size個に制限（yの値に基づいて）
            top_indices = np.argsort(current_group_y_np)[::-1][:list_size]         
            padded_X_continuous = group_X_continuous[top_indices]
            padded_X_categoricals = {col: group_X_categoricals[col][top_indices] for col in categorical_cols}
            padded_y = current_group_y_np[top_indices]
        else: # current_list_len == list_size
            padded_X_continuous = group_X_continuous
            padded_X_categoricals = group_X_categoricals
            padded_y = current_group_y_np
        X_list_collectors['continuous_features'].append(padded_X_continuous)
        for col in categorical_cols:
            X_list_collectors[col].append(padded_X_categoricals[col])
        y_list_collector.append(padded_y)
    if not X_list_collectors['continuous_features']: # Check if any group was processed
        logger.warning("有効なグループが見つかりませんでした")
        empty_X_dict = {key: np.array([]) for key in X.keys()}
        return empty_X_dict, np.array([])
    # Stack collected lists into numpy arrays
    final_X_list_dict = {
        'continuous_features': np.array(X_list_collectors['continuous_features'], dtype=np.float32)
    }
    for col in categorical_cols: # These keys are already ASCII-safe from prepare_data functions
        final_X_list_dict[col] = np.array(X_list_collectors[col], dtype=np.int32)
    y_list_np = np.array(y_list_collector, dtype=np.float32)
    logger.info(f"ランキングデータセット作成完了. Continuous shape: {final_X_list_dict['continuous_features'].shape}")
    return final_X_list_dict, y_list_np

def create_tf_dataset(X_list_dict, y_list_np, batch_size: int):
    """TensorFlowデータセットを作成"""
    if len(y_list_np) == 0: # Check based on y_list as X_list_dict might not be empty dict
        logger.warning("空のデータセットです")
        return None

    # TensorFlow Rankingでは、(features, labels)のタプル形式で渡す
    # Ensure all feature arrays in X_list_dict have the same first dimension as y_list_np
    for key, arr in X_list_dict.items():
        if arr.shape[0] != y_list_np.shape[0]:
            logger.error(f"Feature '{key}' shape {arr.shape} incompatible with y_list shape {y_list_np.shape}")
            return None
    # Convert numpy arrays in X_list_dict to appropriate types
    processed_X_list_dict = {}
    for key, arr in X_list_dict.items():
        if key == 'continuous_features':
            processed_X_list_dict[key] = arr.astype(np.float32)
        else: # Categorical features
            processed_X_list_dict[key] = arr.astype(np.int32)
    dataset = tf.data.Dataset.from_tensor_slices((
        processed_X_list_dict,
        y_list_np.astype(np.float32)
    ))
    return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)

def create_ranking_model(
    list_size: int, 
    num_continuous_features: int,
    categorical_embedding_configs: Dict[str, Dict[str, int]], # e.g. {'jockey_id': {'vocab_size': 100, 'embed_dim': 10}}
    hidden_units: List[int],
    dropout_rate: float
):
    """ランキングモデルを作成"""
    from tensorflow.keras import layers, Model, Input

    input_layers_list = []
    feature_embeddings_list = []

    # Continuous features input
    continuous_input = Input(shape=(list_size, num_continuous_features), name='continuous_features', dtype=tf.float32)
    input_layers_list.append(continuous_input)
    
    # Continuous features are assumed to be scaled and ready for concatenation
    # Add a TimeDistributed Dense layer here if you want to process them before merging
    feature_embeddings_list.append(continuous_input)

    # Categorical features inputs and embeddings
    for cat_col, config in categorical_embedding_configs.items():
        cat_input = Input(shape=(list_size,), name=cat_col, dtype='int32')
        input_layers_list.append(cat_input)
        
        embedding_layer = layers.Embedding(
            input_dim=config['vocab_size'], 
            output_dim=config['embed_dim'],
            input_length=list_size, # Ensures output shape is (batch, list_size, embed_dim)
            name=f'{cat_col}_embedding' # cat_col is already ASCII-safe here
        )(cat_input)
        feature_embeddings_list.append(embedding_layer)

    # Concatenate all feature representations
    if len(feature_embeddings_list) > 1:
        merged_features = layers.Concatenate(axis=-1, name='merged_features')(feature_embeddings_list)
    elif feature_embeddings_list: # Only one type of feature (e.g. only continuous)
        merged_features = feature_embeddings_list[0]
    else:
        raise ValueError("No features provided to the model.")

    # DNN part (TimeDistributed)
    x = merged_features
    for units in hidden_units:
        x = layers.TimeDistributed(layers.Dense(units, activation='relu'))(x)
        x = layers.TimeDistributed(layers.Dropout(dropout_rate))(x)

    # 出力層（各アイテムのスコア予測）
    # (batch_size, list_size, 1)
    scores_with_time_dim = layers.TimeDistributed(layers.Dense(1), name='item_scores')(x) 
    # Reshape to (batch_size, list_size) for TF-Ranking losses/metrics
    outputs = layers.Reshape((list_size,), name='scores')(scores_with_time_dim) 
    model = Model(inputs=input_layers_list, outputs=outputs)
    return model

def compile_ranking_model(model, learning_rate: float, loss_function_name: str = 'SoftmaxLoss'):
    """ランキングモデルをコンパイル"""
    from tensorflow.keras.optimizers import Adam

    # 損失関数の選択
    if loss_function_name == 'SoftmaxLoss':
        loss_fn = tfr.keras.losses.SoftmaxLoss()
    elif loss_function_name == 'ListMLELoss':
        loss_fn = tfr.keras.losses.ListMLELoss()
    elif loss_function_name == 'PairwiseLogisticLoss':
        loss_fn = tfr.keras.losses.PairwiseLogisticLoss()
    elif loss_function_name == 'PairwiseHingeLoss':
        loss_fn = tfr.keras.losses.PairwiseHingeLoss()
    elif loss_function_name == 'ApproxNDCGLoss':
        loss_fn = tfr.keras.losses.ApproxNDCGLoss()
    # 必要に応じて他の損失関数を追加
    else:
        logger.warning(f"未対応の損失関数名: {loss_function_name}。デフォルトのSoftmaxLossを使用します。")
        loss_fn = tfr.keras.losses.SoftmaxLoss()

    # TensorFlow Rankingの損失関数とメトリクスを使用
    model.compile(
        optimizer=Adam(learning_rate=learning_rate),
        loss=loss_fn,
        metrics=[
            tfr.keras.metrics.NDCGMetric(name='ndcg_5', topn=5),
            tfr.keras.metrics.NDCGMetric(name='ndcg_3', topn=3),
            tfr.keras.metrics.MRRMetric(name='mrr')
        ]
    )
    return model

def train_ranking_model(model, train_dataset, validation_dataset=None, epochs: int = 50, patience: int = 10):
    """ランキングモデルを学習"""
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

    callbacks = [
        EarlyStopping(
            monitor='val_ndcg_3' if validation_dataset else 'ndcg_3',
            patience=patience,
            restore_best_weights=True,
            mode='max'
        ),
        ReduceLROnPlateau(
            monitor='val_ndcg_3' if validation_dataset else 'ndcg_3',
            factor=0.5,
            patience=5,
            mode='max'
        )
    ]

    try:
        history = model.fit(
            train_dataset,
            validation_data=validation_dataset,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )
        return history
    except Exception as e:
        logger.error(f"モデル学習中にエラーが発生しました: {e}")
        return None

def main():
    """メイン処理"""
    parser = argparse.ArgumentParser(description="TensorFlow Ranking 競馬予測システム")
    parser.add_argument('--data_path', type=str, default='dataframe.csv', help='入力データCSVファイルのパス')
    parser.add_argument('--test_ratio', type=float, default=0.2, help='テストデータの割合')
    parser.add_argument('--list_size', type=int, default=10, help='各レースのリストサイズ（パディング/切り捨て用）')
    parser.add_argument('--hidden_units', type=int, nargs='+', default=[64, 32], help='モデルの隠れ層のユニット数（スペース区切り）')
    parser.add_argument('--dropout_rate', type=float, default=0.2, help='ドロップアウト率')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学習率')
    parser.add_argument('--batch_size', type=int, default=32, help='バッチサイズ')
    parser.add_argument('--epochs', type=int, default=30, help='エポック数')
    parser.add_argument('--patience', type=int, default=5, help='早期停止のPatience')
    parser.add_argument('--loss_function', type=str, default='SoftmaxLoss',
                        choices=['SoftmaxLoss', 'ListMLELoss', 'PairwiseLogisticLoss', 'PairwiseHingeLoss', 'ApproxNDCGLoss'],
                        help='使用する損失関数 (例: SoftmaxLoss, ListMLELoss, PairwiseLogisticLoss)')
    parser.add_argument('--save_model_path', type=str, default='ranking_model',
                        help='学習済みモデルの保存先パス (SavedModel形式の場合はディレクトリ、HDF5形式の場合は.h5ファイル)')


    args = parser.parse_args()

    logger.info(f"実行引数: {args}")

    try:
        # データの読み込みと準備
        raw_df = load_and_prepare_data(args.data_path)
        # process_df will be modified by prepare_features_and_labels
        processed_df = raw_df.copy()
        continuous_feature_columns, categorical_cols_to_embed, y_label_column_name, vocab_sizes = \
            prepare_features_and_labels(processed_df) # Pass processed_df to be modified in-place

        # データ分割
        train_df, test_df, split_date = split_data_by_date(processed_df, test_ratio=args.test_ratio)
        # 学習データの準備
        X_train_dict, y_train_series, group_train_series = prepare_training_data(
            train_df, continuous_feature_columns, categorical_cols_to_embed, y_label_column_name
        )

        # テストデータの準備
        X_test_dict, y_test_series, group_test_series = prepare_test_data(
            test_df, continuous_feature_columns, categorical_cols_to_embed, y_label_column_name
        )

        # データ整合性チェック
        train_data_valid = validate_data_consistency(X_train_dict, y_train_series, group_train_series, "学習データ")
        test_data_valid = validate_data_consistency(X_test_dict, y_test_series, group_test_series, "テストデータ")

        if not train_data_valid:
            logger.error("学習データが不十分または不整合なため、モデル学習は実行されませんでした。")
            return

        if not test_data_valid:
            logger.warning("テストデータがないか不整合なため、予測はスキップされました。")
            X_test_dict = None # Ensure it's None if not valid


        # 特徴量の標準化
        logger.info("特徴量の標準化を実行中...")
        scaler = StandardScaler()

        X_train_scaled_dict = X_train_dict.copy()
        X_train_scaled_dict['continuous_features'] = scaler.fit_transform(X_train_dict['continuous_features'])

        if test_data_valid and X_test_dict is not None:
            X_test_scaled_dict = X_test_dict.copy()
            X_test_scaled_dict['continuous_features'] = scaler.transform(X_test_dict['continuous_features'])
        else:
            X_test_scaled_dict = None

        # ランキング用データセットの作成
        logger.info("ランキング用データセットを作成中...")

        X_train_list_dict, y_train_list_np = create_ranking_dataset(
            X_train_scaled_dict,
            y_train_series.values, # Pass numpy array
            group_train_series.values, # Pass numpy array
            args.list_size
        )

        if test_data_valid and X_test_scaled_dict is not None and y_test_series is not None and group_test_series is not None:
            X_test_list_dict, y_test_list_np = create_ranking_dataset(
                X_test_scaled_dict, y_test_series.values, group_test_series.values, args.list_size
            )
        else:
            X_test_list_dict, y_test_list_np = None, None

        # TensorFlowデータセットの作成
        if X_train_list_dict and X_train_list_dict['continuous_features'].size > 0 : # Check if not empty
            train_dataset = create_tf_dataset(X_train_list_dict, y_train_list_np, args.batch_size)
            logger.info("TensorFlowデータセット作成完了")

            if test_data_valid and X_test_list_dict is not None and X_test_list_dict['continuous_features'].size > 0:
                test_dataset = create_tf_dataset(X_test_list_dict, y_test_list_np, args.batch_size)
                logger.info("テスト用TensorFlowデータセット作成完了")
            else:
                test_dataset = None
                logger.info("テスト用データセットはスキップされました")

            # モデルの作成と学習
            logger.info("ランキングモデルを作成中...")
            num_continuous_features = len(continuous_feature_columns)
            embedding_dim_default = 10 # You can make this an argparse argument
            categorical_embedding_configs = {
                JAPANESE_TO_ASCII_NAME_MAP.get(col, col): {'vocab_size': vocab_sizes[col], 'embed_dim': embedding_dim_default}
                for col in categorical_cols_to_embed if col in vocab_sizes
            }
            model = create_ranking_model(
            args.list_size, num_continuous_features, categorical_embedding_configs,
            args.hidden_units, args.dropout_rate)

            model = compile_ranking_model(model, learning_rate=args.learning_rate, loss_function_name=args.loss_function)

            logger.info("モデル学習を開始...")
            history = train_ranking_model(
                model,
                train_dataset,
                validation_dataset=test_dataset,
                epochs=30,
                patience=args.patience
            )

            if history is not None:
                logger.info("モデル学習が完了しました")

                # 学習結果の表示
                final_metrics = history.history
                if 'ndcg_3' in final_metrics:
                    best_ndcg = max(final_metrics['ndcg_3'])
                    logger.info(f"最高NDCG@3スコア: {best_ndcg:.4f}")

                if test_dataset is not None:
                    logger.info("テストデータで評価中...")
                    test_results = model.evaluate(test_dataset, verbose=0)
                    logger.info(f"テストデータ評価結果: {test_results}")
            else:
                logger.error("モデル学習に失敗しました")
                return # 学習失敗時はモデル保存をスキップ

            # モデルの保存
            if args.save_model_path:
                logger.info(f"モデルを {args.save_model_path} に保存中...")
                model.save(args.save_model_path) # SavedModel形式で保存
                logger.info(f"モデルをSavedModel形式で {args.save_model_path} に保存しました。")
                # HDF5形式で保存する場合 (ファイル名を .h5 にする)
                # h5_save_path = args.save_model_path if args.save_model_path.endswith(".h5") else args.save_model_path + ".h5"
                # model.save(h5_save_path)
                # logger.info(f"モデルをHDF5形式で {h5_save_path} に保存しました。")
        else:
            logger.error("ランキングデータセットの作成に失敗しました")
            return

        logger.info("TensorFlow Rankingを使用した競馬予測システムの実行が完了しました。")

    except Exception as e:
        logger.error(f"処理中にエラーが発生しました: {e}")
        raise

if __name__ == "__main__":
    main()
