#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特徴量エンジニアリング管理システム

特徴量の追加、管理、適用を体系的に行うためのクラス群
"""

import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional, Callable, Union
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yaml
from datetime import datetime, timedelta
import joblib

# プロジェクトのモジュールをインポート
sys.path.append('.')
from feature_definitions import FeatureDefinition, FeatureCategory, FeatureType
from feature_calculators import FeatureCalculators

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FeatureEngineeringManager:
    """
    特徴量エンジニアリングを管理するメインクラス

    機能:
    - 特徴量の定義と管理
    - 特徴量の計算と適用
    - 特徴量の保存と読み込み
    - 特徴量の依存関係管理
    """

    def __init__(self, config_path: str = "feature_config.yaml"):
        """
        初期化

        Parameters
        ----------
        config_path : str
            特徴量設定ファイルのパス
        """
        self.config_path = config_path
        self.feature_definitions: Dict[str, FeatureDefinition] = {}
        self.calculators = FeatureCalculators()
        self.feature_cache: Dict[str, pd.DataFrame] = {}

        # 設定ファイルを読み込み
        self.load_config()

        # 特徴量定義を初期化
        self._initialize_feature_definitions()

    def load_config(self):
        """設定ファイルを読み込み"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f)
                logger.info(f"設定ファイルを読み込みました: {self.config_path}")
            else:
                # デフォルト設定を作成
                self.config = self._create_default_config()
                self.save_config()
                logger.info("デフォルト設定ファイルを作成しました")
        except Exception as e:
            logger.error(f"設定ファイルの読み込みに失敗: {e}")
            self.config = self._create_default_config()

    def save_config(self):
        """設定ファイルを保存"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"設定ファイルを保存しました: {self.config_path}")
        except Exception as e:
            logger.error(f"設定ファイルの保存に失敗: {e}")

    def _create_default_config(self) -> Dict[str, Any]:
        """デフォルト設定を作成"""
        return {
            'feature_groups': {
                'basic': {
                    'enabled': True,
                    'description': '基本特徴量（馬番、枠番など）'
                },
                'performance': {
                    'enabled': True,
                    'description': '過去成績統計特徴量'
                },
                'pedigree': {
                    'enabled': True,
                    'description': '血統特徴量'
                },
                'jockey': {
                    'enabled': True,
                    'description': '騎手特徴量'
                },
                'trainer': {
                    'enabled': True,
                    'description': '調教師特徴量'
                },
                'race_condition': {
                    'enabled': True,
                    'description': 'レース条件特徴量'
                },
                'advanced': {
                    'enabled': False,
                    'description': '高度な特徴量（実験的）'
                }
            },
            'performance_features': {
                'lookback_races': 10,  # 過去何レース分を見るか
                'min_races': 3,        # 最低必要レース数
                'weight_decay': 0.9    # 古いレースの重み減衰
            },
            'encoding': {
                'categorical_threshold': 50,  # カテゴリカル変数の閾値
                'rare_category_threshold': 0.01  # 稀なカテゴリの閾値
            },
            'cache': {
                'enabled': True,
                'max_size_mb': 500
            }
        }

    def _initialize_feature_definitions(self):
        """特徴量定義を初期化"""
        # 基本特徴量
        if self.config['feature_groups']['basic']['enabled']:
            self._add_basic_features()

        # 過去成績特徴量
        if self.config['feature_groups']['performance']['enabled']:
            self._add_performance_features()

        # 血統特徴量
        if self.config['feature_groups']['pedigree']['enabled']:
            self._add_pedigree_features()

        # 騎手特徴量
        if self.config['feature_groups']['jockey']['enabled']:
            self._add_jockey_features()

        # 調教師特徴量
        if self.config['feature_groups']['trainer']['enabled']:
            self._add_trainer_features()

        # レース条件特徴量
        if self.config['feature_groups']['race_condition']['enabled']:
            self._add_race_condition_features()

        # 高度な特徴量
        if self.config['feature_groups']['advanced']['enabled']:
            self._add_advanced_features()

    def _add_basic_features(self):
        """基本特徴量を追加"""
        basic_features = [
            FeatureDefinition(
                name='馬番',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='馬番（1-18）',
                required_columns=['馬番'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='枠番',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='枠番（1-8）',
                required_columns=['枠番'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='斤量',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='斤量（kg）',
                required_columns=['斤量'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='人気',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='人気順位',
                required_columns=['人気'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='性別_数値',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='性別を数値化（牡=1, 牝=2, セ=3）',
                required_columns=['性齢'],
                calculator=self.calculators.encode_sex
            ),
            FeatureDefinition(
                name='年齢',
                category=FeatureCategory.BASIC,
                feature_type=FeatureType.NUMERICAL,
                description='馬の年齢',
                required_columns=['性齢'],
                calculator=self.calculators.extract_age
            )
        ]

        for feature in basic_features:
            self.feature_definitions[feature.name] = feature

    def _add_performance_features(self):
        """過去成績特徴量を追加"""
        performance_features = [
            FeatureDefinition(
                name='出走回数',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の出走回数',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_race_count,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='平均着順',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の平均着順',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_avg_rank,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='勝率',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の勝率（1着率）',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_win_rate,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='連対率',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の連対率（1-2着率）',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_place_rate,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='複勝率',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の複勝率（1-3着率）',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_show_rate,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='平均賞金',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の平均獲得賞金',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_avg_prize,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='最高賞金',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='過去の最高獲得賞金',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_max_prize,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='前走着順',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='前走の着順',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_last_rank,
                dependencies=['horse_results_df']
            ),
            FeatureDefinition(
                name='前走からの間隔',
                category=FeatureCategory.PERFORMANCE,
                feature_type=FeatureType.NUMERICAL,
                description='前走からの日数',
                required_columns=['horse_id'],
                calculator=self.calculators.calculate_days_since_last_race,
                dependencies=['horse_results_df', 'race_date']
            )
        ]

        for feature in performance_features:
            self.feature_definitions[feature.name] = feature

    def _add_pedigree_features(self):
        """血統特徴量を追加"""
        pedigree_features = [
            FeatureDefinition(
                name='父馬ID',
                category=FeatureCategory.PEDIGREE,
                feature_type=FeatureType.CATEGORICAL,
                description='父馬のID',
                required_columns=['father_id'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='母父馬ID',
                category=FeatureCategory.PEDIGREE,
                feature_type=FeatureType.CATEGORICAL,
                description='母父馬のID',
                required_columns=['mother_father_id'],
                calculator=self.calculators.identity
            )
        ]

        for feature in pedigree_features:
            self.feature_definitions[feature.name] = feature

    def _add_jockey_features(self):
        """騎手特徴量を追加"""
        jockey_features = [
            FeatureDefinition(
                name='騎手ID',
                category=FeatureCategory.JOCKEY,
                feature_type=FeatureType.CATEGORICAL,
                description='騎手のID',
                required_columns=['jockey_id'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='騎手勝率',
                category=FeatureCategory.JOCKEY,
                feature_type=FeatureType.NUMERICAL,
                description='騎手の過去勝率',
                required_columns=['jockey_id'],
                calculator=self.calculators.calculate_jockey_win_rate,
                dependencies=['horse_results_df']
            )
        ]

        for feature in jockey_features:
            self.feature_definitions[feature.name] = feature

    def _add_trainer_features(self):
        """調教師特徴量を追加"""
        trainer_features = [
            FeatureDefinition(
                name='調教師ID',
                category=FeatureCategory.TRAINER,
                feature_type=FeatureType.CATEGORICAL,
                description='調教師のID',
                required_columns=['trainer_id'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='調教師勝率',
                category=FeatureCategory.TRAINER,
                feature_type=FeatureType.NUMERICAL,
                description='調教師の過去勝率',
                required_columns=['trainer_id'],
                calculator=self.calculators.calculate_trainer_win_rate,
                dependencies=['horse_results_df']
            )
        ]

        for feature in trainer_features:
            self.feature_definitions[feature.name] = feature

    def _add_race_condition_features(self):
        """レース条件特徴量を追加"""
        race_condition_features = [
            FeatureDefinition(
                name='距離',
                category=FeatureCategory.RACE_CONDITION,
                feature_type=FeatureType.NUMERICAL,
                description='レース距離（m）',
                required_columns=['距離'],
                calculator=self.calculators.identity
            ),
            FeatureDefinition(
                name='距離カテゴリ',
                category=FeatureCategory.RACE_CONDITION,
                feature_type=FeatureType.CATEGORICAL,
                description='距離カテゴリ（短距離、マイル、中距離、長距離）',
                required_columns=['距離'],
                calculator=self.calculators.encode_race_distance_category
            ),
            FeatureDefinition(
                name='芝ダート',
                category=FeatureCategory.RACE_CONDITION,
                feature_type=FeatureType.CATEGORICAL,
                description='芝・ダートの区分',
                required_columns=['芝・ダート'],
                calculator=self.calculators.encode_race_surface
            ),
            FeatureDefinition(
                name='開催場',
                category=FeatureCategory.RACE_CONDITION,
                feature_type=FeatureType.CATEGORICAL,
                description='開催競馬場',
                required_columns=['開催'],
                calculator=self.calculators.identity
            )
        ]

        for feature in race_condition_features:
            self.feature_definitions[feature.name] = feature

    def _add_advanced_features(self):
        """高度な特徴量を追加"""
        advanced_features = [
            FeatureDefinition(
                name='馬番_枠番_交互作用',
                category=FeatureCategory.ADVANCED,
                feature_type=FeatureType.NUMERICAL,
                description='馬番と枠番の交互作用',
                required_columns=['馬番', '枠番'],
                calculator=self._calculate_interaction_umaban_wakuban
            ),
            FeatureDefinition(
                name='人気_斤量_比率',
                category=FeatureCategory.ADVANCED,
                feature_type=FeatureType.NUMERICAL,
                description='人気と斤量の比率',
                required_columns=['人気', '斤量'],
                calculator=self._calculate_popularity_weight_ratio
            )
        ]

        for feature in advanced_features:
            self.feature_definitions[feature.name] = feature

    def _calculate_interaction_umaban_wakuban(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """馬番と枠番の交互作用を計算"""
        if '馬番' in data.columns and '枠番' in data.columns:
            return data['馬番'] * data['枠番']
        else:
            return pd.Series(index=data.index, dtype=float)

    def _calculate_popularity_weight_ratio(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """人気と斤量の比率を計算"""
        if '人気' in data.columns and '斤量' in data.columns:
            return data['人気'] / data['斤量']
        else:
            return pd.Series(index=data.index, dtype=float)

    # ===== 特徴量計算と適用 =====

    def calculate_features(self, data: pd.DataFrame,
                          feature_names: Optional[List[str]] = None,
                          dependencies: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        指定された特徴量を計算

        Parameters
        ----------
        data : pd.DataFrame
            入力データ
        feature_names : List[str], optional
            計算する特徴量名のリスト（Noneの場合は全ての有効な特徴量）
        dependencies : Dict[str, Any], optional
            依存データ（horse_results_df など）

        Returns
        -------
        pd.DataFrame
            特徴量が追加されたDataFrame
        """
        try:
            logger.info("特徴量計算を開始...")

            if dependencies is None:
                dependencies = {}

            # 計算する特徴量を決定
            if feature_names is None:
                features_to_calculate = [f for f in self.feature_definitions.values() if f.enabled]
            else:
                features_to_calculate = [self.feature_definitions[name] for name in feature_names
                                       if name in self.feature_definitions and self.feature_definitions[name].enabled]

            # 依存関係に基づいてソート
            features_to_calculate = self._sort_features_by_dependencies(features_to_calculate)

            result_df = data.copy()
            calculated_features = {}

            for feature in features_to_calculate:
                try:
                    logger.debug(f"特徴量 '{feature.name}' を計算中...")

                    # 必要なカラムが存在するかチェック
                    missing_columns = [col for col in feature.required_columns if col not in result_df.columns]
                    if missing_columns:
                        logger.warning(f"特徴量 '{feature.name}': 必要なカラムが不足 {missing_columns}")
                        continue

                    # 依存データを準備
                    calc_kwargs = dependencies.copy()
                    calc_kwargs.update(feature.parameters)

                    # 特徴量を計算
                    if len(feature.required_columns) == 1:
                        feature_values = feature.calculator(result_df, feature.required_columns[0], **calc_kwargs)
                    else:
                        feature_values = feature.calculator(result_df, **calc_kwargs)

                    # バリデーション
                    if feature.validate(feature_values):
                        result_df[feature.name] = feature_values
                        calculated_features[feature.name] = feature
                        logger.debug(f"特徴量 '{feature.name}' の計算完了")
                    else:
                        logger.warning(f"特徴量 '{feature.name}' のバリデーションに失敗")

                except Exception as e:
                    logger.error(f"特徴量 '{feature.name}' の計算中にエラー: {e}")
                    continue

            logger.info(f"特徴量計算完了: {len(calculated_features)}個の特徴量を追加")
            return result_df

        except Exception as e:
            logger.error(f"特徴量計算中にエラー: {e}")
            return data

    def _sort_features_by_dependencies(self, features: List[FeatureDefinition]) -> List[FeatureDefinition]:
        """依存関係に基づいて特徴量をソート"""
        # 簡単な実装：依存関係のない特徴量を先に計算
        no_deps = [f for f in features if not f.dependencies]
        with_deps = [f for f in features if f.dependencies]
        return no_deps + with_deps

    def get_feature_importance_ranking(self, model=None, feature_names: Optional[List[str]] = None) -> pd.DataFrame:
        """
        特徴量重要度ランキングを取得

        Parameters
        ----------
        model : object, optional
            学習済みモデル（feature_importances_属性を持つ）
        feature_names : List[str], optional
            対象特徴量名

        Returns
        -------
        pd.DataFrame
            特徴量重要度ランキング
        """
        try:
            if feature_names is None:
                feature_names = list(self.feature_definitions.keys())

            importance_data = []

            for name in feature_names:
                if name in self.feature_definitions:
                    feature = self.feature_definitions[name]
                    importance_data.append({
                        'feature_name': name,
                        'category': feature.category.value,
                        'type': feature.feature_type.value,
                        'importance_level': feature.importance.value,
                        'description': feature.description,
                        'enabled': feature.enabled
                    })

            importance_df = pd.DataFrame(importance_data)

            # モデルの重要度が利用可能な場合は追加
            if model is not None and hasattr(model, 'feature_importances_'):
                model_importance = dict(zip(feature_names, model.feature_importances_))
                importance_df['model_importance'] = importance_df['feature_name'].map(model_importance)
                importance_df = importance_df.sort_values('model_importance', ascending=False)

            return importance_df

        except Exception as e:
            logger.error(f"特徴量重要度ランキング取得中にエラー: {e}")
            return pd.DataFrame()

    def add_custom_feature(self, feature_definition: FeatureDefinition):
        """カスタム特徴量を追加"""
        self.feature_definitions[feature_definition.name] = feature_definition
        logger.info(f"カスタム特徴量 '{feature_definition.name}' を追加しました")

    def remove_feature(self, feature_name: str):
        """特徴量を削除"""
        if feature_name in self.feature_definitions:
            del self.feature_definitions[feature_name]
            logger.info(f"特徴量 '{feature_name}' を削除しました")
        else:
            logger.warning(f"特徴量 '{feature_name}' が見つかりません")

    def enable_feature(self, feature_name: str):
        """特徴量を有効化"""
        if feature_name in self.feature_definitions:
            self.feature_definitions[feature_name].enabled = True
            logger.info(f"特徴量 '{feature_name}' を有効化しました")

    def disable_feature(self, feature_name: str):
        """特徴量を無効化"""
        if feature_name in self.feature_definitions:
            self.feature_definitions[feature_name].enabled = False
            logger.info(f"特徴量 '{feature_name}' を無効化しました")

    def get_feature_summary(self) -> Dict[str, Any]:
        """特徴量の要約情報を取得"""
        total_features = len(self.feature_definitions)
        enabled_features = len([f for f in self.feature_definitions.values() if f.enabled])

        category_counts = {}
        for category in FeatureCategory:
            count = len([f for f in self.feature_definitions.values() if f.category == category])
            category_counts[category.value] = count

        type_counts = {}
        for ftype in FeatureType:
            count = len([f for f in self.feature_definitions.values() if f.feature_type == ftype])
            type_counts[ftype.value] = count

        return {
            'total_features': total_features,
            'enabled_features': enabled_features,
            'disabled_features': total_features - enabled_features,
            'category_counts': category_counts,
            'type_counts': type_counts,
            'config_path': self.config_path
        }

    def list_features(self, category: Optional[FeatureCategory] = None,
                     enabled_only: bool = False) -> List[str]:
        """特徴量名のリストを取得"""
        features = self.feature_definitions.values()

        if category is not None:
            features = [f for f in features if f.category == category]

        if enabled_only:
            features = [f for f in features if f.enabled]

        return [f.name for f in features]

    def save_features_info(self, filepath: str):
        """特徴量情報をファイルに保存"""
        try:
            features_info = []
            for feature in self.feature_definitions.values():
                features_info.append(feature.get_info())

            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump({'features': features_info}, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"特徴量情報を保存しました: {filepath}")

        except Exception as e:
            logger.error(f"特徴量情報の保存に失敗: {e}")

    def load_features_info(self, filepath: str):
        """特徴量情報をファイルから読み込み"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            # 読み込んだ情報から特徴量定義を再構築
            # （実装は省略 - 必要に応じて追加）

            logger.info(f"特徴量情報を読み込みました: {filepath}")

        except Exception as e:
            logger.error(f"特徴量情報の読み込みに失敗: {e}")
