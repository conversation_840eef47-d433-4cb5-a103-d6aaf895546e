#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
既存ファイルを新しい構造にコピーするスクリプト
"""

import os
import shutil
from pathlib import Path

def copy_files():
    """既存ファイルを新しい構造にコピー"""

    # コピー対象ファイルのマッピング
    file_mappings = {
        # プロセッサー
        '../module/race_data_processor.py': 'core/processors/race_processor.py',
        '../module/horse_processor.py': 'core/processors/horse_processor.py',
        '../module/data_merger.py': 'core/processors/data_merger.py',
        '../comprehensive_data_integrator.py': 'core/processors/comprehensive_integrator.py',

        # スクレイパー
        '../module/refactored_scrap.py': 'core/scrapers/refactored_scrap.py',
        '../module/constants.py': 'core/utils/constants.py',

        # 特徴量エンジニアリング
        '../feature_engineering_manager.py': 'core/features/manager.py',
        '../feature_definitions.py': 'core/features/definitions.py',
        '../feature_calculators.py': 'core/features/calculators.py',
        '../feature_config.yaml': 'core/features/config.yaml',

        # 予測システム
        '../live_race_predictor.py': 'prediction/live_predictor.py',
        '../race_predictor.py': 'prediction/race_predictor.py',

        # 使用例
        '../live_race_prediction_example.ipynb': 'examples/notebooks/live_prediction_example.ipynb',
        '../feature_engineering_example.ipynb': 'examples/notebooks/feature_engineering_example.ipynb',
        '../comprehensive_integration_example.ipynb': 'examples/notebooks/comprehensive_integration_example.ipynb',
        '../race_horse_targeted_example.ipynb': 'examples/notebooks/race_horse_targeted_example.ipynb',

        # ドキュメント
        '../LIVE_RACE_PREDICTOR_README.md': 'docs/PREDICTION_GUIDE.md',
        '../FEATURE_ENGINEERING_README.md': 'docs/FEATURE_ENGINEERING_GUIDE.md',
        '../COMPREHENSIVE_INTEGRATION_README.md': 'docs/INTEGRATION_GUIDE.md',
    }

    # ディレクトリを作成
    directories = [
        'keiba_ai_system/core/processors',
        'keiba_ai_system/core/scrapers',
        'keiba_ai_system/core/features',
        'keiba_ai_system/core/utils',
        'keiba_ai_system/prediction',
        'keiba_ai_system/prediction/models',
        'keiba_ai_system/examples/notebooks',
        'keiba_ai_system/examples/scripts',
        'keiba_ai_system/examples/configs',
        'keiba_ai_system/tests',
        'keiba_ai_system/docs'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"ディレクトリを作成: {directory}")

    # ファイルをコピー
    for src, dst in file_mappings.items():
        if os.path.exists(src):
            # ディレクトリを作成
            Path(dst).parent.mkdir(parents=True, exist_ok=True)

            # ファイルをコピー
            shutil.copy2(src, dst)
            print(f"コピー完了: {src} -> {dst}")

            # インポート文を修正
            fix_imports(dst)
        else:
            print(f"ファイルが見つかりません: {src}")

def fix_imports(file_path: str):
    """インポート文を新しい構造に合わせて修正"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # インポート文の置換
        import_replacements = {
            'from module.': 'from keiba_ai_system.core.',
            'import module.': 'import keiba_ai_system.core.',
            'from live_race_predictor': 'from keiba_ai_system.prediction.live_predictor',
            'from race_predictor': 'from keiba_ai_system.prediction.race_predictor',
            'from feature_engineering_manager': 'from keiba_ai_system.core.features.manager',
            'from feature_definitions': 'from keiba_ai_system.core.features.definitions',
            'from feature_calculators': 'from keiba_ai_system.core.features.calculators',
        }

        for old_import, new_import in import_replacements.items():
            content = content.replace(old_import, new_import)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"インポート修正完了: {file_path}")

    except Exception as e:
        print(f"インポート修正エラー ({file_path}): {e}")

if __name__ == "__main__":
    copy_files()
    print("ファイルコピー完了")
