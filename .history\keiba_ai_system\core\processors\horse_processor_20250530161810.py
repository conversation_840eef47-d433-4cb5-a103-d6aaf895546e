import io
import logging
import os
import re
from abc import ABCMeta, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed # type: ignore
from typing import Any, Dict, List, Optional
# from keiba_ai_system.core.horse_html_parser import HorseHtmlParser
import numpy as np
import pandas as pd
from bs4 import BeautifulSoup
import time # インデックス構築の時間計測用
from tqdm.auto import tqdm

from keiba_ai_system.core.constants import HorseInfoCols, HorseResultsCols, LocalPaths


class AbstractDataProcessor(metaclass=ABCMeta):
    """
    データ処理の抽象クラス
    """
    def __init__(self, filepath: Optional[str] = None, raw_data: Optional[pd.DataFrame] = None):
        self.logger = logging.getLogger(__name__) # ロガーを初期化
        try:
            if filepath is not None:
                self.__raw_data = pd.read_pickle(filepath)
            elif isinstance(raw_data, pd.DataFrame): # raw_dataがDataFrameの場合のみ代入
                self.__raw_data = raw_data
            else:
                self.__raw_data = pd.DataFrame()
        except Exception as e:
            self.logger.error(f"データ読み込みエラー: {e}", exc_info=True)
            self.__raw_data = pd.DataFrame()
        self.__preprocessed_data = self._preprocess()

    @abstractmethod
    def _preprocess(self):
        pass

    @property
    def raw_data(self):
        if isinstance(self.__raw_data, pd.DataFrame):
            return self.__raw_data.copy()
        else:
            return pd.DataFrame() # 空のDataFrameを返す

    @property
    def preprocessed_data(self):
        if isinstance(self.__preprocessed_data, pd.DataFrame):
            return self.__preprocessed_data.copy()
        else:
            return pd.DataFrame() # 空のDataFrameを返す


class HorseProcessor(AbstractDataProcessor):
    """
    馬の基本情報と過去成績を統合して処理するクラス
    HorseInfoProcessorとHorseResultsProcessorの機能を統合
    """

    def __init__(self, horse_results_filepath: Optional[str] = None, horse_info_filepath: Optional[str] = None,
                 horse_results_data: Optional[pd.DataFrame] = None, horse_info_data: Optional[pd.DataFrame] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        horse_results_filepath : str, optional
            馬の過去成績データのファイルパス
        horse_info_filepath : str, optional
            馬の基本情報データのファイルパス
        horse_results_data : pd.DataFrame, optional
            馬の過去成績データ
        horse_info_data : pd.DataFrame, optional
            馬の基本情報データ
        config : Dict[str, Any], optional
            処理の設定情報
        """
        self._config = config or {}
        self._horse_info_filepath = horse_info_filepath
        self.logger = logging.getLogger(__name__) # ロガーを初期化

        # 馬の基本情報データを読み込む
        try:
            if horse_info_filepath is not None:
                self._horse_info_data = pd.read_pickle(horse_info_filepath) # type: ignore
            elif isinstance(horse_info_data, pd.DataFrame):
                self._horse_info_data = horse_info_data
            else:
                self._horse_info_data = pd.DataFrame()
        except Exception as e:
            self.logger.error(f"馬の基本情報データ読み込みエラー: {e}")
            self._horse_info_data = pd.DataFrame()

        self._horse_html_index: Dict[str, str] = {}
        self._build_horse_html_index()

        # HTMLパーサーのインスタンスを作成
        self._html_parser = HorseHtmlParser()

        # 親クラスのinitを呼び出す（この中で_preprocessが呼ばれる）
        super().__init__(horse_results_filepath, horse_results_data)

    def _build_horse_html_index(self):
        """
        馬のHTMLファイルのインデックスを構築する。
        馬IDをキー、ファイルパスを値とする辞書を作成し、self._horse_html_indexに格納する。
        """
        self.logger.info("馬HTMLファイルのインデックスを構築中...")
        start_time = time.time()
        # LocalPaths.HTML_HORSE_DIR 以下の .bin ファイルを再帰的に検索
        # 想定されるパス構造: data/html/horse/horse_by_year/YYYY/horse_id.bin
        # または data/html/horse/horse_id.bin (LocalPaths.HTML_HORSE_DIRの定義による)

        # LocalPaths.HTML_HORSE_DIR が "data/html/horse/horse_by_year" を指すことを前提とする。
        # このディレクトリのサブディレクトリ（年ごとのディレクトリ）内の .bin ファイルを探す。
        search_base_path = LocalPaths.HTML_HORSE_DIR
        if not os.path.isdir(search_base_path):
            self.logger.warning(f"馬HTMLディレクトリが見つかりません: {search_base_path}。インデックスは空になります。")
            return

        for dirpath, _, filenames in os.walk(search_base_path):
            for filename in filenames:
                if filename.endswith(".bin"):
                    horse_id = filename[:-4] # ".bin" を除去
                    file_path = os.path.join(dirpath, filename)
                    if horse_id not in self._horse_html_index:
                        self._horse_html_index[horse_id] = file_path
                    else:
                        # 稀にIDが重複するファイルが存在する場合の警告
                        self.logger.warning(f"重複する馬IDのファイルが見つかりました: {horse_id}。"
                                            f"既存パス: {self._horse_html_index[horse_id]}, 新規パス: {file_path}。既存パスを維持します。")

        end_time = time.time()
        self.logger.info(f"馬HTMLファイルのインデックス構築完了。{len(self._horse_html_index)}件 ({end_time - start_time:.2f}秒)")



    def _preprocess(self) -> pd.DataFrame:
        """
        馬データの前処理

        Returns
        -------
        pd.DataFrame
            前処理後のデータ
        """
        # _preprocess は主に馬の過去成績の前処理を担当するように変更
        try:
            current_raw_data = self.raw_data # プロパティ経由で取得
            if current_raw_data.empty:
                horse_results_df = pd.DataFrame()
            # raw_dataがDataFrameでない場合
            elif not isinstance(current_raw_data, pd.DataFrame): # 基本的にプロパティでDataFrameが保証されるはず
                self.logger.warning(f"raw_dataがDataFrameではありません: {type(current_raw_data)}。空のDataFrameを返します。")
                horse_results_df = pd.DataFrame()
            else:
                # 馬の過去成績の前処理
                horse_results_df = self._preprocess_horse_results(self.raw_data.copy())

                # 日付カラムの型変換と 'horse_id' カラムの追加
                if 'date' in horse_results_df.columns:
                    horse_results_df['date'] = pd.to_datetime(horse_results_df['date'], errors='coerce')
                elif '日付' in horse_results_df.columns: # '日付' カラムがあれば 'date' にリネームまたはコピー
                    horse_results_df['date'] = pd.to_datetime(horse_results_df['日付'], errors='coerce')
                    if '日付' != 'date': # 元の '日付' カラムが 'date' でないなら削除も検討
                        # df = df.drop(columns=['日付'])
                        pass

                # horse_idカラムを追加
                if horse_results_df.index.name == 'horse_id' or 'horse_id' in horse_results_df.index.names:
                    # インデックスが 'horse_id' またはマルチインデックスの一部である場合
                    if 'horse_id' not in horse_results_df.columns: # カラムにまだ無ければ
                        horse_results_df['horse_id'] = horse_results_df.index.get_level_values('horse_id') if isinstance(horse_results_df.index, pd.MultiIndex) else horse_results_df.index
                elif 'horse_id' not in horse_results_df.columns:
                    # インデックスがhorse_idでなく、カラムにもない場合は警告
                    self.logger.warning("horse_id がインデックスにもカラムにも見つかりません。")

            return horse_results_df

        except Exception as e:
            self.logger.error(f"前処理エラー: {e}", exc_info=True)
            return pd.DataFrame()

    def _preprocess_horse_results(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        馬の過去成績の前処理

        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム

        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """
        try:
            # 着順の前処理
            df = self._preprocess_rank(df)

            # 性齢を性と年齢に分ける
            if HorseResultsCols.SEX_AGE in df.columns:
                sex_age_series = df[HorseResultsCols.SEX_AGE].astype(str)
                # str.extract を使用して性別と年齢を抽出
                # 例: "牡2" -> extract_result[0] = "牡", extract_result[1] = "2"
                extract_result = sex_age_series.str.extract(r'^([^\d]+)(\d+)$', expand=True)
                if not extract_result.empty:
                    df["性"] = extract_result[0]
                    df["年齢"] = pd.to_numeric(extract_result[1], errors='coerce').astype('Int64')

                    # 性別が期待される文字でない場合はNAにするバリデーション
                    valid_sexes = ['牡', '牝', 'セ']
                    if "性" in df.columns: # "性" カラムが実際に作成されたか確認
                        df.loc[~df["性"].isin(valid_sexes), "性"] = pd.NA
                else:
                    df["性"] = pd.NA
                    df["年齢"] = pd.NA


            # 馬体重を体重と体重変化に分ける
            if HorseResultsCols.WEIGHT_AND_DIFF in df.columns:
                # (xxx) の形式を想定し、xxx と ()内の数値を抽出
                # 例: "450(-2)" -> 体重: 450, 体重変化: -2
                # 例: "計不" -> 体重: NaN, 体重変化: NaN
                df[HorseResultsCols.WEIGHT_AND_DIFF] = df[HorseResultsCols.WEIGHT_AND_DIFF].astype(str)
                weight_split = df[HorseResultsCols.WEIGHT_AND_DIFF].str.extract(r'(\d+)(?:\(([\+\-]?\d+)\))?')
                if not weight_split.empty:
                    df["体重"] = pd.to_numeric(weight_split.iloc[:, 0], errors='coerce')
                    if weight_split.shape[1] > 1:
                        df["体重変化"] = pd.to_numeric(weight_split.iloc[:, 1], errors='coerce')
                    else:
                        df["体重変化"] = pd.NA
                else:
                    df["体重"] = pd.NA
                    df["体重変化"] = pd.NA

            # 賞金の前処理
            if HorseResultsCols.PRIZE in df.columns:
                df[HorseResultsCols.PRIZE] = df[HorseResultsCols.PRIZE].fillna(0).astype(int)

            # 各列を数値型に変換
            for col in [HorseResultsCols.RANK, HorseResultsCols.WAKUBAN, HorseResultsCols.UMABAN, HorseResultsCols.POPULARITY, HorseResultsCols.TANSHO_ODDS, HorseResultsCols.KINRYO]:
                if col in df.columns: # .value を削除
                    # .astypeで適切な型に変換 (Int64/Float64は欠損値NaNを許容する)
                    if col in [HorseResultsCols.RANK, HorseResultsCols.WAKUBAN, HorseResultsCols.UMABAN, HorseResultsCols.POPULARITY]:
                        df[col] = pd.to_numeric(df[col], errors='coerce').astype('Int64') # .value を削除
                    else: # TANSHO_ODDS, KINRYO など
                        df[col] = pd.to_numeric(df[col], errors='coerce').astype('Float64') # .value を削除

            return df
        except Exception as e:
            self.logger.error(f"馬の過去成績前処理エラー: {e}", exc_info=True)
            return df # エラーが発生しても元のDataFrameを返す

    def _preprocess_horse_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        馬の基本情報の前処理
        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム
        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """
        try:
            # 馬の基本情報の前処理
            if 'birthday' in df.columns:
                if HorseInfoCols.BIRTHDAY not in df.columns:
                    df = df.rename(columns={'birthday': HorseInfoCols.BIRTHDAY})
                # 'birthday' と '生年月日' が両方存在する場合の処理
                elif HorseInfoCols.BIRTHDAY in df.columns:
                    # HorseInfoCols.BIRTHDAY が優先されるため、'birthday' カラムは削除
                    df = df.drop(columns=['birthday'], errors='ignore')

            # インデックス名を与える
            if df.index.name is None:
                df.index.name = 'horse_id'

            # --- ここから追加: pedigreeカラムが消えないようにする ---
            # 必要なカラムリストを拡張（存在するもののみ抽出）
            pedigree_cols = [
                'FATHER_NAME', 'FATHER_ID', 'MOTHER_NAME', 'MOTHER_ID', 'MOTHER_FATHER_NAME'
            ]
            # 既存のカラムを保持
            base_cols = list(df.columns)
            # pedigreeカラムがあれば必ず残す
            for col in pedigree_cols:
                if col in df.columns and col not in base_cols:
                    base_cols.append(col)
            df = df[base_cols]
            # --- ここまで追加 ---

            return df
        except Exception as e:
            self.logger.error(f"馬の基本情報前処理エラー: {e}", exc_info=True)
            return df

    def _preprocess_rank(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        着順の前処理

        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム

        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """
        if HorseResultsCols.RANK in df.columns: # .value を削除
            # 着順を数値に変換。変換できない場合はNaNとする。
            df[HorseResultsCols.RANK] = pd.to_numeric(df[HorseResultsCols.RANK], errors='coerce') # .value を削除

            # NaNの行を削除（または適切に処理）
            df.dropna(subset=[HorseResultsCols.RANK], inplace=True) # .value を削除

            # 着順が数値であることを確認し、整数に変換
            df[HorseResultsCols.RANK] = df[HorseResultsCols.RANK].astype('Int64') # Int64で欠損許容 # .value を削除

            # 3着以内を1、それ以外を0とする
            df['rank_binary'] = np.where(df[HorseResultsCols.RANK].isin([1, 2, 3]), 1, 0) # .value を削除 (より直接的)

        return df

    def filter_by_date(self, target_date, horse_id_list=None):
        """
        指定した日付より前の馬の過去成績を取得

        Parameters
        ----------
        target_date : datetime-like
            基準となる日付
        horse_id_list : List[str], optional
            対象の馬IDリスト。Noneの場合は全ての馬を対象とする

        Returns
        -------
        pd.DataFrame
            フィルタリングされた馬の過去成績
        """
        df = self.preprocessed_data # プロパティ経由で取得

        # preprocessed_dataが空またはNoneの場合
        if df.empty:
            return pd.DataFrame()

        # 日付でフィルタリング
        if 'date' in df.columns:
            filtered_df = df[df['date'] < target_date]
        else:
            filtered_df = df

        # 馬IDでフィルタリング
        if horse_id_list is not None and not filtered_df.empty:
            if hasattr(filtered_df.index, 'get_level_values'):
                filtered_df = filtered_df[filtered_df.index.get_level_values(0).isin(horse_id_list)]
            else:
                filtered_df = filtered_df[filtered_df.index.isin(horse_id_list)]

        return filtered_df

    def get_latest_results(self, n_races=5, horse_id_list=None):
        """
        各馬の直近n_races分のレース結果を取得

        Parameters
        ----------
        n_races : int, optional
            取得するレース数
        horse_id_list : List[str], optional
            対象の馬IDリスト。Noneの場合は全ての馬を対象とする

        Returns
        -------
        pd.DataFrame
            各馬の直近n_races分のレース結果
        """
        df = self.preprocessed_data # プロパティ経由で取得

        # 馬IDでフィルタリング
        if horse_id_list is not None:
            df = df[df.index.get_level_values(0).isin(horse_id_list)]

        # 各馬ごとに直近n_races分のレース結果を取得
        latest_results = df.sort_values('date', ascending=False).groupby(level=0).head(n_races)

        return latest_results

    def summarize_horse_results(self, target_cols, n_races=None, group_cols=None):
        """
        馬の過去成績を集計

        Parameters
        ----------
        target_cols : List[str]
            集計対象の列
        n_races : int, optional
            集計対象のレース数。Noneの場合は全てのレースを対象とする
        group_cols : List[str], optional
            グループ化する列。Noneの場合はhorse_idのみでグループ化

        Returns
        -------
        pd.DataFrame
            集計結果
        """
        df = self.preprocessed_data # プロパティ経由で取得

        # 直近n_races分に絞る
        if n_races is not None:
            df = df.sort_values('date', ascending=False).groupby(level=0).head(n_races)

        # グループ化して集計
        if group_cols is None:
            # horse_idのみでグループ化
            summarized = df.groupby(level=0)[target_cols].mean()
        else:
            # horse_idとgroup_colsでグループ化
            summarized = df.groupby(['horse_id'] + group_cols)[target_cols].mean()

        return summarized

    def get_horse_info(self, horse_id_list=None):
        """
        馬の基本情報を取得

        Parameters
        ----------
        horse_id_list : List[str], optional
            対象の馬IDリスト。Noneの場合は全ての馬を対象とする

        Returns
        -------
        pd.DataFrame
            馬の基本情報
        """
        # 馬の基本情報を取得し、前処理を行う
        if not isinstance(self._horse_info_data, pd.DataFrame) or self._horse_info_data.empty:
            processed_horse_info = pd.DataFrame()
        else:
            processed_horse_info = self._preprocess_horse_info(self._horse_info_data.copy())

        # 馬IDでフィルタリング
        if horse_id_list is not None and not processed_horse_info.empty:
            processed_horse_info = processed_horse_info[processed_horse_info.index.isin(horse_id_list)]

        return processed_horse_info

    def get_rawdata_horse_info(self, horse_id_list: Optional[List[str]] = None,
                               html_path_list: Optional[List[str]] = None,
                               max_workers: Optional[int] = None) -> pd.DataFrame:
        """
        馬の基本情報をHTMLファイルから取得し、DataFrameに変換する関数。
        horse_id_list が指定された場合は、それに対応するHTMLファイルを探して処理します。

        Parameters
        ----------
        horse_id_list : list, optional
            馬IDのリスト。指定された場合、対応するHTMLファイルを探して処理。
        html_path_list : list, optional
            馬の基本情報のHTMLファイルパスのリスト。
            horse_id_list と html_path_list の両方が指定された場合は html_path_list を優先。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。

        Returns
        -------
        pd.DataFrame
            馬の基本情報のDataFrame
        """
        self.logger.info('馬の基本情報テーブルを準備中...')

        if html_path_list is None and horse_id_list is not None:
            self.logger.info(f"{len(horse_id_list)}頭の馬IDに対応するHTMLファイルを探します。")
            html_path_list = []
            for horse_id in tqdm(horse_id_list, desc="馬IDからHTMLファイル検索", leave=False):
                path = self._find_horse_info_html_by_id(horse_id)
                if path and os.path.exists(path):
                    html_path_list.append(path)
                else:
                    self.logger.debug(f"馬ID {horse_id} のHTMLファイルが見つかりません。")
            self.logger.info(f"処理対象のHTMLファイル数: {len(html_path_list)}")

        if not html_path_list:
            self.logger.warning("処理対象のHTMLファイルがありません")
            return pd.DataFrame()

        # 並列処理のワーカー数を決定
        num_workers = max_workers if max_workers is not None else (os.cpu_count() or 1)

        info_list = []
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = {executor.submit(self._html_parser.parse_horse_info_html, path): path for path in html_path_list}
            for future in tqdm(as_completed(futures), total=len(html_path_list), desc="馬基本情報HTMLパース(並列)"):
                path_completed = futures[future]
                try:
                    result_df = future.result()
                    if result_df is not None and not result_df.empty:
                        info_list.append(result_df)
                except Exception as e:
                    self.logger.error(f"ファイル {path_completed} のパース中にエラー: {e}", exc_info=True)

        if not info_list:
            self.logger.info("処理対象の馬基本情報データがありませんでした。")
            return pd.DataFrame()

        horse_info_df = pd.concat(info_list)
        self.logger.info(f"馬基本情報HTMLパース完了。合計 {len(horse_info_df)}件")

        return self._preprocess_horse_info(horse_info_df) # 直接前処理メソッドを呼ぶ

    def get_rawdata_horse_results(self, html_path_list: Optional[list] = None, year: Optional[int] = None,
                                 max_files: Optional[int] = None, max_workers: Optional[int] = None):
        """
        horseページのhtmlを受け取って、馬の過去成績のDataFrameに変換する関数。

        Parameters
        ----------
        html_path_list : list, optional
            馬の過去成績のHTMLファイルパスのリスト。Noneの場合はyearから自動取得
        year : int, optional
            対象年度。html_path_listがNoneの場合に使用
        max_files : int, optional
            処理するファイルの最大数。Noneの場合は全ファイルを処理
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。

        Returns
        -------
        pd.DataFrame
            馬の過去成績のDataFrame
        """
        self.logger.info('馬の過去成績テーブルを準備中...')

        # html_path_listがNoneの場合、yearから自動取得
        if html_path_list is None:
            if year is not None:
                html_path_list = self._find_horse_html_files_by_year(year)
            else:
                self.logger.error("html_path_listとyearの両方がNoneです")
                return pd.DataFrame()

        if max_files is not None and html_path_list:
            html_path_list = html_path_list[:max_files]
            self.logger.info(f'{len(html_path_list)} 件のファイルを処理します (max_files={max_files} により制限)。')

        if not html_path_list:
            self.logger.warning("処理対象のHTMLファイルがありません")
            return pd.DataFrame()

        # 並列処理のワーカー数を決定
        num_workers = max_workers if max_workers is not None else (os.cpu_count() or 1)

        results_list = []
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures_results = {executor.submit(self._html_parser.parse_horse_results_html, path): path for path in html_path_list}
            for future in tqdm(as_completed(futures_results), total=len(html_path_list), desc="馬過去成績HTMLパース(並列)"):
                 result_df = future.result()
                 if result_df is not None and not result_df.empty:
                    results_list.append(result_df)

        if not results_list:
            self.logger.info("処理対象の過去成績データがありませんでした。")
            return pd.DataFrame()

        horse_results_df = pd.concat(results_list)
        self.logger.info(f"馬過去成績HTMLパース完了。合計 {len(horse_results_df)}件")

        return self._preprocess_horse_results(horse_results_df) # 直接前処理メソッドを呼ぶ

    @staticmethod
    def update_rawdata(filepath: str, new_df: pd.DataFrame, logger_instance: Optional[logging.Logger] = None) -> pd.DataFrame:
        """
        filepathにrawテーブルのpickleファイルパスを指定し、new_dfに追加したいDataFrameを指定。
        元々のテーブルにnew_dfが追加されてpickleファイルが更新される。
        pickleファイルが存在しない場合は、filepathに新たに作成される。

        Parameters
        ----------
        filepath : str
            更新するpickleファイルのパス
        new_df : pd.DataFrame
            追加するデータ
        logger_instance : logging.Logger, optional
            ロギングに使用するロガーインスタンス。Noneの場合は標準のロガーを使用。

        Returns
        -------
        pd.DataFrame
            更新後のデータ
        """
        # pickleファイルが存在する場合の更新処理
        logger = logger_instance or logging.getLogger(__name__)

        if os.path.isfile(filepath):
            backupfilepath = filepath + '.bak'
            # 結合データがない場合
            if new_df.empty:
                logger.info('更新するデータが空です。既存のデータを返します。')
                return pd.read_pickle(filepath)
            else:
                logger.info(f"{filepath} を更新します。")
                # 元々のテーブルを読み込み
                filedf = pd.read_pickle(filepath)
                # new_dfに存在しないindexのみ、旧データを使う
                filtered_old = filedf[~filedf.index.isin(new_df.index)]
                # bakファイルが存在する場合
                if os.path.isfile(backupfilepath):
                    os.remove(backupfilepath)
                # バックアップ
                os.rename(filepath, backupfilepath)
                # 結合
                updated = pd.concat([filtered_old, new_df])
                # 保存
                updated.to_pickle(filepath)
                return updated
        else:
            # pickleファイルが存在しない場合、新たに作成
            logger.info(f"{filepath} が存在しないため、新規作成します。")
            new_df.to_pickle(filepath)
            return new_df

    def _find_horse_html_files_by_year(self, year: int) -> list:
        """指定された年度の馬HTMLファイルパスのリストを取得する"""
        from constants import LocalPaths # constants.py からインポート
        import glob

        # 実際のファイル構造: data/html/horse/horse_by_year/2022/2022xxxxxx.bin
        pattern1 = os.path.join(LocalPaths.HTML_HORSE_DIR, "horse_by_year", str(year), "*.bin")
        html_path_list = glob.glob(pattern1)

        if not html_path_list:
            # 別のパターンも試す（フラット構造の場合）
            pattern2 = os.path.join(LocalPaths.HTML_HORSE_DIR, f"*{year}*.bin")
            html_path_list = glob.glob(pattern2)

        if not html_path_list:
            # さらに別のパターン（全ファイルから年度フィルタ）
            pattern3 = os.path.join(LocalPaths.HTML_HORSE_DIR, "**", "*.bin") # サブディレクトリも検索
            all_files = glob.glob(pattern3, recursive=True)
            # ファイル名に年度が含まれるものをフィルタ
            html_path_list = [f for f in all_files if str(year) in os.path.basename(f)]

        self.logger.info(f'{year}年の馬HTMLファイルを{len(html_path_list)}件発見 (パターン1: {pattern1}, パターン2: {pattern2}, パターン3: {pattern3})')
        return html_path_list

    def _find_all_horse_html_files(self) -> list:
        """全ての馬HTMLファイルパスのリストを取得する"""
        from constants import LocalPaths # constants.py からインポート
        import glob

        pattern = os.path.join(LocalPaths.HTML_HORSE_DIR, "**", "*.bin") # サブディレクトリも検索
        html_path_list = glob.glob(pattern, recursive=True)
        self.logger.info(f'全年度の馬HTMLファイルを{len(html_path_list)}件発見 (パターン: {pattern})')
        return html_path_list

    def get_all_horse_results(self, max_files: Optional[int] = None,
                             max_workers: Optional[int] = None,
                             year_range: Optional[tuple] = None) -> pd.DataFrame:
        """
        年代関係なく全ての馬過去成績データを取得する関数

        Parameters
        ----------
        max_files : int, optional
            処理するファイルの最大数。Noneの場合は全ファイルを処理
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        year_range : tuple, optional
            年度範囲を指定 (start_year, end_year)。Noneの場合は全年度

        Returns
        -------
        pd.DataFrame
            全馬の過去成績のDataFrame
        """
        try:
            self.logger.info('全馬過去成績データを収集中...')
            html_path_list = []
            if year_range is not None:
                start_year, end_year = int(year_range[0]), int(year_range[1]) # 年を整数に
                for year in tqdm(range(start_year, end_year + 1), desc="年度別HTMLファイル収集"):
                    html_path_list.extend(self._find_horse_html_files_by_year(year))
                self.logger.info(f'{start_year}-{end_year}年の馬HTMLファイルを合計{len(html_path_list)}件発見')
            else:
                html_path_list = self._find_all_horse_html_files()


            if not html_path_list:
                self.logger.warning("処理対象のHTMLファイルがありません")
                return pd.DataFrame()

            # max_filesの制限を適用
            if max_files is not None and len(html_path_list) > max_files:
                html_path_list = html_path_list[:max_files]
                self.logger.info(f'{len(html_path_list)} 件のファイルを処理します (max_files={max_files} により制限)。')

            # 既存のget_rawdata_horse_resultsメソッドを使用
            return self.get_rawdata_horse_results(
                html_path_list=html_path_list,
                max_files=None,  # 既にhtml_path_listで制限済み
                max_workers=max_workers
            )

        except Exception as e:
            self.logger.error(f"get_all_horse_results実行エラー: {e}")
            return pd.DataFrame()

    def get_horse_results_by_years(self, years: list, max_files_per_year: Optional[int] = None,
                                  max_workers: Optional[int] = None) -> pd.DataFrame:
        """
        指定された複数年度の馬過去成績データを取得する関数

        Parameters
        ----------
        years : list
            対象年度のリスト (例: [2020, 2021, 2022])
        max_files_per_year : int, optional
            年度あたりの最大ファイル数。Noneの場合は全ファイルを処理
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。

        Returns
        -------
        pd.DataFrame
            指定年度の馬過去成績のDataFrame
        """
        try:
            all_results = []

            for year_int in tqdm(years, desc="指定年度別 馬過去成績処理"): # years は int のリストを期待
                year = int(year_int) # 念のためintに変換
                self.logger.info(f'{year}年の馬過去成績データを処理中...')
                html_paths_for_year = self._find_horse_html_files_by_year(year)
                year_results = self.get_rawdata_horse_results( # html_path_list を渡す
                    html_path_list=html_paths_for_year,
                    max_files=max_files_per_year,
                    max_workers=max_workers
                )

                if not year_results.empty:
                    # 年度情報を追加
                    year_results['data_year'] = year
                    all_results.append(year_results)
                    self.logger.info(f'{year}年: {len(year_results):,}件のデータを取得')
                else:
                    self.logger.warning(f'{year}年: データが見つかりませんでした')

            # 全年度のデータを結合
            if all_results:
                combined_results = pd.concat(all_results, ignore_index=True)
                self.logger.info(f'全年度結合完了: {len(combined_results):,}件')
                return combined_results
            else:
                self.logger.warning("指定年度のデータが見つかりませんでした")
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"get_horse_results_by_years実行エラー: {e}")
            return pd.DataFrame()

    def process_horse_info_for_ids(self, horse_ids: List[str],
                                   parallel: bool = True,
                                   max_workers: Optional[int] = None) -> pd.DataFrame:
        """
        指定された馬IDリストの馬基本情報を処理

        Parameters
        ----------
        horse_ids : List[str]
            処理対象の馬IDリスト
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, optional
            並列処理の最大ワーカー数

        Returns
        -------
        pd.DataFrame
            馬基本情報のDataFrame
        """
        if not horse_ids:
            self.logger.warning("処理対象の馬IDが空です")
            return pd.DataFrame()

        self.logger.info(f"{len(horse_ids)}頭の馬基本情報を処理中...")

        results_list: List[pd.DataFrame] = []
        num_workers = max_workers if parallel and max_workers is not None else (os.cpu_count() or 1)

        # HTMLパスを事前にまとめて取得
        paths_for_processing: List[str] = []
        # path_to_id_map: Dict[str, str] = {} # デバッグや詳細なエラー追跡に必要なら使用
        self.logger.info(f"馬基本情報HTMLファイルのインデックスを利用してパスを特定中...")
        for horse_id in tqdm(horse_ids, desc="馬基本情報HTMLファイル特定(インデックス利用)", leave=False):
            html_path = self._find_horse_info_html_by_id(horse_id) # インデックス利用
            if html_path and os.path.exists(html_path):
                paths_for_processing.append(html_path)
                # path_to_id_map[html_path] = horse_id
            else:
                self.logger.debug(f"馬ID {horse_id} のHTMLファイルが見つかりません (インデックス利用)")

        if not paths_for_processing:
            self.logger.warning("処理可能な馬基本情報HTMLファイルがありませんでした。")
            return pd.DataFrame()

        self.logger.info(f"{len(paths_for_processing)}個のHTMLファイルを処理 (馬基本情報)")

        with ThreadPoolExecutor(max_workers=num_workers if parallel else 1) as executor:
            # _process_single_horse_info を呼び出す代わりに、直接 _html_parser.parse_horse_info_html を呼び出す
            # これにより、ファイル検索の重複を避ける
            future_to_path = {
                executor.submit(self._html_parser.parse_horse_info_html, path): path
                for path in paths_for_processing
            }
            for future in tqdm(as_completed(future_to_path), total=len(paths_for_processing), desc="馬基本情報処理(並列)", leave=False):
                path_completed = future_to_path[future]
                # horse_id_completed = path_to_id_map[path_completed] # 必要なら使用
                try:
                    result_df = future.result()
                    if result_df is not None and not result_df.empty:
                        results_list.append(result_df)
                except Exception as e:
                    self.logger.error(f"HTMLファイル {path_completed} の基本情報処理中にエラー: {e}", exc_info=True)

        if not results_list:
            self.logger.warning("処理可能な馬基本情報データがありませんでした。")
            return pd.DataFrame()
        horse_info_df = pd.concat(results_list)
        self.logger.info(f"馬基本情報処理完了。合計 {len(horse_info_df)}件")
        return self._preprocess_horse_info(horse_info_df)

    def _process_single_horse_info(self, horse_id: str) -> Optional[pd.DataFrame]:
        """単一の馬IDの基本情報を処理するヘルパー関数"""
        html_path = self._find_horse_info_html_by_id(horse_id)
        if html_path: # _find_horse_info_html_by_id が os.path.exists をチェック済み
            return self._html_parser.parse_horse_info_html(html_path)
        else:
            self.logger.debug(f"馬ID {horse_id} のHTMLファイルが見つかりません")
            return None

    def process_horse_results_for_ids(self,
                                    horse_ids: List[str],
                                    parallel: bool = True,
                                    max_workers: Optional[int] = None) -> pd.DataFrame:
        """
        指定された馬IDリストの馬過去成績を処理

        Parameters
        ----------
        horse_ids : List[str]
            処理対象の馬IDリスト
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, optional
            並列処理の最大ワーカー数

        Returns
        -------
        pd.DataFrame
            馬過去成績のDataFrame
        """
        if not horse_ids:
            self.logger.warning("処理対象の馬IDが空です")
            return pd.DataFrame()

        self.logger.info(f"{len(horse_ids)}頭の馬過去成績を処理中...")

        # 馬IDに対応するHTMLファイルパスを特定
        self.logger.info(f"馬過去成績HTMLファイルのインデックスを利用してパスを特定中...")
        html_paths = []
        for horse_id_val in tqdm(horse_ids, desc="馬過去成績HTMLファイル特定(インデックス利用)", leave=False):
            html_path = self._find_horse_info_html_by_id(horse_id_val) # インデックス利用
            if html_path: # _find_horse_info_html_by_id が os.path.exists をチェック済み
                html_paths.append(html_path)
            else:
                self.logger.debug(f"馬ID {horse_id_val} のHTMLファイルが見つかりません (インデックス利用)")

        if not html_paths:
            self.logger.warning("処理可能なHTMLファイルがありません")
            return pd.DataFrame()

        self.logger.info(f"{len(html_paths)}個のHTMLファイルを処理")

        # 馬過去成績を取得
        horse_results_df = self.get_rawdata_horse_results(
            html_path_list=html_paths,
            max_workers=max_workers if parallel else 1
        ) # get_rawdata_horse_results が前処理まで行う

        return horse_results_df

    def _process_single_horse_results(self, horse_id: str) -> Optional[pd.DataFrame]:
        """単一の馬IDの過去成績を処理するヘルパー関数"""
        html_path = self._find_horse_info_html_by_id(horse_id)
        if html_path: # _find_horse_info_html_by_id が os.path.exists をチェック済み
            return self._html_parser.parse_horse_results_html(html_path)
        else:
            self.logger.debug(f"馬ID {horse_id} のHTMLファイルが見つかりません")
            return None

    def _find_horse_info_html_by_id(self, horse_id: str) -> Optional[str]:
        """
        馬IDから馬基本情報のHTMLファイルパスを特定

        Parameters
        ----------
        horse_id : str
            馬ID

        Returns
        -------
        str or None
            HTMLファイルパス
        """
        # インデックスから直接検索
        path = self._horse_html_index.get(horse_id)
        if path and os.path.exists(path):
            return path
        # インデックスにない場合はNoneを返す (フォールバック検索はインデックス構築時にカバーされている想定)
        return None

    def get_horse_results_summary(self, max_sample_files: int = 100) -> dict:
        """
        馬過去成績データの概要情報を取得する関数

        Parameters
        ----------
        max_sample_files : int, optional
            サンプリングするファイル数。デフォルトは100

        Returns
        -------
        dict
            データ概要情報
        """
        try:
            # 全HTMLファイルを検索
            all_files = self._find_all_horse_html_files()

            # ファイルパスから年度を抽出するヘルパー関数
            def get_year_from_path(file_path):
                # 親ディレクトリ名から年度を抽出 (例: .../horse_by_year/2022/...)
                # またはファイル名から抽出
                parts = file_path.split(os.sep)
                # 後ろから2番目の要素が年であると期待 (例: .../horse_by_year/2022/file.bin)
                if len(parts) >= 2 and parts[-2].isdigit() and len(parts[-2]) == 4:
                    return parts[-2]

                # ファイル名から年を抽出 (例: 2022xxxx.bin)
                filename = os.path.basename(file_path)
                match = re.search(r"(\d{4})", filename)
                if match:
                    return match.group(1)
                return "Unknown"

            # 年度別ファイル数を集計
            year_counts = {}
            for file_path in all_files:
                year = get_year_from_path(file_path)
                year_counts[year] = year_counts.get(year, 0) + 1

            # サンプルデータを取得
            sample_files = all_files[:max_sample_files] if len(all_files) > max_sample_files else all_files

            # get_rawdata_horse_results は html_path_list を受け取るので、
            # サンプルファイルパスを渡して実行
            sample_data_frames = []
            if sample_files: # サンプルファイルがある場合のみ実行
                num_workers_sample = min(os.cpu_count() or 1, 4) # サンプル取得時のワーカー数を制限
                with ThreadPoolExecutor(max_workers=num_workers_sample) as executor:
                    future_to_path = {executor.submit(self._html_parser.parse_horse_results_html, path): path for path in sample_files}
                    for future in tqdm(as_completed(future_to_path), total=len(sample_files), desc="サンプルデータ取得", leave=False):
                        try:
                            df_res = future.result()
                            if df_res is not None and not df_res.empty:
                                sample_data_frames.append(df_res)
                        except Exception:
                            pass # エラーは無視して次に進む

            sample_data = pd.DataFrame()
            if sample_data_frames:
                sample_data = pd.concat(sample_data_frames)
                sample_data = self._preprocess_horse_results(sample_data) # 前処理も適用

            summary = {
                'total_files': len(all_files),
                'year_distribution': dict(sorted(year_counts.items())),
                'sample_files_processed': len(sample_files),
                'sample_records': len(sample_data) if not sample_data.empty else 0,
                'sample_columns': list(sample_data.columns) if not sample_data.empty else [],
                'sample_horse_count': sample_data['horse_id'].nunique() if not sample_data.empty and 'horse_id' in sample_data.columns else 0
            }

            self.logger.info(f"馬データ概要: 総ファイル数={summary['total_files']}, サンプル処理={summary['sample_files_processed']}")

            return summary

        except Exception as e:
            self.logger.error(f"get_horse_results_summary実行エラー: {e}", exc_info=True)
            return {}


