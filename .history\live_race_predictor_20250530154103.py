#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
実際の競馬レース予測システム

指定されたレースIDの出馬表をスクレイピングし、
出走馬の過去成績を分析して着順を予測します。
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.preprocessing import StandardScaler
import joblib
from datetime import datetime

# プロジェクトのモジュールをインポート
sys.path.append('.')
from module.refactored_scrap import scrape_html_race, scrape_html_horse
from module.race_data_processor import RaceProcessor
from module.horse_processor import HorseProcessor
from module.constants import LocalPaths, UrlPaths
from extract_horse_ids import extract_horse_ids_from_race_data

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('live_race_predictor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class LiveRacePredictor:
    """実際の競馬レース予測を行うクラス"""

    def __init__(self, model_path: str = None, scaler_path: str = None):
        """
        初期化

        Parameters
        ----------
        model_path : str, optional
            学習済みLightGBMモデルのパス
        scaler_path : str, optional
            学習済みStandardScalerのパス
        """
        self.race_processor = RaceProcessor()
        self.horse_processor = HorseProcessor()
        self.model = None
        self.scaler = None
        self.feature_columns = None

        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        if scaler_path and os.path.exists(scaler_path):
            self.load_scaler(scaler_path)

    def load_model(self, model_path: str):
        """学習済みモデルをロード"""
        try:
            self.model = joblib.load(model_path)
            logger.info(f"モデルをロードしました: {model_path}")
        except Exception as e:
            logger.error(f"モデルのロードに失敗しました: {e}")
            raise

    def load_scaler(self, scaler_path: str):
        """学習済みスケーラーをロード"""
        try:
            self.scaler = joblib.load(scaler_path)
            logger.info(f"スケーラーをロードしました: {scaler_path}")
        except Exception as e:
            logger.error(f"スケーラーのロードに失敗しました: {e}")
            raise

    def scrape_race_data(self, race_id: str, skip_existing: bool = True) -> bool:
        """
        指定されたレースIDのデータをスクレイピング

        Parameters
        ----------
        race_id : str
            レースID
        skip_existing : bool, default True
            既存ファイルをスキップするか

        Returns
        -------
        bool
            スクレイピング成功の可否
        """
        try:
            logger.info(f"レースID {race_id} のスクレイピングを開始...")

            # レースHTMLをスクレイピング
            scrape_html_race(race_id_list=[race_id], skip=skip_existing)
            logger.info(f"レースID {race_id} のHTMLスクレイピング完了")

            return True

        except Exception as e:
            logger.error(f"レースID {race_id} のスクレイピング中にエラー: {e}")
            return False

    def scrape_horse_data(self, horse_ids: List[str], skip_existing: bool = True) -> bool:
        """
        指定された馬IDリストのデータをスクレイピング

        Parameters
        ----------
        horse_ids : List[str]
            馬IDのリスト
        skip_existing : bool, default True
            既存ファイルをスキップするか

        Returns
        -------
        bool
            スクレイピング成功の可否
        """
        try:
            logger.info(f"{len(horse_ids)}頭の馬データをスクレイピング中...")

            # 馬HTMLをスクレイピング
            scrape_html_horse(horse_id_list=horse_ids, skip=skip_existing)
            logger.info(f"馬データのスクレイピング完了")

            return True

        except Exception as e:
            logger.error(f"馬データのスクレイピング中にエラー: {e}")
            return False

    def process_race_data(self, race_id: str) -> Tuple[pd.DataFrame, pd.DataFrame, str]:
        """
        レースデータを処理してDataFrameに変換

        Parameters
        ----------
        race_id : str
            レースID

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame, str]
            レース情報、レース結果（出馬表）、レース名
        """
        try:
            # HTMLファイルパスを構築
            html_file_path = Path(LocalPaths.HTML_RACE_DIR) / f"{race_id}.html"

            if not html_file_path.exists():
                logger.error(f"HTMLファイルが見つかりません: {html_file_path}")
                return pd.DataFrame(), pd.DataFrame(), ""

            # レースHTMLをパース
            race_info_df, race_results_df = self.race_processor.parse_race_html(str(html_file_path))

            if race_results_df.empty:
                logger.error(f"レースID {race_id} の出馬表データが取得できませんでした")
                return pd.DataFrame(), pd.DataFrame(), ""

            # レース名を取得
            race_name = race_info_df['レース名'].iloc[0] if not race_info_df.empty and 'レース名' in race_info_df.columns else f"レース_{race_id}"

            logger.info(f"レース '{race_name}' のデータを処理しました（{len(race_results_df)}頭）")
            return race_info_df, race_results_df, race_name

        except Exception as e:
            logger.error(f"レースデータの処理中にエラー: {e}")
            return pd.DataFrame(), pd.DataFrame(), ""

    def extract_horse_ids(self, race_id: str) -> List[str]:
        """
        レースから出走馬のIDを抽出

        Parameters
        ----------
        race_id : str
            レースID

        Returns
        -------
        List[str]
            馬IDのリスト
        """
        try:
            # extract_horse_ids_from_race_data関数を使用
            horse_ids = extract_horse_ids_from_race_data(year=None, race_id=race_id)
            logger.info(f"レースID {race_id} から {len(horse_ids)}頭の馬IDを抽出")
            return horse_ids

        except Exception as e:
            logger.error(f"馬ID抽出中にエラー: {e}")
            return []

    def process_horse_data(self, horse_ids: List[str]) -> pd.DataFrame:
        """
        馬データを処理してDataFrameに変換

        Parameters
        ----------
        horse_ids : List[str]
            馬IDのリスト

        Returns
        -------
        pd.DataFrame
            馬の基本情報と過去成績を統合したDataFrame
        """
        try:
            if not horse_ids:
                logger.warning("処理する馬IDがありません")
                return pd.DataFrame()

            # 馬の基本情報を取得
            logger.info("馬の基本情報を処理中...")
            horse_info_df = self.horse_processor.get_rawdata_horse_info(
                horse_id_list=horse_ids,
                parallel=True,
                max_workers=4
            )

            # 馬の過去成績を取得
            logger.info("馬の過去成績を処理中...")
            horse_results_df = self.horse_processor.get_rawdata_horse_results(
                horse_id_list=horse_ids,
                parallel=True,
                max_workers=4,
                max_files=50  # 最大50レース分の過去成績
            )

            logger.info(f"馬データの処理完了: 基本情報 {len(horse_info_df)}件, 過去成績 {len(horse_results_df)}件")
            return horse_info_df, horse_results_df

        except Exception as e:
            logger.error(f"馬データの処理中にエラー: {e}")
            return pd.DataFrame(), pd.DataFrame()

    def create_features(self, race_info_df: pd.DataFrame, race_results_df: pd.DataFrame,
                       horse_info_df: pd.DataFrame, horse_results_df: pd.DataFrame) -> pd.DataFrame:
        """
        予測用の特徴量を作成

        Parameters
        ----------
        race_info_df : pd.DataFrame
            レース情報
        race_results_df : pd.DataFrame
            出馬表データ
        horse_info_df : pd.DataFrame
            馬の基本情報
        horse_results_df : pd.DataFrame
            馬の過去成績

        Returns
        -------
        pd.DataFrame
            特徴量DataFrame
        """
        try:
            logger.info("特徴量を作成中...")

            # 基本的な特徴量をrace_results_dfから取得
            features_df = race_results_df.copy()

            # レース情報を結合
            if not race_info_df.empty:
                race_features = ['距離', '開催', '芝・ダート', 'レース名']
                for col in race_features:
                    if col in race_info_df.columns:
                        features_df[col] = race_info_df[col].iloc[0]

            # 馬の基本情報を結合
            if not horse_info_df.empty and 'horse_id' in features_df.columns:
                features_df = features_df.merge(
                    horse_info_df[['horse_id', '生年月日', '父', '母', '母父']],
                    on='horse_id',
                    how='left'
                )

            # 過去成績から統計特徴量を作成
            if not horse_results_df.empty:
                features_df = self._add_performance_features(features_df, horse_results_df)

            logger.info(f"特徴量作成完了: {features_df.shape}")
            return features_df

        except Exception as e:
            logger.error(f"特徴量作成中にエラー: {e}")
            return pd.DataFrame()

    def _add_performance_features(self, features_df: pd.DataFrame, horse_results_df: pd.DataFrame) -> pd.DataFrame:
        """過去成績から統計特徴量を追加"""
        try:
            # 馬ごとの過去成績統計を計算
            performance_stats = horse_results_df.groupby('horse_id').agg({
                '着順': ['count', 'mean', lambda x: (x == 1).sum()],  # 出走回数、平均着順、勝利数
                '賞金': ['mean', 'max'],  # 平均賞金、最高賞金
                '人気': 'mean'  # 平均人気
            }).round(3)

            # カラム名を平坦化
            performance_stats.columns = [
                '出走回数', '平均着順', '勝利数', '平均賞金', '最高賞金', '平均人気'
            ]
            performance_stats = performance_stats.reset_index()

            # メインDataFrameに結合
            if 'horse_id' in features_df.columns:
                features_df = features_df.merge(performance_stats, on='horse_id', how='left')

            return features_df

        except Exception as e:
            logger.error(f"過去成績特徴量の追加中にエラー: {e}")
            return features_df

    def predict_race(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        レースの着順を予測

        Parameters
        ----------
        features_df : pd.DataFrame
            特徴量DataFrame

        Returns
        -------
        pd.DataFrame
            予測結果を含むDataFrame
        """
        try:
            if self.model is None:
                logger.error("モデルがロードされていません")
                return features_df

            logger.info("着順予測を実行中...")

            # 予測用の特徴量を準備
            prediction_features = self._prepare_prediction_features(features_df)

            if prediction_features.empty:
                logger.error("予測用特徴量の準備に失敗しました")
                return features_df

            # 予測実行
            if hasattr(self.model, 'predict_proba'):
                # 分類モデルの場合（1着確率を予測）
                probabilities = self.model.predict_proba(prediction_features)
                if probabilities.shape[1] > 1:
                    win_probabilities = probabilities[:, 1]  # 1着確率
                else:
                    win_probabilities = probabilities[:, 0]
                features_df['勝率予測'] = win_probabilities
            else:
                # 回帰モデルの場合（着順を直接予測）
                predicted_ranks = self.model.predict(prediction_features)
                features_df['着順予測'] = predicted_ranks
                # 着順から勝率に変換（簡易的）
                features_df['勝率予測'] = 1.0 / (predicted_ranks + 1)

            # 予測順位を計算
            features_df['予測順位'] = features_df['勝率予測'].rank(ascending=False, method='min').astype(int)

            logger.info("着順予測完了")
            return features_df

        except Exception as e:
            logger.error(f"予測実行中にエラー: {e}")
            return features_df

    def _prepare_prediction_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """予測用の特徴量を準備"""
        try:
            # 数値特徴量を選択
            numeric_columns = features_df.select_dtypes(include=[np.number]).columns.tolist()

            # 不要なカラムを除外
            exclude_columns = ['race_id', 'horse_id', '勝率予測', '着順予測', '予測順位']
            numeric_columns = [col for col in numeric_columns if col not in exclude_columns]

            prediction_df = features_df[numeric_columns].copy()

            # 欠損値を補完
            prediction_df = prediction_df.fillna(0)

            # スケーリング（スケーラーがロードされている場合）
            if self.scaler is not None:
                try:
                    prediction_df = pd.DataFrame(
                        self.scaler.transform(prediction_df),
                        columns=prediction_df.columns,
                        index=prediction_df.index
                    )
                except Exception as e:
                    logger.warning(f"スケーリングに失敗しました: {e}")

            return prediction_df

        except Exception as e:
            logger.error(f"予測用特徴量の準備中にエラー: {e}")
            return pd.DataFrame()

    def display_results(self, results_df: pd.DataFrame, race_name: str):
        """
        予測結果を表示

        Parameters
        ----------
        results_df : pd.DataFrame
            予測結果DataFrame
        race_name : str
            レース名
        """
        try:
            if results_df.empty:
                logger.warning("表示する結果がありません")
                return

            # 予測順位でソート
            if '予測順位' in results_df.columns:
                sorted_df = results_df.sort_values('予測順位').reset_index(drop=True)
            else:
                sorted_df = results_df.copy()

            print(f"\n{'='*60}")
            print(f"レース名: {race_name}")
            print(f"出走頭数: {len(sorted_df)}頭")
            print(f"{'='*60}")
            print(f"{'順位':<4} {'馬番':<4} {'馬名':<20} {'勝率':<8} {'人気':<4} {'騎手':<15}")
            print(f"{'-'*60}")

            for i, (_, row) in enumerate(sorted_df.iterrows()):
                rank = row.get('予測順位', i + 1)
                umaban = row.get('馬番', 'N/A')
                horse_name = row.get('馬名', 'N/A')
                win_prob = row.get('勝率予測', 0.0)
                popularity = row.get('人気', 'N/A')
                jockey = row.get('騎手', 'N/A')

                print(f"{rank:<4} {umaban:<4} {horse_name:<20} {win_prob:<8.3f} {popularity:<4} {jockey:<15}")

            print(f"{'='*60}")

        except Exception as e:
            logger.error(f"結果表示中にエラー: {e}")

    def save_results(self, results_df: pd.DataFrame, race_id: str, output_dir: str = "predictions"):
        """
        予測結果をCSVファイルに保存

        Parameters
        ----------
        results_df : pd.DataFrame
            予測結果DataFrame
        race_id : str
            レースID
        output_dir : str, default "predictions"
            出力ディレクトリ
        """
        try:
            # 出力ディレクトリを作成
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # ファイル名を生成
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prediction_{race_id}_{timestamp}.csv"
            filepath = output_path / filename

            # CSVに保存
            results_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"予測結果を保存しました: {filepath}")

        except Exception as e:
            logger.error(f"結果保存中にエラー: {e}")

    def predict_race_complete(self, race_id: str, skip_existing: bool = True,
                            save_results: bool = True) -> pd.DataFrame:
        """
        レース予測の完全な処理フロー

        Parameters
        ----------
        race_id : str
            レースID
        skip_existing : bool, default True
            既存ファイルをスキップするか
        save_results : bool, default True
            結果を保存するか

        Returns
        -------
        pd.DataFrame
            予測結果DataFrame
        """
        try:
            logger.info(f"レースID {race_id} の予測処理を開始...")

            # 1. レースデータをスクレイピング
            if not self.scrape_race_data(race_id, skip_existing):
                logger.error("レースデータのスクレイピングに失敗しました")
                return pd.DataFrame()

            # 2. レースデータを処理
            race_info_df, race_results_df, race_name = self.process_race_data(race_id)
            if race_results_df.empty:
                logger.error("レースデータの処理に失敗しました")
                return pd.DataFrame()

            # 3. 出走馬のIDを抽出
            horse_ids = self.extract_horse_ids(race_id)
            if not horse_ids:
                logger.error("馬IDの抽出に失敗しました")
                return pd.DataFrame()

            # 4. 馬データをスクレイピング
            if not self.scrape_horse_data(horse_ids, skip_existing):
                logger.warning("馬データのスクレイピングに一部失敗しました（続行）")

            # 5. 馬データを処理
            horse_info_df, horse_results_df = self.process_horse_data(horse_ids)

            # 6. 特徴量を作成
            features_df = self.create_features(race_info_df, race_results_df, horse_info_df, horse_results_df)
            if features_df.empty:
                logger.error("特徴量の作成に失敗しました")
                return pd.DataFrame()

            # 7. 予測実行
            results_df = self.predict_race(features_df)

            # 8. 結果表示
            self.display_results(results_df, race_name)

            # 9. 結果保存
            if save_results:
                self.save_results(results_df, race_id)

            logger.info(f"レースID {race_id} の予測処理完了")
            return results_df

        except Exception as e:
            logger.error(f"予測処理中にエラー: {e}")
            return pd.DataFrame()
