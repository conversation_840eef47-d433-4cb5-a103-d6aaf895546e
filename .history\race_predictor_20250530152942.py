#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬レース予測実行スクリプト

指定されたレースの情報をスクレイピング（または読み込み）、
学習済みTensorFlow Rankingモデルを使用して着順を予測します。
"""

import argparse
import logging
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import random # 追加
from bs4 import BeautifulSoup # 追加

import pandas as pd
import numpy as np
import tensorflow as tf
import tensorflow_ranking as tfr
from sklearn.preprocessing import StandardScaler
import joblib # For loading the scaler

from module.refactored_scrap import scrape_html_race
from module.constants import LocalPaths, UrlPaths
from module.race_data_processor import RaceProcessor # 追加

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 既存のTFranking_fixed.pyから持ってくる定数や関数 ---
JAPANESE_TO_ASCII_NAME_MAP = {
    '枠番': 'wakuban',
    '馬番': 'umaban',
    '性齢': 'seirei',
    '開催': 'kaisai',
    'jockey_id': 'jockey_id',
    'trainer_id': 'trainer_id',
    'owner_id': 'owner_id',
    'breeder_id': 'breeder_id',
    'father_id': 'father_id',
    'mother_father_id': 'mother_father_id',
}
# --- ここまで ---

def scrape_and_parse_race_data(race_identifier: str) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
    """
    指定されたレースIDまたはURLのレースデータをスクレイピングし、DataFrameに変換します。
    【注意】この関数はプレースホルダーであり、実際のスクレイピングロジックを実装する必要があります。
    
    Args:
        race_identifier (str): レースを特定するための情報（例: レースID、URL）。

    Returns:
        Tuple[Optional[pd.DataFrame], Optional[str]]: 
            処理済みの馬ごとの特徴量を含むDataFrame、またはエラー時はNone。
            レース名、またはエラー時はNone。
    """
    logger.info(f"{race_identifier} のレースデータをスクレイピング・解析中...")
    # ここに実際のスクレイピングと解析ロジックを実装します。
    # 例: netkeiba.com から特定のレースIDの出馬表と結果を取得する
    # スクレイピングしたデータは、学習時に使用した dataframe.csv と同様の形式（特に特徴量カラム）
    # になるように整形する必要があります。
    # '着順' カラムは予測対象なので、この時点では不要ですが、馬名などの識別情報は保持してください。

    logger.info(f"{race_identifier} のレースデータをスクレイピング・解析中...")

    # 1. レースHTMLのスクレイピング
    # scrape_html_race は race_id_list を受け取るので、単一のIDをリストとして渡す
    try:
        scrape_html_race(race_id_list=[race_identifier], skip=False)
        logger.info(f"レースID {race_identifier} のHTMLスクレイピングが完了しました。")
    except Exception as e:
        logger.error(f"レースID {race_identifier} のHTMLスクレイピング中にエラーが発生しました: {e}")
        return None, None

    # 2. スクレイピングしたHTMLの読み込みとパース
    # HTMLファイルは LocalPaths.RACE_HTML_DIR に保存されていると仮定
    html_file_path = Path(LocalPaths.HTML_RACE_DIR) / f"{race_identifier}.html"

    if not html_file_path.exists():
        logger.error(f"HTMLファイルが見つかりません: {html_file_path}")
        return None, None

    with open(html_file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    # ここにHTMLパースロジックを実装します。
    # 既存のモジュール (例: module/race_data_processor.py, module/horse_html_parser.py) を参考に、
    # 予測モデルが期待するDataFrame形式に変換する必要があります。
    # 現時点ではダミーデータを返しますが、この部分を実際のパースロジックに置き換えてください。
    
    # --- ダミーデータ生成 (実際のパースロジックに置き換える) ---
    # 予測に必要なカラムを定義
    required_columns = [
        'race_id', 'horse_id', 'horse_name', '枠番', '馬番', '斤量', '人気',
        'jockey_id', 'trainer_id', 'owner_id', 'breeder_id', 'father_id', 'mother_father_id',
        '性齢', '開催', '芝・ダート', '距離'
        # 他にモデルが使用する特徴量があればここに追加
    ]
    
    # BeautifulSoupでレース名を取得する例 (簡易版)
    soup = BeautifulSoup(html_content, 'html.parser')
    race_name_element = soup.find('div', class_='RaceName')
    race_name = race_name_element.text.strip() if race_name_element else "不明なレース名"

    # ダミーデータ生成
    num_horses = 18 # 仮に18頭立てとする
    data = {
        'race_id': [race_identifier] * num_horses,
        'horse_id': [f'horse_{race_identifier}_{i:02d}' for i in range(1, num_horses + 1)],
        'horse_name': [f'馬_{i:02d}' for i in range(1, num_horses + 1)],
        '枠番': [(i-1)//2 + 1 for i in range(1, num_horses + 1)], # 1-8枠を繰り返す
        '馬番': list(range(1, num_horses + 1)),
        '斤量': np.random.uniform(50.0, 60.0, num_horses).round(1),
        '人気': np.random.randint(1, num_horses + 1, num_horses),
        'jockey_id': np.random.randint(1000, 2000, num_horses),
        'trainer_id': np.random.randint(3000, 4000, num_horses),
        'owner_id': np.random.randint(5000, 6000, num_horses),
        'breeder_id': np.random.randint(7000, 8000, num_horses),
        'father_id': np.random.randint(9000, 10000, num_horses),
        'mother_father_id': np.random.randint(10000, 11000, num_horses),
        '性齢': [random.choice(['牡3', '牝4', 'セ5']) for _ in range(num_horses)],
        '開催': [random.choice(['東京', '中山', '京都', '阪神'])] * num_horses,
        '芝・ダート': [random.choice(['芝', 'ダート'])] * num_horses,
        '距離': [random.choice([1200, 1600, 2000, 2400])] * num_horses,
    }
    
    # 2. スクレイピングしたHTMLの読み込みとパース
    # HTMLファイルは LocalPaths.HTML_RACE_DIR に保存されていると仮定
    html_file_path = Path(LocalPaths.HTML_RACE_DIR) / f"{race_identifier}.html"

    if not html_file_path.exists():
        logger.error(f"HTMLファイルが見つかりません: {html_file_path}")
        return None, None

    # RaceProcessor を使用してHTMLをパース
    race_processor = RaceProcessor()
    race_info_df, race_results_df = race_processor.parse_race_html(str(html_file_path))

    if race_results_df.empty:
        logger.error(f"レースID {race_identifier} の出馬表データがパースできませんでした。")
        return None, None

    # レース名を取得
    race_name = race_info_df['レース名'].iloc[0] if not race_info_df.empty and 'レース名' in race_info_df.columns else "不明なレース名"

    # 予測に必要なカラムを選択し、整形
    # race_results_df は出馬表の形式になっていることを想定
    # 必要なカラムは feature_info.json に記載されているものに基づきます。
    # ここでは、race_predictor.py の preprocess_for_prediction が期待する形式に合わせる
    
    # 例: 必要なカラムを抽出 (実際のモデルの入力に合わせて調整)
    # '着順' カラムは予測対象なので、この時点では不要
    # 'horse_name' や '馬番' など識別情報は保持
    
    # race_results_df には既に horse_id, jockey_id, trainer_id などが含まれているはず
    # 必要に応じて、性齢、開催、芝・ダート、距離などのカラムを race_info_df から結合
    
    # 現状のrace_results_dfが予測モデルの入力に必要なカラムを全て含んでいるか確認し、不足があれば追加
    # 例えば、race_info_dfからレースの距離や開催場所などをrace_results_dfに結合する必要があるかもしれません。
    
    # ここでは、race_results_dfが予測に必要な全ての馬ごとの特徴量を含んでいると仮定します。
    # もし不足しているカラムがあれば、ここで race_info_df などから結合するロジックを追加してください。
    
    # 例: 距離と開催を結合 (race_info_dfから取得)
    if not race_info_df.empty:
        if '距離' in race_info_df.columns and '距離' not in race_results_df.columns:
            race_results_df['距離'] = race_info_df['距離'].iloc[0]
        if '開催' in race_info_df.columns and '開催' not in race_results_df.columns:
            race_results_df['開催'] = race_info_df['開催'].iloc[0]
        if '芝・ダート' in race_info_df.columns and '芝・ダート' not in race_results_df.columns:
            race_results_df['芝・ダート'] = race_info_df['芝・ダート'].iloc[0]
        if '性齢' not in race_results_df.columns: # 性齢は馬ごとの情報なので、race_results_dfに直接あるべきだが、もしなければダミー
            race_results_df['性齢'] = '不明' # 適切な値に置き換える必要あり
    
    # 最終的なDataFrameを race_df として返す
    race_df = race_results_df.copy()
    
    logger.info(f"レース {race_name} ({race_identifier}) のデータをパースしました。馬の数: {len(race_df)}")
    return race_df, race_name


def preprocess_for_prediction(
    df: pd.DataFrame, 
    continuous_feature_columns: List[str],
    categorical_cols_to_embed: List[str], # 元の日本語カラム名
    vocab_sizes: Dict[str, int], # ASCII名キー
    scaler: StandardScaler,
    list_size: int,
    name_map: Dict[str, str]
    ) -> Optional[Dict[str, np.ndarray]]:
    """
    予測用にスクレイピングされたデータを前処理し、モデル入力形式に変換します。
    """
    logger.info("予測用データの前処理を開始...")
    
    # 1. 連続特徴量の抽出とスケーリング
    X_continuous_df = df[continuous_feature_columns].copy()
    X_continuous_np = X_continuous_df.fillna(0).astype(np.float32).values
    try:
        X_continuous_scaled_np = scaler.transform(X_continuous_np)
    except ValueError as e:
        logger.error(f"連続特徴量のスケーリングに失敗: {e}")
        logger.error(f"  期待される特徴量数: {scaler.n_features_in_}, 入力された特徴量数: {X_continuous_np.shape[1]}")
        logger.error(f"  入力特徴量カラム: {continuous_feature_columns}")
        return None
    
    # 2. カテゴリカル特徴量のエンコードと抽出
    X_categorical_dict_ascii_keys = {}
    for col_original_name in categorical_cols_to_embed:
        ascii_name = name_map.get(col_original_name, col_original_name)
        if col_original_name not in df.columns:
            logger.warning(f"カテゴリカル特徴量 '{col_original_name}' (ASCII: {ascii_name}) が入力DataFrameに存在しません。ゼロで埋めます。")
            # モデルがこの入力を期待している場合、ゼロの配列を作成
            if ascii_name in vocab_sizes: # vocab_sizesのキーはASCII名
                 X_categorical_dict_ascii_keys[ascii_name] = np.zeros(len(df), dtype=np.int32)
            continue

        # 学習時と同様にfactorize（ただし、未知のカテゴリは扱えないため、マッピングが必要）
        # ここでは、学習時に保存したカテゴリマッピング（またはvocab_sizesから推測）を使って変換する想定
        # 簡単のため、ここでは学習時と同様にfactorizeし、未知の値は0（UNKNOWN_CAT）として扱う
        # より堅牢なのは、学習時の pd.factorize の uniques を保存しておき、それを使ってマッピングすること
        current_col_data = df[col_original_name].fillna("UNKNOWN_CAT").astype(str)
        
        # 学習時のカテゴリカルエンコーディングを再現
        # 本来は学習時に使用した Factorizer またはマッピングをロードして適用
        # ここでは簡略化のため、再度factorizeするが、語彙サイズは学習時のものを使う
        # これは理想的ではない。未知のカテゴリは特別なIDにマップすべき。
        # vocab_sizes[ascii_name] は学習時の語彙サイズ
        
        # 実際の予測時には、学習時に作成したカテゴリとIDの対応表を使って変換する
        # ここではダミーとして、もしカテゴリが学習時に存在しなかった場合の処理を追加
        # (例: 新しい騎手IDなど) -> UNKNOWN_CAT に対応するID (通常は0) にマップ
        # この部分は、学習時のカテゴリカルエンコーディング方法に厳密に合わせる必要があります。
        # pd.factorize は新しいカテゴリに新しいIDを振るので、学習時とテスト時でIDがズレる可能性がある。
        # 正しくは、学習時のカテゴリ->IDの辞書を保存し、それを使って変換する。
        # ここでは、dfの当該カラムが既に整数ID化されていると仮定する（prepare_features_and_labelsで処理済みのため）
        if pd.api.types.is_numeric_dtype(df[col_original_name]):
             X_categorical_dict_ascii_keys[ascii_name] = df[col_original_name].fillna(0).astype(np.int32).values
        else: # 文字列の場合は再度factorizeするが、これは訓練時と一貫性を保つのが難しい
            logger.warning(f"列 '{col_original_name}' は数値型ではありません。Factorizeを試みますが、学習時との一貫性に注意してください。")
            labels, _ = pd.factorize(current_col_data)
            X_categorical_dict_ascii_keys[ascii_name] = labels.astype(np.int32)


    # 3. リスト形式への変換 (パディング/切り捨て)
    # 1レース分のデータなので、groupは1つだけ
    
    # Continuous features
    padded_X_continuous = np.zeros((1, list_size, X_continuous_scaled_np.shape[1]), dtype=np.float32)
    num_horses = len(df)
    if num_horses > 0:
        if num_horses < list_size:
            padded_X_continuous[0, :num_horses, :] = X_continuous_scaled_np
            # カテゴリカルも同様にパディング
            for ascii_name in X_categorical_dict_ascii_keys.keys():
                original_cat_data = X_categorical_dict_ascii_keys[ascii_name]
                padded_cat_data = np.zeros(list_size, dtype=np.int32)
                padded_cat_data[:num_horses] = original_cat_data
                X_categorical_dict_ascii_keys[ascii_name] = padded_cat_data.reshape(1, list_size)
        else: # num_horses >= list_size
            # TensorFlow Rankingでは通常、関連性の高い順にソートしてから切り捨てるが、
            # 予測時は未知なので、馬番順などで先頭からlist_size分取得
            padded_X_continuous[0, :, :] = X_continuous_scaled_np[:list_size, :]
            for ascii_name in X_categorical_dict_ascii_keys.keys():
                X_categorical_dict_ascii_keys[ascii_name] = X_categorical_dict_ascii_keys[ascii_name][:list_size].reshape(1, list_size)
    else: # レースに出走馬がいない場合（通常ありえないが）
        logger.warning("レースに出走馬がいません。ゼロで埋めた特徴量を返します。")
        # X_categorical_dict_ascii_keys は既に (1, list_size) のゼロ配列になっているはず (もしキーが存在すれば)
        for ascii_name in X_categorical_dict_ascii_keys.keys():
             if X_categorical_dict_ascii_keys[ascii_name].shape != (1, list_size) :
                  X_categorical_dict_ascii_keys[ascii_name] = np.zeros((1,list_size), dtype=np.int32)


    # モデル入力用の辞書を作成
    model_input_dict = {'continuous_features': padded_X_continuous}
    model_input_dict.update(X_categorical_dict_ascii_keys)
    
    logger.info("予測用データの前処理完了。")
    return model_input_dict


def load_model_and_dependencies(model_path: str, scaler_path: str, feature_info_path: str) -> Tuple[Any, Any, Any, Any, Any, Any, Any]:
    """学習済みモデルと関連オブジェクトをロードします。"""
    logger.info("モデルと関連オブジェクトをロード中...")
    try:
        # TensorFlow Rankingのカスタムオブジェクトをロード
        # **tfr.keras.metrics.default_keras_metrics() の部分でエラーが出る場合があるため、一時的にコメントアウトまたは修正
        # TensorFlowのバージョンによっては、metricsが直接辞書として展開できない場合がある
        custom_objects = {'SoftmaxLoss': tfr.keras.losses.SoftmaxLoss}
        # if hasattr(tfr.keras.metrics, 'default_keras_metrics') and callable(tfr.keras.metrics.default_keras_metrics):
        #     custom_objects.update(tfr.keras.metrics.default_keras_metrics())
        
        model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)
        logger.info(f"モデルを {model_path} からロードしました。")
        
        scaler = joblib.load(scaler_path)
        logger.info(f"StandardScalerを {scaler_path} からロードしました。")
        
        with open(feature_info_path, 'r', encoding='utf-8') as f:
            feature_info = json.load(f)
        logger.info(f"特徴量情報を {feature_info_path} からロードしました。")

        continuous_cols = feature_info['continuous_feature_columns']
        categorical_cols_original = feature_info['categorical_cols_to_embed'] # 日本語名
        vocab_sizes_ascii_keys = feature_info['vocab_sizes'] # ASCII名キー
        list_size_trained = feature_info['list_size']
        name_map_loaded = feature_info.get('JAPANESE_TO_ASCII_NAME_MAP', JAPANESE_TO_ASCII_NAME_MAP)


        return model, scaler, continuous_cols, categorical_cols_original, vocab_sizes_ascii_keys, list_size_trained, name_map_loaded
    except Exception as e:
        logger.error(f"モデルまたは依存関係のロード中にエラーが発生しました: {e}")
        raise


def display_predictions(race_df: pd.DataFrame, scores: np.ndarray):
    """予測結果を表示します。"""
    if race_df.empty or scores.size == 0:
        logger.warning("表示する予測結果がありません。")
        return

    # scores は (1, list_size) の形状と仮定
    predicted_scores = scores[0] 
    
    # 元のDataFrameの馬の数とスコアの数を合わせる
    num_horses_in_race = len(race_df)
    
    # 予測スコアをDataFrameに追加（実際の馬の数まで）
    # パディングされた部分は無視
    race_df['predicted_score'] = pd.Series(predicted_scores[:num_horses_in_race], index=race_df.index[:num_horses_in_race])
    
    # スコアに基づいて降順ソート
    ranked_df = race_df.sort_values(by='predicted_score', ascending=False).reset_index(drop=True)
    
    logger.info("\n--- 予測ランキング ---")
    for i, row in ranked_df.iterrows():
        # 'horse_name' カラムが存在するか確認
        horse_name = row.get('horse_name', f"馬ID: {row.get('horse_id', 'N/A')}")
        logger.info(f"予測順位 {int(i)+1}: {horse_name} (馬番: {row.get('馬番', 'N/A')}, スコア: {row.get('predicted_score', 0.0):.4f})")
    logger.info("--- 予測完了 ---")


def main():
    parser = argparse.ArgumentParser(description="競馬レース予測実行スクリプト")
    parser.add_argument('--race_identifier', type=str, required=True,
                        help='予測対象のレースIDまたはURL (例: 202306040811)')
    parser.add_argument('--model_path', type=str, default='ranking_model',
                        help='学習済みモデルのパス (SavedModelディレクトリ)')
    parser.add_argument('--scaler_path', type=str, default='scaler.pkl',
                        help='学習済みStandardScalerのパス (.pklファイル)')
    parser.add_argument('--feature_info_path', type=str, default='feature_info.json',
                        help='特徴量情報（カラム名、語彙サイズなど）のJSONファイルパス')
    
    args = parser.parse_args()

    try:
        # 1. モデルと依存関係のロード
        model, scaler, continuous_cols, categorical_cols_original, \
        vocab_sizes_ascii_keys, list_size_trained, name_map = \
            load_model_and_dependencies(args.model_path, args.scaler_path, args.feature_info_path)

        # 2. レースデータのスクレイピングと解析 (プレースホルダー)
        #    この部分は実際のウェブサイト構造に合わせて実装する必要があります。
        #    出力は、学習データと同様のカラムを持つDataFrameを期待します。
        #    '着順'カラムは不要です。'horse_name'や馬番など識別情報を含めてください。
        race_df, race_name = scrape_and_parse_race_data(args.race_identifier)
        if race_df is None or race_df.empty:
            logger.error(f"{args.race_identifier} のデータ取得または解析に失敗しました。")
            return
        
        logger.info(f"レース名: {race_name}")
        logger.info(f"取得した馬の数: {len(race_df)}")

        # 3. 予測用データの前処理
        #    categorical_cols_to_embed は日本語名リスト、vocab_sizes はASCII名キーの辞書
        model_input_features = preprocess_for_prediction(
            race_df.copy(), # 元のDataFrameを保持するためにコピーを渡す
            continuous_cols,
            categorical_cols_original, 
            vocab_sizes_ascii_keys,
            scaler,
            list_size_trained,
            name_map
        )
        
        if model_input_features is None:
            logger.error("予測用データの前処理に失敗しました。")
            return

        # 4. 予測の実行
        logger.info("予測を実行中...")
        # モデルの predict メソッドは辞書型の入力を受け付ける
        predictions = model.predict(model_input_features)
        
        # 5. 結果の表示
        display_predictions(race_df, predictions)

    except Exception as e:
        logger.error(f"予測処理中にエラーが発生しました: {e}", exc_info=True)

if __name__ == "__main__":
    # 例: python race_predictor.py --race_identifier dummy_race_202401010111 --model_path ranking_model --scaler_path scaler.pkl --feature_info_path feature_info.json
    main()
