import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

# 最新のプロセッサクラスをインポート
from module.race_batch_processor import process_race_bin_to_pickle_batch
from module.race_horse_targeted_processor import RaceHorseTargetedProcessor
from module.race_data_processor import RaceProcessor
from module.data_merger import DataMerger

import pandas as pd
import numpy as np
from tqdm.notebook import tqdm

# 処理したい年のリストを指定
years = ['2020', '2021', '2022', '2023', '2024']  # 必要な年を追加してください
# 単一年の場合: years = ['2024']
# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']

# データディレクトリの設定
base_dir = Path.cwd()  # 現在のディレクトリを基準
data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'

# 出力ディレクトリの作成
output_dir = Path('output')
output_dir.mkdir(exist_ok=True)

print(f"処理対象年: {years}")
print(f"データディレクトリ: {data_dir}")
print(f"出力ディレクトリ: {output_dir}")

processor = RaceProcessor()

import module.race_batch_processor
# 使用例1: binファイルからpickleファイルへの変換
years = ["2019"]
module.race_batch_processor.process_race_bin_to_pickle_batch(
    years=years,
    bin_base_dir=data_dir,
    output_dir=output_dir,
    parallel=True,
    max_workers=4
)

from module.race_horse_targeted_processor import RaceHorseTargetedProcessor

# プロセッサ作成
processor = RaceHorseTargetedProcessor()

# 2024年のレースから馬IDを抽出し、馬情報を処理
result = processor.process_race_to_horses(
    year="2022",
    include_basic_info=True,
    include_results=True,
    parallel=True,
    max_workers=4,
    save_output=True
)

race_info = pd.read_pickle(output_dir / "race_info_2024.pickle")
race_info

race_results = pd.read_pickle(output_dir / "race_results_2024.pickle")
race_results

horse_info = pd.read_pickle(output_dir / "race_horses_horse_info_2024.pickle")
horse_info

horse_results = pd.read_pickle(output_dir / "race_horses_horse_results_2024.pickle")
horse_results

# %% [markdown]
# # ComprehensiveDataIntegrator の利用サンプル
# 
# このノートブックでは、`ComprehensiveDataIntegrator` を使用して、
# レース情報、レース結果、馬の基本情報、馬の過去成績を統合した
# 包括的なデータテーブルを生成する方法を示します。

# %%
import os
import pandas as pd
import logging

# 必要なモジュールをインポート
# (プロジェクトのルートディレクトリからの相対パスで指定することを想定)
# 必要に応じて sys.path にプロジェクトのパスを追加してください。
# import sys
# sys.path.append('F:/keiba__AI_2025') # ご自身のプロジェクトパスに合わせてください

from module.comprehensive_data_integrator import ComprehensiveDataIntegrator, load_config_from_file, merge_settings
from module.constants import LocalPaths, ComprehensiveIntegratorConfig # 設定クラスもインポート

# %% [markdown]
# ## 1. ロギング設定
# 
# 詳細な処理状況を確認するためにロギングを設定します。

# %%
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])
logger = logging.getLogger() # ルートロガーを取得
logger.setLevel(logging.INFO) # INFOレベル以上のログを出力
# --- 設定の選択 (上記いずれかの方法で config_dict を準備) ---
# 例: Python辞書で設定する場合
config_dict_for_integrator = {
    # "default_year": "2023", # generate_comprehensive_table の引数で指定する場合は不要
    "default_years": ["2022"],
    "use_pickle_source": True, # pickleファイルから読み込むことを指定
    "pickle_base_dir": output_dir, #pickleファイルが格納されているディレクトリ
    #各pickleファイルのファイル名テンプレート (デフォルト値の例)
    "race_info_filename_template": "race_info_{year}.pickle", #RaceProcessor側でデフォルトを持つ想定
    "race_results_filename_template": "race_results_{year}.pickle", #RaceProcessor側でデフォルトを持つ想定
    "horse_info_pickle_filename_template": "race_horses_horse_info_{year}.pickle", #ComprehensiveIntegratorConfigで定義可能
    "horse_results_pickle_filename_template": "race_horses_horse_results_{year}.pickle", #ComprehensiveIntegratorConfigで定義可能

    # カスタム特徴量pickle (使用しない場合は enable_custom_feature_pickle: False)
    "enable_custom_feature_pickle": False,
    # "custom_feature_pickle_filename_template": "custom_features_{year}.pickle",

    "include_race_info": True,
    "include_horse_info": True,
    "include_past_performance": True,
    "performance_window_races": [3, 5, 10], # 直近3, 5, 10レースの成績を集計
    "parallel": True,
    "max_workers": os.cpu_count() or 1, # 環境に合わせて調整
    "save_output": False # NotebookではまずFalseで結果確認
}

# 設定ファイル (config.json) があればそれも読み込む (オプション)
# config_file_path = "config.json" 
# file_config = load_config_from_file(config_file_path)
# final_settings = merge_settings(file_config, argparse.Namespace(**config_dict_for_integrator)) # argparse.Namespaceでラップ

# ここでは直接 config_dict_for_integrator を使用
final_settings = config_dict_for_integrator

print("\n最終的な実行設定:")
for key, value in final_settings.items():
    print(f"  {key}: {value}")

# %% [markdown]
# ## 3. `ComprehensiveDataIntegrator` のインスタンス化
# 
# 準備した設定で `ComprehensiveDataIntegrator` のインスタンスを作成します。

# %%
try:
    integrator = ComprehensiveDataIntegrator(config=final_settings)
    print("\nComprehensiveDataIntegrator のインスタンス化に成功しました。")
except Exception as e:
    logger.error(f"ComprehensiveDataIntegrator のインスタンス化中にエラーが発生しました: {e}", exc_info=True)
    integrator = None # エラー時は None にしておく

# %% [markdown]
# ## 4. `generate_comprehensive_table` メソッドの実行
# 
# 包括的なデータテーブルを生成します。
# `years` 引数で処理対象の年度を明示的に指定することも可能です。
# 
# **注意:** 初回実行時やHTMLからデータを取得する場合、時間がかかることがあります。
# pickleファイルが存在しない場合、`HorseProcessor` や `RaceProcessor` がHTMLからの読み込みにフォールバックしようとします。
# その際にHTMLファイルが存在しないと、データが空になる可能性があります。
# 
# 事前に `RaceDataProcessor` や `HorseDataProcessor` のスクリプトを実行して、
# 必要なpickleファイル (`race_info_{year}.pickle`, `race_results_{year}.pickle`, `horse_info_{year}.pickle`, `horse_results_{year}.pickle` など) を
# `pickle_base_dir` で指定したディレクトリに配置しておいてください。

# %%
if integrator:
    try:
        # generate_comprehensive_table の引数で設定を上書きすることも可能
        # ここではインスタンス化時の設定を使用
        comprehensive_df = integrator.generate_comprehensive_table(
            # years=["2023"], # 特定の年だけ処理したい場合
            # use_pickle_source=True # メソッド呼び出し時にも指定可能
        )
        
        print("\n`generate_comprehensive_table` の実行が完了しました。")
    except Exception as e:
        logger.error(f"`generate_comprehensive_table` の実行中にエラーが発生しました: {e}", exc_info=True)
        comprehensive_df = pd.DataFrame() # エラー時は空のDataFrame
else:
    print("Integratorのインスタンスが作成されていないため、処理をスキップします。")
    comprehensive_df = pd.DataFrame()

# %% [markdown]
# ## 5. 結果の確認と利用

# %%
if not comprehensive_df.empty:
    print(f"\n✅ 包括的データ生成完了:")
    print(f"   データ件数: {len(comprehensive_df):,}件")
    print(f"   カラム数: {len(comprehensive_df.columns)}個")
    if 'race_id' in comprehensive_df.columns:
        print(f"   ユニークレース数: {comprehensive_df['race_id'].nunique()}")
    if 'horse_id' in comprehensive_df.columns:
        print(f"   ユニーク馬数: {comprehensive_df['horse_id'].nunique()}")

    print("\n📋 サンプルデータ (先頭5行):")
    display(comprehensive_df.head())

    print("\n📊 データ概要:")
    summary = integrator.get_data_summary()
    for key, value in summary.items():
        if key == "data_columns":
            print(f"  {key}:")
            for cat, cols in value.items():
                print(f"    {cat}: {len(cols)}個")
        elif key == "missing_data_ratio":
            print(f"  {key} (上位5件):")
            sorted_missing = sorted(value.items(), key=lambda item: item[1], reverse=True)
            for col, ratio in sorted_missing[:5]:
                if ratio > 0:
                    print(f"    {col}: {ratio:.2%}")
        else:
            print(f"  {key}: {value}")
    
    # カラム一覧
    # print("\nカラム一覧:")
    # for col in comprehensive_df.columns:
    #     print(f"- {col}")

else:
    print("\n❌ 生成されたデータは空です。ログを確認してエラーの原因を調査してください。")
    print("考えられる原因:")
    print("- 指定したpickleファイルが存在しない、またはパスが間違っている。")
    print("- pickleファイルの中身が空、または期待する形式ではない。")
    print("- HTMLからの読み込みにフォールバックしたが、HTMLファイルが存在しない。")
    print("- データ処理中に何らかのエラーが発生した。")

# %% [markdown]
# ## 6. (オプション) 結果のファイル保存
# 
# 生成されたデータをCSVやPickle形式で保存します。

# %%
# save_output を True にして実行する場合
# if integrator and not comprehensive_df.empty and final_settings.get("save_output", False):
#     try:
#         current_years = final_settings.get('default_years', [])
#         if isinstance(current_years, list) and len(current_years) > 1:
#             year_range_str = f"{min(current_years)}-{max(current_years)}"
#             filename_prefix_for_save = f"{final_settings.get('filename_prefix', integrator.config.filename_prefix)}_multi_year_{year_range_str}"
#             save_year_param = None
#         else:
#             filename_prefix_for_save = final_settings.get('filename_prefix', integrator.config.filename_prefix)
#             save_year_param = current_years[0] if isinstance(current_years, list) and current_years else final_settings.get('default_year', integrator.config.default_year)
        
#         pickle_path, csv_path = integrator.save_comprehensive_table(
#             filename_prefix=filename_prefix_for_save,
#             year=save_year_param, # 単一年度の場合のみ年を指定
#             save_pickle=True,
#             save_csv=True
#         )
#         print("\n💾 ファイル保存完了:")
#         if pickle_path:
#             print(f"   Pickle: {os.path.abspath(pickle_path)}")
#         if csv_path:
#             print(f"   CSV: {os.path.abspath(csv_path)}")
#     except Exception as e:
#         logger.error(f"ファイル保存中にエラー: {e}", exc_info=True)
# elif integrator and comprehensive_df.empty:
#     print("データが空のため、ファイル保存はスキップされました。")
# else:
#     print("ファイル保存は設定されていません (save_output=False)。")

# %% [markdown]
# ---
# これで、`ComprehensiveDataIntegrator` を使用してデータを統合し、結果を確認する基本的な流れは完了です。
# 設定や処理対象のデータを変更して、さまざまなパターンのデータ統合を試してみてください。

from datetime import timedelta
import pandas as pd # pandas をインポート
import numpy as np # numpy をインポート (ダミーデータ用)
from module.constants import HorseInfoCols # HorseInfoCols.BIRTHDAY を使用するためにインポート

# from module.constants import ResultsCols # 定義されている場合
actual_rank_col = '着順'
# actual_rank_col = ResultsCols.RANK # 定数を使用する場合

# '着順' カラムの存在を確認 (ResultsCols.RANK を使うのが望ましい)
if actual_rank_col not in comprehensive_df.columns:
    if 'rank' in comprehensive_df.columns: # 'rank' カラムでフォールバック
        actual_rank_col = 'rank'
        print(f"警告: '着順' カラムが見つからないため、'{actual_rank_col}' カラムを使用します。")
    else:
        raise KeyError(f"関連度スコアの生成に必要な着順カラム ('着順' または 'rank') がデータフレームに存在しません。")

# 1. '着順' カラムを数値型に変換
comprehensive_df[actual_rank_col] = pd.to_numeric(comprehensive_df[actual_rank_col], errors='coerce')

# NaNが含まれている場合の処理
if comprehensive_df[actual_rank_col].isnull().any():
    num_rows_before_dropna = len(comprehensive_df)
    # 数値に変換できなかった行 (NaNになった行) を除外
    comprehensive_df.dropna(subset=[actual_rank_col], inplace=True)
    num_rows_after_dropna = len(comprehensive_df)
    num_dropped = num_rows_before_dropna - num_rows_after_dropna
    if num_dropped > 0:
        print(f"情報: '{actual_rank_col}' カラムに数値変換できない値、または欠損値が含まれていたため、{num_dropped}行が除外されました。")

    if comprehensive_df.empty:
        raise ValueError(
            f"'{actual_rank_col}' カラムの数値変換不能な行を除外した結果、DataFrameが空になりました。"
            f"有効な着順データを持つ行が存在しません。"
        )
    comprehensive_df[actual_rank_col] = comprehensive_df[actual_rank_col].astype(int)
else:
    # NaN がない場合でも、pd.to_numeric は float を返すことがあるため int に変換
    # (例: "1" が 1.0 になる)。元々全て整数で欠損がなければこの変換は厳密には不要ですが、
    # 確実性を期すために行います。
    if not comprehensive_df.empty: # DataFrameが空でないことを確認
        comprehensive_df[actual_rank_col] = comprehensive_df[actual_rank_col].astype(int)
    elif actual_rank_col in comprehensive_df.columns: # カラムは存在するがDFが空の場合
        # comprehensive_df が初期状態で空の場合、このelseブロックは実行されるが、
        # comprehensive_df[actual_rank_col] は空のSeriesなので astype(int) は問題ない。
        # ただし、この状況は通常、入力データが空であることを意味する。
        pass


# 2. 数値型に変換したカラムを使って計算
# comprehensive_dfが空の場合、以降の処理でエラーが発生する可能性があるため、チェックを追加
if comprehensive_df.empty:
    # このエラーは、上記の NaN 処理で DataFrame が空になった場合やすでに捕捉されているか、
    # または初期データが空だった場合に発生します。
    print("警告: 関連度スコア計算の前にDataFrameが空です。関連度スコアは計算されません。")
    # この後の処理で空のDataFrameを扱うことになるため、各処理が空のDataFrameに対応しているか確認が必要。
    # もしくは、ここで raise ValueError("処理対象のデータがありません。") のように処理を中断する。
else:
    # 各レースの出走頭数（= そのレースでの最大着順）を取得します。
    comprehensive_df['num_starters_in_race'] = comprehensive_df.groupby('race_id')[actual_rank_col].transform('max')
    # 関連度スコアを計算します: (出走頭数 - 実際の着順 + 1)
    # comprehensive_df['relevance_score'] = comprehensive_df['num_starters_in_race'] - comprehensive_df[actual_rank_col] + 1
    def calculate_relative_scores(series_rank): # 引数は各グループの着順Series
        max_rank_in_group = series_rank.max()
        return max_rank_in_group + 1 - series_rank

    comprehensive_df['relevance_score'] = comprehensive_df.groupby('race_id')[actual_rank_col].transform(calculate_relative_scores)

y_label_column_name = 'relevance_score'
print("--- 関連度スコア生成後 ---")
if not comprehensive_df.empty:
    print(comprehensive_df[['race_id', 'horse_id', actual_rank_col, 'num_starters_in_race', y_label_column_name]].head())
else:
    # y_label_column_name が 'relevance_score' であり、これが生成されていない可能性があるため、
    # 表示するカラムを調整するか、メッセージのみ表示する。
    print(f"DataFrameが空のため、関連度スコアを含む情報を表示できません。表示予定のカラム: ['race_id', 'horse_id', '{actual_rank_col}', 'num_starters_in_race', '{y_label_column_name}']")


# # --- 関連度スコアの生成 (固定スコア) ---

# # `label_gain` を指定しなくてもモデルはスコアが高いものを上位にしようと学習します。

# 特徴量の選択
# 'rank' カラムは、もし actual_rank_col と同じなら y_label_column_name の元データなので除外。
# もし 'rank' が actual_rank_col と異なり、かつ特徴量として使いたくない別のカラムなら、リストに含める。
# ここでは、actual_rank_col (元 '着順') を特徴量から除外する。
columns_to_exclude = [
    'race_id', 'horse_id', 'date', y_label_column_name, actual_rank_col,
    'last_race_date', # datetime64型なので除外 (interval_days を使用)
    HorseInfoCols.BIRTHDAY,  # datetime64型なので除外 (年齢カラムを使用)
    '騎手', '単勝', '調教ﾀｲﾑ', '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考', '調教師', '馬主', '賞金(万円)', 'レース名',
    'around', '調教師_horse_info', '馬主_horse_info', '生産者', '産地', 'セリ取引価格', '獲得賞金', '通算成績', '主な勝鞍', '近親馬', 'horse_name', 'trainer_id_horse_info',
    'father_name', 'mother_father_name', 'mother_name' ,'sibling_ids', '募集情報',
    'タイム', '着差', 'ﾀｲﾑ指数', '通過', '上り', '人気', 'weather', 'race_type', 'ground_state', 'mother_id' ,
]
if 'rank' in comprehensive_df.columns and 'rank' != actual_rank_col: # 'rank'が別カラムならそれも除外
    columns_to_exclude.append('rank')
# num_starters_in_race も relevance_score の計算に使ったので、リークを避けるため除外した方が良い場合がある。
# ただし、レースの規模を示す特徴量として有効な場合もあるので、ここでは残す。
columns_to_exclude.append('num_starters_in_race')
feature_columns = [
    col for col in comprehensive_df.columns
        if col not in columns_to_exclude
]
print(f"特徴量として使用するカラム: {feature_columns}")

# 欠損値処理 (データ型に応じて処理を分岐)
for col in feature_columns:
    if comprehensive_df[col].dtype == pd.StringDtype():
        # StringDtype のカラムは、カテゴリカル特徴量として扱うことを想定し、
        # 欠損値を表す特定の文字列で埋める。
        comprehensive_df[col] = comprehensive_df[col].fillna("_NaN_") # または pd.NA や "_UNKNOWN_"
        # LightGBMでカテゴリカル特徴量として扱う場合、pd.NAより具体的な文字列が良い場合がある。
    elif pd.api.types.is_numeric_dtype(comprehensive_df[col].dtype):
        # 既に数値型のカラム
        comprehensive_df[col] = comprehensive_df[col].fillna(-999.0)
    elif comprehensive_df[col].dtype == 'object':
        # object型のカラムは、まず数値への変換を試みる
        try:
            comprehensive_df[col] = pd.to_numeric(comprehensive_df[col], errors='coerce').fillna(-999.0)
        except Exception:
            # 数値に変換できないobject型（文字列など）は、特定の文字列で埋める
            comprehensive_df[col] = comprehensive_df[col].fillna("_UNKNOWN_")
    # 他の型（datetimeなど）は、必要に応じて個別の欠損値処理を行う
# データをレースIDと日付でソート

# --- 2. データの分割 (時系列を考慮し、自動的に分割日を決定) ---
if 'date' not in comprehensive_df.columns:
    raise KeyError("データ分割に必要な 'date' カラムがデータフレームに存在しません。")
if not pd.api.types.is_datetime64_any_dtype(comprehensive_df['date']):
    comprehensive_df['date'] = pd.to_datetime(comprehensive_df['date'], errors='coerce')
    if comprehensive_df['date'].isnull().any():
# 日付変換でNaNが発生した場合の処理（例：警告、エラー、除外など）
        print("警告: 'date' カラムに日付に変換できない値が含まれており、NaNになりました。")
        # raise ValueError("日付カラム ('date') に不正な値が含まれています。変換できませんでした。")

if comprehensive_df.empty or comprehensive_df['date'].isnull().all():
    raise ValueError("データフレームが空か、有効な日付データがありません。分割できません。")

# 利用可能なデータの最大日付を取得
max_data_date = comprehensive_df['date'].max()

# 分割戦略: 例えば、最新のN日間をテストデータとする
test_period_days = 30  # 例: 最新の30日間をテストデータとする

if pd.isna(max_data_date):
    raise ValueError("最大日付が取得できませんでした。")

# split_date をここで定義
split_date = max_data_date - timedelta(days=test_period_days)

# もし、学習データが極端に少なくなる場合や、テストデータが存在しない場合の調整
min_train_races = 5 # 学習データとして最低限確保したいレース数（目安）
min_test_races = 1  # テストデータとして最低限確保したいレース数（目安）

# 全レース数を取得
total_unique_races = comprehensive_df['race_id'].nunique()

# 学習データとテストデータのレース数を計算
potential_train_races = comprehensive_df[comprehensive_df['date'] < split_date]['race_id'].nunique()
potential_test_races = comprehensive_df[comprehensive_df['date'] >= split_date]['race_id'].nunique()

if potential_train_races < min_train_races and total_unique_races > (min_train_races + min_test_races):
    # 学習データが少なすぎる場合、テスト期間を短縮して学習データを増やす
    # (ただし、テストデータが最低限確保できる範囲で)
    print(f"警告: 自動決定されたsplit_date ({split_date.strftime('%Y-%m-%d')}) では学習レース数が不足 ({potential_train_races} レース)。")
    # split_date を調整するロジック (例: テストレース数がmin_test_racesになるようにsplit_dateを前にずらす)
    # ここでは簡略化のため、日付ベースの分割を優先し、警告のみとします。
    # より高度な調整が必要な場合は、レースIDのリストで分割点を決めるなどの方法も考えられます。
    # 例: sorted_race_ids = comprehensive_df['race_id'].unique()
    #      split_race_id_index = len(sorted_race_ids) - min_test_races
    #      if split_race_id_index > 0:
    #          split_race_id = sorted_race_ids[split_race_id_index]
    #          split_date = comprehensive_df[comprehensive_df['race_id'] == split_race_id]['date'].min()
    #          print(f"split_dateを {split_date.strftime('%Y-%m-%d')} に調整しました。")
elif potential_test_races < min_test_races and total_unique_races > (min_train_races + min_test_races):
    # テストデータが少なすぎる場合、学習期間を短縮してテストデータを増やす
    print(f"警告: 自動決定されたsplit_date ({split_date.strftime('%Y-%m-%d')}) ではテストレース数が不足 ({potential_test_races} レース)。")
    # split_date を調整するロジック (例: 学習レース数がmin_train_racesになるようにsplit_dateを後ろにずらす)
elif comprehensive_df['date'].min() >= split_date and comprehensive_df['date'].nunique() > 1 :
    # 全データがテスト期間に入ってしまう場合 (かつ日付が複数ある場合)
    # データの中間点で分割する
    sorted_unique_dates = sorted(comprehensive_df['date'].unique())
    split_point_index = len(sorted_unique_dates) // 2 # split_point_index の定義を追加
    split_date = pd.to_datetime(sorted_unique_dates[split_point_index])
    print(f"警告: 自動決定されたsplit_dateでは学習データがありません。"
          f"データの中間点 ({split_date.strftime('%Y-%m-%d')}) で分割します。")
elif comprehensive_df['date'].nunique() <= 1:
    # 日付が1種類しかない場合は学習とテストに分割できない
    print("警告: データに含まれる日付が1種類のみのため、学習データとテストデータに分割できません。全データを学習に使用します。")
    split_date = max_data_date + timedelta(days=1) # 全データが学習データになるように

print(f"自動決定された分割日 (この日より前のデータを学習、以降をテスト): {split_date.strftime('%Y-%m-%d')}")

# データを分割
train_df = comprehensive_df[comprehensive_df['date'] < split_date].copy()
test_df = comprehensive_df[comprehensive_df['date'] >= split_date].copy()

# X_test のダミー定義 (実際のモデル学習・予測部分で適切に定義される想定)
# --- 3. 特徴量、ラベル、グループ情報の準備 ---
X_train, y_train, group_train = pd.DataFrame(), pd.Series(dtype='float64'), []
X_test, y_test, group_test = pd.DataFrame(), pd.Series(dtype='float64'), []
categorical_features = [] # カテゴリカル特徴量のリストを初期化

# y_label_column_name と feature_columns がこのスコープで定義されていることを確認
# y_label_column_name は 'relevance_score'
# feature_columns は既に定義済み

if not train_df.empty:
    # object型とStringDtypeをcategory型に変換 (LightGBMが推奨する型)
    # categorical_features リストもここで作成
    categorical_features = [col for col in feature_columns if train_df[col].dtype.name == 'object' or isinstance(train_df[col].dtype, pd.StringDtype)]
    for col in categorical_features:
        train_df[col] = train_df[col].astype('category')
        if not test_df.empty and col in test_df.columns: # test_dfにも適用
             test_df[col] = test_df[col].astype('category')

    X_train = train_df[feature_columns]
    y_train = train_df[y_label_column_name]
    group_train = train_df.groupby('race_id').size().tolist()
    print(f"学習データ: X_train {X_train.shape}, y_train {y_train.shape}, group_train {len(group_train)} groups")
    print(f"学習データのカテゴリカル特徴量: {[col for col in X_train.columns if X_train[col].dtype.name == 'category']}")

if not test_df.empty:
    X_test = test_df[feature_columns]
    y_test = test_df[y_label_column_name]
    group_test = test_df.groupby('race_id').size().tolist()
    print(f"テストデータ: X_test {X_test.shape}, y_test {y_test.shape}, group_test {len(group_test)} groups")
    print(f"テストデータのカテゴリカル特徴量: {[col for col in X_test.columns if X_test[col].dtype.name == 'category']}")

if train_df.empty:
    print(f"警告: 学習データが空です。split_date ({split_date.strftime('%Y-%m-%d')}) を確認してください。")
    # train_dfが空の場合、test_data_with_predictions もこの時点では未定義のはずなので、以下の行はコメントアウトまたは削除
    # test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')


# 以下の部分は予測結果の表示ロジックの一部であり、エラー箇所とは直接関係ありませんが、
# test_df が空でないこと、およびモデルが学習済みであることを確認する文脈で使われるべきです。
# このdiffでは、エラーの直接的な原因である split_date の未定義問題に焦点を当てています。
# test_data_with_predictions の定義や X_test の存在確認は、モデル学習・予測のフェーズで行われます。
if not test_df.empty: # test_dfが空でない場合のみ予測関連の処理を行う想定
    test_data_with_predictions = test_df.copy() # 予測スコアなどを追加する前のコピー
    # 実際の予測スコアを代入する必要があります。ここではダミーとしてランダムな値を入れます。
    # この部分はモデルの予測結果で置き換えてください。
    if not test_data_with_predictions.empty:
        test_data_with_predictions['predicted_score'] = np.random.rand(len(test_data_with_predictions))
    # test_data_with_predictions の定義と predicted_score の代入がここより前にある想定
    # このブロックはモデル学習・予測後なので、test_data_with_predictions は定義済みのはず
    # ただし、このコードスニペットの文脈では未定義の可能性があるため、ダミーの定義を追加するか、
    # 実際のモデル学習・予測コードがこの前にあることを前提とする。
    # 前回の修正で test_data_with_predictions の初期化とダミーの predicted_score を追加済み。
    # ここでは、その前提で進める。
    if 'test_data_with_predictions' in locals() and 'predicted_score' in test_data_with_predictions.columns:
        test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')
        print("\n予測結果（テストデータ）:")
    else:
        print("\n予測に必要な test_data_with_predictions または predicted_score が未定義です。")
        print(test_data_with_predictions[['race_id', 'horse_id', 'predicted_score', 'predicted_rank']].head())
elif not X_test.empty: # X_test が空でない（つまりテストデータの特徴量がある）が、モデルが学習されていない場合など
    print("\nモデルが学習されていない、またはテストデータの特徴量が空のため、テストデータの予測はスキップされました。")
else:
    print("\nテストデータがないため、予測はスキップされました。")


import lightgbm as lgb
import optuna
import pandas as pd
import functools # functools.partial を使用するためにインポート

# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。

# --- Optunaによるハイパーパラメータ最適化 ---
def objective_for_lgbm_ranker(trial, x_train_data, y_train_data, group_train_data,
                              x_test_data, y_test_data, group_test_data,
                              cat_features, eval_at_k):
    """Optunaの目的関数。LightGBM LGBMRankerのハイパーパラメータを最適化します。"""
    params = {
        'objective': 'lambdarank',
        'metric': 'ndcg',
        'boosting_type': 'gbdt',
        'n_estimators': trial.suggest_int('n_estimators', 100, 1000, step=100),
        'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.1, log=True),
        'num_leaves': trial.suggest_int('num_leaves', 15, 150),
        'max_depth': trial.suggest_int('max_depth', 3, 10),
        'min_child_samples': trial.suggest_int('min_child_samples', 5, 50),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0, step=0.1),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0, step=0.1),
        'reg_alpha': trial.suggest_float('reg_alpha', 1e-3, 10.0, log=True),
        'reg_lambda': trial.suggest_float('reg_lambda', 1e-3, 10.0, log=True),
        'random_state': 42,
        'n_jobs': -1,
        'verbose': -1,
    }

    model = lgb.LGBMRanker(**params)
    early_stopping_callback = lgb.early_stopping(
        stopping_rounds=20,
        verbose=False
    )

    # テストデータが存在し、かつ空でない場合にのみ評価セットとして使用
    eval_set = []
    eval_group = []
    if x_test_data is not None and not x_test_data.empty and \
       y_test_data is not None and not y_test_data.empty and \
       group_test_data is not None and len(group_test_data) > 0:
        eval_set = [(x_test_data, y_test_data)]
        eval_group = [group_test_data]
    else:
        print("警告: テストデータが不完全なため、Optunaでの検証は学習データで行います（過学習のリスクあり）。")
        # 代替として学習データの一部を検証に使うか、CVを実装することも検討できます。
        # ここでは簡略化のため、検証セットなし（または学習データ自身を検証に使う）とします。
        # eval_set = [(x_train_data, y_train_data)] # 学習データで評価する場合
        # eval_group = [group_train_data]
        # より安全なのは、テストデータがない場合は高いスコアを返さないことです。
        return -float('inf')


    model.fit(
        x_train_data,
        y_train_data,
        group=group_train_data,
        eval_set=eval_set,
        eval_group=eval_group,
        eval_at=[eval_at_k], # 単一のkで評価
        categorical_feature=cat_features,
        callbacks=[early_stopping_callback]
    )

    if model.evals_result_ and 'valid_0' in model.evals_result_ and f'ndcg@{eval_at_k}' in model.evals_result_['valid_0']:
        score = model.evals_result_['valid_0'][f'ndcg@{eval_at_k}'][-1]
    else:
        print(f"警告: 評価スコア (ndcg@{eval_at_k}) を取得できませんでした。")
        score = -float('inf')

    return score

# --- データ準備とOptuna実行制御 ---
# グローバルスコープの変数が存在するかどうかを確認
data_is_ready = (
    'X_train' in globals() and not X_train.empty and
    'y_train' in globals() and not y_train.empty and
    'group_train' in globals() and len(group_train) > 0 and
    'categorical_features' in globals() and
    'feature_columns' in globals()
)
# テストデータは任意とするが、存在すればOptunaや最終評価で使用
test_data_is_ready = (
    'X_test' in globals() and not X_test.empty and
    'y_test' in globals() and not y_test.empty and
    'group_test' in globals() and len(group_test) > 0
)

USE_OPTUNA = True  # Optunaによる最適化を実行するかどうか
optimized_lgbm_ranker = None
best_params_from_optuna = {}

if USE_OPTUNA and data_is_ready:
    print("Optunaによるハイパーパラメータ最適化を開始します...")
    # functools.partial を使って目的関数に必要なデータを渡す
    # 評価するkの値を指定 (例: NDCG@3)
    target_eval_k_for_optuna = 3
    objective_with_data = functools.partial(
        objective_for_lgbm_ranker,
        x_train_data=X_train, y_train_data=y_train, group_train_data=group_train,
        x_test_data=X_test if test_data_is_ready else None, # テストデータがない場合はNoneを渡す
        y_test_data=y_test if test_data_is_ready else None,
        group_test_data=group_test if test_data_is_ready else None,
        cat_features=categorical_features,
        eval_at_k=target_eval_k_for_optuna
    )

    study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=42))
    study.optimize(objective_with_data, n_trials=30, timeout=1800)

    print("\nOptunaによるハイパーパラメータ最適化が完了しました。")
    print("最適な試行:")
    best_trial_result = study.best_trial
    print(f"  Value (NDCG@{target_eval_k_for_optuna}): {best_trial_result.value}")
    print("  Params: ")
    for key, value in best_trial_result.params.items():
        print(f"    {key}: {value}")
    best_params_from_optuna = best_trial_result.params

# --- 最終モデルの学習 ---
if data_is_ready:
    print("\n最終モデルを学習します...")
    if USE_OPTUNA and best_params_from_optuna:
        print("Optunaで見つかった最適なパラメータを使用します。")
        final_model_params = {
            'objective': 'lambdarank',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1,
            **best_params_from_optuna
        }
    else:
        print("Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。")
        final_model_params = {
            'objective': 'lambdarank',
            'metric': 'ndcg',
            'n_estimators': 100,
            'learning_rate': 0.05,
            'num_leaves': 31,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1,
        }

    optimized_lgbm_ranker = lgb.LGBMRanker(**final_model_params)

    eval_set_for_final_model = []
    eval_group_for_final_model = []
    callbacks_for_final_model = []

    if test_data_is_ready:
        eval_set_for_final_model = [(X_test, y_test)]
        eval_group_for_final_model = [group_test]
        # 最終モデル学習時にも早期停止を適用
        callbacks_for_final_model.append(lgb.early_stopping(10, verbose=True))

    optimized_lgbm_ranker.fit(
        X_train,
        y_train,
        group=group_train,
        eval_set=eval_set_for_final_model if eval_set_for_final_model else None,
        eval_group=eval_group_for_final_model if eval_group_for_final_model else None,
        categorical_feature=categorical_features,
        callbacks=callbacks_for_final_model if callbacks_for_final_model else None
    )
    print("最終モデルの学習が完了しました。")
else:
    print("学習データが不十分なため、モデル学習は実行されませんでした。")

# --- 学習済み最終モデルでテストデータの予測 ---
if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, "fitted_") and optimized_lgbm_ranker.fitted_ and test_data_is_ready:
    print("\n最終モデルでテストデータの予測を行います...")

    # test_data_with_predictions の準備
    if 'test_df' in globals() and not test_df.empty:
        test_data_with_predictions = test_df.copy()
    else:
        print("警告: test_df が存在しないか空のため、予測結果を格納するDataFrameを作成できません。")
        test_data_with_predictions = pd.DataFrame()

    if not test_data_with_predictions.empty:
        predicted_scores_on_test = optimized_lgbm_ranker.predict(X_test)
        test_data_with_predictions['predicted_score'] = predicted_scores_on_test
        test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')

        print("\n予測結果（テストデータ）:")
        # y_label_column_name と actual_rank_col が存在するか確認
        display_cols = ['race_id', 'horse_id', 'predicted_score', 'predicted_rank']
        if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:
            display_cols.append(y_label_column_name)
        if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:
            display_cols.append(actual_rank_col)
        
        # 実際に存在するカラムのみを選択して表示
        valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]
        print(test_data_with_predictions[valid_display_cols].head())

        # (オプション) 最終モデルの評価
        # ここでは予測スコアの表示に留めます。NDCG等の詳細な評価は別途関数を用意することを推奨します。
        print("\nテストデータでの評価 (注意: これは簡易的な表示であり、NDCG等の指標計算は別途必要です)")

elif not test_data_is_ready:
    print("\nテストデータがないため、予測はスキップされました。")
else: # モデルが学習されなかった場合
    print("\nモデルが学習されていないため、テストデータの予測はスキップされました。")


# --- 5. 評価 ---
if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'best_score_') and optimized_lgbm_ranker.best_score_:
    print("\nモデル評価 (検証データ):")
    if 'valid_0' in optimized_lgbm_ranker.best_score_:
        for eval_metric_name, value in optimized_lgbm_ranker.best_score_['valid_0'].items():
            print(f"  {eval_metric_name}: {value:.4f}")
    else:
        print("  検証データでのスコアは記録されていません。fit時にeval_setが指定されなかったか、早期停止が作動しなかった可能性があります。")
elif optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'fitted_') and optimized_lgbm_ranker.fitted_:
     print("\nモデルは学習されましたが、検証スコア (best_score_) は利用できません。")

# 特徴量の重要度
if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'feature_importances_') and hasattr(optimized_lgbm_ranker, 'fitted_') and optimized_lgbm_ranker.fitted_:
    if 'feature_columns' in globals() and len(feature_columns) == len(optimized_lgbm_ranker.feature_importances_):
        feature_importance_df = pd.DataFrame({
            'feature_name': feature_columns,
            'importance_score': optimized_lgbm_ranker.feature_importances_
        }).sort_values('importance_score', ascending=False)
        print("\n特徴量の重要度:")
        print(feature_importance_df.head())
    else:
        print("\n特徴量の重要度を表示できませんでした。feature_columnsの定義またはモデルの学習状態を確認してください。")



feature_importance_df

