{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 改善版：競馬データ一括処理Notebook\n", "\n", "このNotebookは、最新の統合プロセッサクラスを使用して、\n", "複数年分のbinファイルをDataFrameに変換し、レース情報と馬情報を統合処理します。\n", "\n", "## 主な改善点\n", "- 最新の`RaceHorseTargetedProcessor`を使用\n", "- エラーハンドリングとログ機能の強化\n", "- 並列処理の最適化\n", "- 進捗表示の改善\n", "- 設定の外部化\n", "- 結果の検証機能"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import logging\n", "from pathlib import Path\n", "from typing import Dict, List, Optional, Any\n", "import json\n", "from datetime import datetime\n", "\n", "# 最新のプロセッサクラスをインポート\n", "from module.race_batch_processor import process_race_bin_to_pickle_batch\n", "from module.race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "from module.race_data_processor import RaceProcessor\n", "from module.data_merger import DataMerger\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["処理対象年: ['2020', '2021', '2022', '2023', '2024']\n", "データディレクトリ: f:\\keiba__AI_2025\\data\\html\\race\\race_by_year\n", "出力ディレクトリ: output\n"]}], "source": ["# 処理したい年のリストを指定\n", "years = ['2020', '2021', '2022', '2023', '2024']  # 必要な年を追加してください\n", "# 単一年の場合: years = ['2024']\n", "# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']\n", "\n", "# データディレクトリの設定\n", "base_dir = Path.cwd()  # 現在のディレクトリを基準\n", "data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'\n", "\n", "# 出力ディレクトリの作成\n", "output_dir = Path('output')\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "print(f\"処理対象年: {years}\")\n", "print(f\"データディレクトリ: {data_dir}\")\n", "print(f\"出力ディレクトリ: {output_dir}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["processor = RaceProcessor()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 各年のbinファイルをDataFrameに変換し、pickleで保存"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:00:32,294 - root - INFO - 2019年のbinファイル処理を開始\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2019年のbinファイルを処理中...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2019年binファイル処理: 100%|██████████| 3452/3452 [09:04<00:00,  6.33it/s]\n", "2025-05-29 11:09:38,584 - root - INFO - race_info_2019.pickle を保存 (3452件)\n", "2025-05-29 11:09:38,709 - root - INFO - race_results_2019.pickle を保存 (47574件)\n", "2025-05-29 11:09:38,710 - root - INFO - 2019年の処理完了\n", "2025-05-29 11:09:38,711 - root - INFO - 全ての年の処理が完了しました。\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  race_info_2019.pickle を保存\n", "  race_results_2019.pickle を保存\n", "2019年のpickle保存完了\n", "\n", "全ての年の処理が完了しました。\n"]}], "source": ["import module.race_batch_processor\n", "# 使用例1: binファイルからpickleファイルへの変換\n", "years = [\"2019\"]\n", "module.race_batch_processor.process_race_bin_to_pickle_batch(\n", "    years=years,\n", "    bin_base_dir=data_dir,\n", "    output_dir=output_dir,\n", "    parallel=True,\n", "    max_workers=4\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-28 16:00:12,717 - module.horse_processor - INFO - 馬HTMLファイルのインデックスを構築中...\n", "2025-05-28 16:00:12,869 - module.horse_processor - INFO - 馬HTMLファイルのインデックス構築完了。52246件 (0.15秒)\n", "2025-05-28 16:00:12,871 - module.race_horse_targeted_processor - INFO - レース→馬情報の統合処理を開始\n", "2025-05-28 16:00:12,871 - module.race_horse_targeted_processor - INFO - レース結果から馬IDを抽出しています...\n", "2025-05-28 16:00:12,872 - module.refactored_scrap - INFO - レースデータから馬IDを抽出します...\n", "2025-05-28 16:00:12,873 - module.refactored_scrap - INFO - キャッシュからデータを読み込みます: data/cache\\horse_ids_2022.pkl\n", "2025-05-28 16:00:12,883 - module.refactored_scrap - INFO - キャッシュから読み込んだ馬ID数: 11557\n", "2025-05-28 16:00:12,885 - module.race_horse_targeted_processor - INFO - 抽出完了: 11557頭の馬ID\n", "2025-05-28 16:00:12,885 - module.race_horse_targeted_processor - INFO - 11557頭の馬情報を処理開始\n", "2025-05-28 16:00:12,887 - module.race_horse_targeted_processor - INFO - 馬基本情報処理をサブミット...\n", "2025-05-28 16:00:12,890 - module.horse_processor - INFO - 11557頭の馬基本情報を処理中...\n", "2025-05-28 16:00:12,890 - module.race_horse_targeted_processor - INFO - 馬過去成績処理をサブミット...\n", "2025-05-28 16:00:12,892 - module.horse_processor - INFO - 馬基本情報HTMLファイルのインデックスを利用してパスを特定中...\n", "2025-05-28 16:00:12,894 - module.horse_processor - INFO - 11557頭の馬過去成績を処理中...\n", "2025-05-28 16:00:12,908 - module.horse_processor - INFO - 馬過去成績HTMLファイルのインデックスを利用してパスを特定中...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4e14d14f38dd44dc824843d6987abaa0", "version_major": 2, "version_minor": 0}, "text/plain": ["馬基本情報HTMLファイル特定(インデックス利用):   0%|          | 0/11557 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb1ba0be9edb4915ae87efb72080739c", "version_major": 2, "version_minor": 0}, "text/plain": ["対象馬情報処理(並列):   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eef543acfdba46f6960046720c79a743", "version_major": 2, "version_minor": 0}, "text/plain": ["馬過去成績HTMLファイル特定(インデックス利用):   0%|          | 0/11557 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-28 16:00:14,052 - module.horse_processor - INFO - 11557個のHTMLファイルを処理\n", "2025-05-28 16:00:14,053 - module.horse_processor - INFO - 馬の過去成績テーブルを準備中...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "efbb336612954ea7b70ccedcdc7ca424", "version_major": 2, "version_minor": 0}, "text/plain": ["馬過去成績HTMLパース(並列):   0%|          | 0/11557 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-28 16:00:17,808 - module.horse_processor - INFO - 11557個のHTMLファイルを処理 (馬基本情報)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d5e577c0868f47a1ac8b35549723f196", "version_major": 2, "version_minor": 0}, "text/plain": ["馬基本情報処理(並列):   0%|          | 0/11557 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from module.race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "\n", "# プロセッサ作成\n", "processor = RaceHorseTargetedProcessor()\n", "\n", "# 2024年のレースから馬IDを抽出し、馬情報を処理\n", "result = processor.process_race_to_horses(\n", "    year=\"2022\",\n", "    include_basic_info=True,\n", "    include_results=True,\n", "    parallel=True,\n", "    max_workers=4,\n", "    save_output=True\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "race_id", "rawType": "object", "type": "string"}, {"name": "開催", "rawType": "object", "type": "string"}, {"name": "レース名", "rawType": "object", "type": "string"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "course_len", "rawType": "int64", "type": "integer"}, {"name": "weather", "rawType": "object", "type": "string"}, {"name": "race_type", "rawType": "object", "type": "string"}, {"name": "ground_state", "rawType": "object", "type": "string"}, {"name": "around", "rawType": "object", "type": "string"}], "ref": "6eb090dc-c8b4-4e02-b2b1-c7d13c9fc43f", "rows": [["0", "202401010101", "01", "2歳未勝利", "2024年7月20日", "1200", "晴", "芝", "良", ""], ["1", "202401010102", "01", "3歳未勝利", "2024年7月20日", "1000", "晴", "ダート", "良", ""], ["2", "202401010104", "01", "3歳未勝利", "2024年7月20日", "1500", "晴", "芝", "良", ""], ["3", "202401010103", "01", "3歳未勝利", "2024年7月20日", "1700", "晴", "ダート", "良", ""], ["4", "202401010105", "01", "2歳新馬", "2024年7月20日", "1700", "晴", "ダート", "良", ""], ["5", "202401010106", "01", "3歳未勝利", "2024年7月20日", "1800", "晴", "芝", "良", ""], ["6", "202401010107", "01", "3歳以上1勝クラス", "2024年7月20日", "1200", "晴", "芝", "良", ""], ["7", "202401010108", "01", "3歳以上1勝クラス", "2024年7月20日", "1700", "晴", "ダート", "良", ""], ["8", "202401010109", "01", "北辰特別(1勝)", "2024年7月20日", "1800", "晴", "芝", "良", ""], ["9", "202401010110", "01", "釧路湿原特別(2勝)", "2024年7月20日", "2000", "晴", "芝", "良", ""], ["10", "202401010111", "01", "TVh賞(3勝)", "2024年7月20日", "1200", "晴", "芝", "良", ""], ["11", "202401010112", "01", "3歳以上1勝クラス", "2024年7月20日", "1700", "晴", "ダート", "良", ""], ["12", "202401010201", "01", "2歳未勝利", "2024年7月21日", "1000", "晴", "ダート", "良", ""], ["13", "202401010202", "01", "3歳未勝利", "2024年7月21日", "1700", "晴", "ダート", "良", ""], ["14", "202401010203", "01", "3歳未勝利", "2024年7月21日", "1200", "晴", "芝", "良", ""], ["15", "202401010205", "01", "2歳新馬", "2024年7月21日", "1800", "晴", "芝", "良", ""], ["16", "202401010204", "01", "3歳未勝利", "2024年7月21日", "1700", "晴", "ダート", "良", ""], ["17", "202401010206", "01", "3歳未勝利", "2024年7月21日", "2000", "晴", "芝", "良", ""], ["18", "202401010207", "01", "3歳以上1勝クラス", "2024年7月21日", "1000", "晴", "ダート", "良", ""], ["19", "202401010208", "01", "3歳以上1勝クラス", "2024年7月21日", "2000", "晴", "芝", "良", ""], ["20", "202401010209", "01", "HBC賞(2勝)", "2024年7月21日", "1200", "晴", "芝", "良", ""], ["21", "202401010210", "01", "大倉山特別(2勝)", "2024年7月21日", "1700", "晴", "ダート", "良", ""], ["22", "202401010211", "01", "しらかばステークス(OP)", "2024年7月21日", "1200", "晴", "芝", "良", ""], ["23", "202401010212", "01", "3歳以上1勝クラス", "2024年7月21日", "1500", "晴", "芝", "良", ""], ["24", "202401010301", "01", "2歳未勝利", "2024年7月27日", "1800", "曇", "芝", "良", ""], ["25", "202401010302", "01", "3歳未勝利", "2024年7月27日", "1700", "曇", "ダート", "良", ""], ["26", "202401010303", "01", "3歳未勝利", "2024年7月27日", "2000", "曇", "芝", "良", ""], ["27", "202401010304", "01", "3歳未勝利", "2024年7月27日", "1700", "曇", "ダート", "良", ""], ["28", "202401010305", "01", "2歳新馬", "2024年7月27日", "1500", "曇", "芝", "良", ""], ["29", "202401010306", "01", "3歳以上1勝クラス", "2024年7月27日", "1000", "曇", "ダート", "良", ""], ["30", "202401010307", "01", "3歳以上1勝クラス", "2024年7月27日", "2000", "曇", "芝", "良", ""], ["31", "202401010308", "01", "3歳以上1勝クラス", "2024年7月27日", "1700", "曇", "ダート", "良", ""], ["32", "202401010309", "01", "積丹特別(1勝)", "2024年7月27日", "2600", "曇", "芝", "良", ""], ["33", "202401010310", "01", "摩周湖特別(2勝)", "2024年7月27日", "1500", "曇", "芝", "良", ""], ["34", "202401010311", "01", "STV賞(3勝)", "2024年7月27日", "2000", "曇", "芝", "良", ""], ["35", "202401010312", "01", "3歳以上2勝クラス", "2024年7月27日", "1000", "曇", "ダート", "良", ""], ["36", "202401010401", "01", "3歳未勝利", "2024年7月28日", "1500", "雨", "芝", "重", ""], ["37", "202401010402", "01", "3歳未勝利", "2024年7月28日", "1000", "曇", "ダート", "不良", ""], ["38", "202401010403", "01", "3歳未勝利", "2024年7月28日", "1700", "曇", "ダート", "不良", ""], ["39", "202401010404", "01", "3歳未勝利", "2024年7月28日", "2000", "曇", "芝", "重", ""], ["40", "202401010405", "01", "2歳新馬", "2024年7月28日", "1800", "曇", "芝", "重", ""], ["41", "202401010406", "01", "3歳以上1勝クラス", "2024年7月28日", "1700", "曇", "ダート", "不良", ""], ["42", "202401010407", "01", "3歳以上1勝クラス", "2024年7月28日", "1800", "曇", "芝", "稍重", ""], ["43", "202401010408", "01", "3歳以上1勝クラス", "2024年7月28日", "1200", "曇", "芝", "稍重", ""], ["44", "202401010409", "01", "阿寒湖特別(2勝)", "2024年7月28日", "2600", "曇", "芝", "稍重", ""], ["45", "202401010410", "01", "ポプラステークス(3勝)", "2024年7月28日", "1700", "晴", "ダート", "重", ""], ["46", "202401010411", "01", "第72回クイーンステークス(GIII)", "2024年7月28日", "1800", "晴", "芝", "稍重", ""], ["47", "202401010412", "01", "3歳以上2勝クラス", "2024年7月28日", "1700", "曇", "ダート", "重", ""], ["48", "202401010501", "01", "2歳未勝利", "2024年8月3日", "1500", "曇", "芝", "良", ""], ["49", "202401010502", "01", "3歳未勝利", "2024年8月3日", "1700", "曇", "ダート", "良", ""]], "shape": {"columns": 9, "rows": 3454}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>race_id</th>\n", "      <th>開催</th>\n", "      <th>レース名</th>\n", "      <th>date</th>\n", "      <th>course_len</th>\n", "      <th>weather</th>\n", "      <th>race_type</th>\n", "      <th>ground_state</th>\n", "      <th>around</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>202401010101</td>\n", "      <td>01</td>\n", "      <td>2歳未勝利</td>\n", "      <td>2024年7月20日</td>\n", "      <td>1200</td>\n", "      <td>晴</td>\n", "      <td>芝</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>202401010102</td>\n", "      <td>01</td>\n", "      <td>3歳未勝利</td>\n", "      <td>2024年7月20日</td>\n", "      <td>1000</td>\n", "      <td>晴</td>\n", "      <td>ダート</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>202401010104</td>\n", "      <td>01</td>\n", "      <td>3歳未勝利</td>\n", "      <td>2024年7月20日</td>\n", "      <td>1500</td>\n", "      <td>晴</td>\n", "      <td>芝</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>202401010103</td>\n", "      <td>01</td>\n", "      <td>3歳未勝利</td>\n", "      <td>2024年7月20日</td>\n", "      <td>1700</td>\n", "      <td>晴</td>\n", "      <td>ダート</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>202401010105</td>\n", "      <td>01</td>\n", "      <td>2歳新馬</td>\n", "      <td>2024年7月20日</td>\n", "      <td>1700</td>\n", "      <td>晴</td>\n", "      <td>ダート</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3449</th>\n", "      <td>202410030808</td>\n", "      <td>10</td>\n", "      <td>3歳以上1勝クラス</td>\n", "      <td>2024年7月21日</td>\n", "      <td>1000</td>\n", "      <td>晴</td>\n", "      <td>ダート</td>\n", "      <td>稍重</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3450</th>\n", "      <td>202410030809</td>\n", "      <td>10</td>\n", "      <td>宗像特別(2勝)</td>\n", "      <td>2024年7月21日</td>\n", "      <td>2000</td>\n", "      <td>曇</td>\n", "      <td>芝</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>202410030810</td>\n", "      <td>10</td>\n", "      <td>九州スポーツ杯(2勝)</td>\n", "      <td>2024年7月21日</td>\n", "      <td>1200</td>\n", "      <td>曇</td>\n", "      <td>芝</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>202410030811</td>\n", "      <td>10</td>\n", "      <td>第72回中京記念(GIII)</td>\n", "      <td>2024年7月21日</td>\n", "      <td>1800</td>\n", "      <td>晴</td>\n", "      <td>芝</td>\n", "      <td>良</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>202410030812</td>\n", "      <td>10</td>\n", "      <td>3歳以上1勝クラス</td>\n", "      <td>2024年7月21日</td>\n", "      <td>1700</td>\n", "      <td>晴</td>\n", "      <td>ダート</td>\n", "      <td>稍重</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3454 rows × 9 columns</p>\n", "</div>"], "text/plain": ["           race_id  開催            レース名        date  course_len weather  \\\n", "0     202401010101  01           2歳未勝利  2024年7月20日        1200       晴   \n", "1     202401010102  01           3歳未勝利  2024年7月20日        1000       晴   \n", "2     202401010104  01           3歳未勝利  2024年7月20日        1500       晴   \n", "3     202401010103  01           3歳未勝利  2024年7月20日        1700       晴   \n", "4     202401010105  01            2歳新馬  2024年7月20日        1700       晴   \n", "...            ...  ..             ...         ...         ...     ...   \n", "3449  202410030808  10       3歳以上1勝クラス  2024年7月21日        1000       晴   \n", "3450  202410030809  10        宗像特別(2勝)  2024年7月21日        2000       曇   \n", "3451  202410030810  10     九州スポーツ杯(2勝)  2024年7月21日        1200       曇   \n", "3452  202410030811  10  第72回中京記念(GIII)  2024年7月21日        1800       晴   \n", "3453  202410030812  10       3歳以上1勝クラス  2024年7月21日        1700       晴   \n", "\n", "     race_type ground_state around  \n", "0            芝            良         \n", "1          ダート            良         \n", "2            芝            良         \n", "3          ダート            良         \n", "4          ダート            良         \n", "...        ...          ...    ...  \n", "3449       ダート           稍重         \n", "3450         芝            良         \n", "3451         芝            良         \n", "3452         芝            良         \n", "3453       ダート           稍重         \n", "\n", "[3454 rows x 9 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["race_info = pd.read_pickle(output_dir / \"race_info_2024.pickle\")\n", "race_info"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "着順", "rawType": "object", "type": "unknown"}, {"name": "枠番", "rawType": "int64", "type": "integer"}, {"name": "馬番", "rawType": "int64", "type": "integer"}, {"name": "馬名", "rawType": "object", "type": "string"}, {"name": "性齢", "rawType": "object", "type": "string"}, {"name": "斤量", "rawType": "float64", "type": "float"}, {"name": "騎手", "rawType": "object", "type": "string"}, {"name": "タイム", "rawType": "object", "type": "unknown"}, {"name": "着差", "rawType": "object", "type": "unknown"}, {"name": "ﾀｲﾑ指数", "rawType": "object", "type": "unknown"}, {"name": "通過", "rawType": "object", "type": "unknown"}, {"name": "上り", "rawType": "float64", "type": "float"}, {"name": "単勝", "rawType": "object", "type": "unknown"}, {"name": "人気", "rawType": "float64", "type": "float"}, {"name": "馬体重", "rawType": "object", "type": "string"}, {"name": "調教ﾀｲﾑ", "rawType": "float64", "type": "float"}, {"name": "厩舎ｺﾒﾝﾄ", "rawType": "float64", "type": "float"}, {"name": "備考", "rawType": "float64", "type": "float"}, {"name": "調教師", "rawType": "object", "type": "string"}, {"name": "馬主", "rawType": "object", "type": "string"}, {"name": "賞金(万円)", "rawType": "float64", "type": "float"}, {"name": "race_id", "rawType": "object", "type": "string"}, {"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "jockey_id", "rawType": "object", "type": "string"}, {"name": "trainer_id", "rawType": "object", "type": "string"}], "ref": "bd13e0ad-2a2e-497f-b45a-9a0c84fbbe18", "rows": [["0", "1", "5", "5", "ポッドベイダー", "牡2", "55.0", "佐々木大", "1:08.8", null, "**", "1-1", "33.9", "1.2", "1.0", "462(-2)", null, null, null, "[東] 上原佑紀", "小川眞査雄", "550.0", "202401010101", "2022105244", "01197", "01192"], ["1", "2", "2", "2", "ニシノクードクール", "牝2", "55.0", "武藤雅", "1:09.1", "1.3/4", "**", "2-2", "34.1", "10.2", "4.0", "452(-2)", null, null, null, "[東] 武藤善則", "西山茂行", "220.0", "202401010101", "2022106999", "01169", "01064"], ["2", "3", "3", "3", "ロードヴェルト", "牡2", "55.0", "横山武史", "1:09.4", "1.3/4", "**", "3-3", "34.0", "7.9", "3.0", "416(+6)", null, null, null, "[西] 牧浦充徳", "ロードホースクラブ", "140.0", "202401010101", "2022100639", "01170", "01113"], ["3", "4", "1", "1", "ルージュアマリア", "牝2", "55.0", "永野猛蔵", "1:10.0", "3.1/2", "**", "5-5", "34.4", "5.9", "2.0", "410(+6)", null, null, null, "[東] 黒岩陽一", "東京ホースレーシング", "83.0", "202401010101", "2022105762", "01188", "01133"], ["4", "5", "4", "4", "ロードヴァルカン", "牡2", "54.0", "角田大河", "1:10.1", "クビ", "**", "3-3", "34.7", "21.3", "5.0", "438(-2)", null, null, null, "[西] 中村直也", "ロードホースクラブ", "55.0", "202401010101", "2022100660", "01199", "01186"], ["5", "1", "2", "2", "タミゼ", "牝3", "55.0", "北村友一", "0:59.5", null, "**", "3-3", "35.5", "3.9", "2.0", "474(-2)", null, null, null, "[西] 平田修", "水上行雄", "550.0", "202401010102", "2021100650", "01102", "01082"], ["6", "2", "5", "6", "タマモアルタイル", "牡3", "57.0", "武豊", "1:00.0", "3", "**", "5-3", "35.9", "9.4", "6.0", "494(+4)", null, null, null, "[東] 水野貴広", "タマモ", "220.0", "202401010102", "2021101558", "00666", "01094"], ["7", "3", "6", "8", "ウィスピースノー", "牝3", "54.0", "角田大河", "1:00.1", "クビ", "**", "8-8", "35.3", "44.4", "9.0", "434(0)", null, null, null, "[西] 今野貞一", "水上ふじ子", "140.0", "202401010102", "2021100648", "01199", "01128"], ["8", "4", "6", "7", "スキーサンダー", "牝3", "52.0", "小林勝太", "1:00.1", "クビ", "**", "2-2", "36.2", "3.9", "1.0", "474(-2)", null, null, null, "[東] 辻哲英", "ゴドルフィン", "83.0", "202401010102", "2021101653", "01205", "01182"], ["9", "5", "7", "10", "ゴールドタリスマン", "牝3", "55.0", "武藤雅", "1:00.2", "クビ", "**", "6-6", "35.8", "90.0", "10.0", "424(+8)", null, null, null, "[東] 稲垣幸雄", "高昭牧場", "55.0", "202401010102", "2021100728", "01169", "01167"], ["10", "6", "3", "3", "コパマエチャン", "牝3", "52.0", "長浜鴻緒", "1:00.2", "アタマ", "**", "1-1", "36.4", "16.0", "8.0", "476(0)", null, null, null, "[東] 大和田成", "小林祥晃", null, "202401010102", "2021104459", "01214", "01124"], ["11", "7", "4", "4", "スリーボビー", "牡3", "57.0", "永野猛蔵", "1:00.2", "クビ", "**", "9-9", "35.1", "14.1", "7.0", "482(-4)", null, null, null, "[東] 伊藤圭三", "永井商事", null, "202401010102", "2021103742", "01188", "01023"], ["12", "8", "8", "12", "メトゥス", "牡3", "57.0", "佐々木大", "1:00.4", "1", "**", "6-6", "36.0", "6.1", "4.0", "458(+6)", null, null, null, "[東] 堀内岳志", "中村祐子", null, "202401010102", "2021101803", "01197", "01189"], ["13", "9", "7", "9", "ブートストラップ", "セ3", "54.0", "川端海翼", "1:01.1", "4", "**", "12-12", "35.6", "167.0", "11.0", "486(-6)", null, null, null, "[東] 本間忍", "梶原大輔", null, "202401010102", "2021103998", "01195", "01056"], ["14", "10", "8", "11", "クミンフレイバー", "牝3", "55.0", "鮫島克駿", "1:01.2", "1/2", "**", "3-3", "37.2", "8.2", "5.0", "438(+4)", null, null, null, "[東] 松永康利", "尾田信夫", null, "202401010102", "2021107237", "01157", "01093"], ["15", "11", "1", "1", "ロマンスヒコー", "牡3", "57.0", "大野拓弥", "1:01.4", "1.1/4", "**", "9-9", "36.3", "172.5", "12.0", "444(-8)", null, null, null, "[東] 佐藤吉勝", "片山博志", null, "202401010102", "2021102364", "01096", "01035"], ["16", "12", "5", "5", "タイセイフォルテ", "牡3", "57.0", "横山武史", "1:01.8", "2.1/2", "**", "11-11", "36.5", "5.7", "3.0", "486(0)", null, null, null, "[西] 西村真幸", "田中成奉", null, "202401010102", "2021105345", "01170", "01148"], ["17", "1", "5", "8", "アリーバ", "牝3", "55.0", "浜中俊", "1:28.6", null, "**", "10-10-4", "34.4", "5.0", "4.0", "424(+6)", null, null, null, "[西] 田中克典", "リーヴァ", "550.0", "202401010104", "2021105390", "01115", "01180"], ["18", "2", "3", "4", "カーモス", "牝3", "52.0", "長浜鴻緒", "1:28.6", "クビ", "**", "2-2-2", "34.9", "40.4", "11.0", "428(+2)", null, null, null, "[西] 高橋義忠", "社台レースホース", "220.0", "202401010104", "2021105033", "01214", "01129"], ["19", "3", "6", "10", "ユメカウツツカ", "牝3", "52.0", "小林勝太", "1:28.7", "1/2", "**", "11-13-7", "34.3", "28.2", "9.0", "392(+10)", null, null, null, "[西] 小栗実", "岡浩二", "140.0", "202401010104", "2021103861", "01205", "01194"], ["20", "4", "4", "5", "アウストラリス", "牝3", "54.0", "角田大河", "1:28.9", "1.1/4", "**", "5-4-4", "35.1", "22.8", "8.0", "422(-4)", null, null, null, "[東] 高橋文雅", "石井輝昭", "83.0", "202401010104", "2021103030", "01199", "01134"], ["21", "5", "4", "6", "ツキガキレイデスネ", "牝3", "55.0", "永野猛蔵", "1:29.0", "1/2", "**", "11-11-12", "34.7", "18.2", "7.0", "456(+4)", null, null, null, "[東] 小島茂之", "ミルファーム", "55.0", "202401010104", "2021103328", "01188", "01068"], ["22", "6", "8", "14", "ディアアリーチェ", "牝3", "55.0", "鮫島克駿", "1:29.1", "1/2", "**", "5-7-10", "35.0", "4.3", "1.0", "450(-4)", null, null, null, "[西] 武幸四郎", "ＴＮレーシング", null, "202401010104", "2021105655", "01157", "01160"], ["23", "7", "5", "7", "タイプフェイス", "牝3", "55.0", "大野拓弥", "1:29.1", "ハナ", "**", "13-11-12", "34.8", "39.3", "10.0", "442(+8)", null, null, null, "[東] 小手川準", "社台レースホース", null, "202401010104", "2021105091", "01096", "01171"], ["24", "8", "6", "9", "カウネウス", "牝3", "55.0", "丹内祐次", "1:29.1", "ハナ", "**", "14-14-12", "34.5", "18.2", "6.0", "446(+2)", null, null, null, "[東] 宗像義忠", "サンデーレーシング", null, "202401010104", "2021105487", "01091", "00420"], ["25", "9", "7", "11", "セントオブシン", "牝3", "55.0", "亀田温心", "1:29.2", "クビ", "**", "8-7-7", "35.2", "53.5", "12.0", "426(-4)", null, null, null, "[西] 高野友和", "中村祐子", null, "202401010104", "2021100146", "01176", "01117"], ["26", "10", "8", "13", "ラーリア", "牝3", "55.0", "横山武史", "1:29.2", "アタマ", "**", "4-4-4", "35.4", "4.8", "3.0", "396(-6)", null, null, null, "[東] 大竹正博", "中村昭博", null, "202401010104", "2021101206", "01170", "01102"], ["27", "11", "2", "2", "チャルドーニ", "牝3", "55.0", "富田暁", "1:29.3", "3/4", "**", "2-2-2", "35.7", "4.4", "2.0", "464(-2)", null, null, null, "[東] 林徹", "ジャコモ", null, "202401010104", "2021104946", "01168", "01163"], ["28", "12", "7", "12", "ウーロン", "牝3", "55.0", "横山和生", "1:29.8", "3", "**", "1-1-1", "36.3", "8.5", "5.0", "480(+10)", null, null, null, "[東] 上原佑紀", "ゴドルフィン", null, "202401010104", "2021101649", "01140", "01192"], ["29", "13", "3", "3", "シャイニーマンボ", "牝3", "52.0", "川端海翼", "1:29.9", "クビ", "**", "8-7-10", "35.9", "166.7", "14.0", "470(+22)", null, null, null, "[西] 羽月友彦", "小林昌志", null, "202401010104", "2021102467", "01195", "01091"], ["30", "14", "1", "1", "アオイフォール", "牝3", "55.0", "小林凌大", "1:31.1", "7", "**", "5-4-7", "37.3", "104.7", "13.0", "454(-4)", null, null, null, "[東] 牧光二", "鈴木照雄", null, "202401010104", "2021107002", "01177", "01106"], ["31", "1", "3", "4", "メイショウソムリエ", "牡3", "57.0", "武豊", "1:46.9", null, "**", "3-3-3-2", "38.5", "3.6", "2.0", "522(+14)", null, null, null, "[東] 蛯名正義", "松本好雄", "550.0", "202401010103", "2021102980", "00666", "01191"], ["32", "2", "5", "7", "リアルペガサス", "牡3", "57.0", "菱田裕二", "1:47.6", "4", "**", "10-10-8-7", "38.6", "10.0", "6.0", "456(0)", null, null, null, "[西] 大橋勇樹", "水谷光太郎", "220.0", "202401010103", "2021103316", "01144", "01065"], ["33", "3", "4", "6", "クラウンクーロン", "牡3", "57.0", "丹内祐次", "1:47.8", "1.1/4", "**", "1-1-1-1", "39.7", "6.8", "4.0", "442(-6)", null, null, null, "[東] 牧光二", "矢野悦三", "140.0", "202401010103", "2021101266", "01091", "01106"], ["34", "4", "3", "3", "エナアニ<PERSON>", "牡3", "57.0", "横山和生", "1:47.8", "アタマ", "**", "12-12-10-9", "38.5", "9.7", "5.0", "450(+8)", null, null, null, "[西] 吉田直弘", "宮原廣伸", "83.0", "202401010103", "2021109073", "01140", "01101"], ["35", "5", "1", "1", "ハテナビト", "牡3", "54.0", "長浜鴻緒", "1:48.1", "1.3/4", "**", "6-6-6-5", "39.2", "31.9", "7.0", "462(-4)", null, null, null, "[西] 吉岡辰弥", "山口裕介", "55.0", "202401010103", "2021103371", "01214", "01176"], ["36", "6", "6", "9", "ポップスター", "牡3", "57.0", "武藤雅", "1:48.3", "1.1/4", "**", "14-14-14-14", "38.1", "214.5", "12.0", "510(-10)", null, null, null, "[東] 南田美知", "久松朋史", null, "202401010103", "2021100179", "01169", "00437"], ["37", "7", "8", "14", "ナムラミック", "牡3", "57.0", "鮫島克駿", "1:48.7", "2", "**", "8-9-6-7", "39.9", "106.0", "10.0", "484(+4)", null, null, null, "[西] 村山明", "奈村睦弘", null, "202401010103", "2021102630", "01157", "01107"], ["38", "8", "4", "5", "ニースライト", "牝3", "55.0", "柴田善臣", "1:48.7", "クビ", "**", "10-10-10-10", "39.3", "80.5", "9.0", "472(-4)", null, null, null, "[東] 粕谷昌央", "樋口正蔵", null, "202401010103", "2021102898", "00641", "01096"], ["39", "9", "2", "2", "ワカミヤノミコト", "牡3", "54.0", "小林勝太", "1:48.8", "3/4", "**", "12-13-13-11", "39.0", "257.8", "13.0", "472(0)", null, null, null, "[東] 稲垣幸雄", "芳川貴行", null, "202401010103", "2021101030", "01205", "01167"], ["40", "10", "5", "8", "スターゲット", "牡3", "57.0", "横山武史", "1:49.3", "3", "**", "2-2-2-2", "41.1", "2.5", "1.0", "458(0)", null, null, null, "[東] 中舘英二", "嶋田賢", null, "202401010103", "2021104392", "01170", "01153"], ["41", "11", "7", "11", "プラチナバンド", "牡3", "57.0", "永野猛蔵", "1:49.4", "1/2", "**", "3-3-5-5", "40.9", "59.8", "8.0", "506(-2)", null, null, null, "[東] 新開幸一", "明栄商事", null, "202401010103", "2021100045", "01188", "01116"], ["42", "12", "8", "13", "クリノアイリス", "牝3", "55.0", "古川吉洋", "1:49.5", "3/4", "**", "9-8-10-12", "40.2", "333.6", "14.0", "444(-12)", null, null, null, "[西] 谷潔", "栗本博晴", null, "202401010103", "2021102262", "01015", "00431"], ["43", "13", "6", "10", "アオレレ", "牡3", "57.0", "佐々木大", "1:50.2", "4", "**", "6-6-9-12", "41.1", "140.2", "11.0", "430(-2)", null, null, null, "[西] 鈴木孝志", "中村忠彦", null, "202401010103", "2021100298", "01197", "01111"], ["44", "14", "7", "12", "ミッキースターダム", "牡3", "57.0", "浜中俊", "1:50.4", "3/4", "**", "3-5-3-4", "42.0", "6.8", "3.0", "450(-6)", null, null, null, "[西] 音無秀孝", "野田みづき", null, "202401010103", "2021105765", "01115", "01002"], ["45", "1", "3", "4", "ナチュラルライズ", "牡2", "55.0", "横山武史", "1:45.7", null, "**", "4-4-4-2", "36.9", "3.0", "1.0", "476(0)", null, null, null, "[東] 伊藤圭三", "吉岡寛行", "720.0", "202401010105", "2022103795", "01170", "01023"], ["46", "2", "6", "10", "ベルベルコンパス", "牡2", "55.0", "北村友一", "1:46.7", "6", "**", "1-1-1-1", "38.3", "5.3", "3.0", "522(0)", null, null, null, "[西] 小栗実", "吉田和美", "290.0", "202401010105", "2022103059", "01102", "01194"], ["47", "3", "1", "1", "ジーティートゥルー", "牡2", "55.0", "大野拓弥", "1:49.0", "大", "**", "9-10-10-7", "39.0", "21.5", "8.0", "452(0)", null, null, null, "[東] 田島俊明", "田畑利彦", "180.0", "202401010105", "2022105487", "01096", "01112"], ["48", "4", "2", "2", "ソラノキャンバス", "牡2", "52.0", "長浜鴻緒", "1:49.0", "クビ", "**", "11-11-10-8", "39.0", "46.3", "10.0", "430(0)", null, null, null, "[東] 武井亮", "ＭＹレーシング", "110.0", "202401010105", "2022100083", "01214", "01147"], ["49", "5", "4", "5", "アローオブライト", "牡2", "55.0", "藤岡佑介", "1:49.3", "1.1/2", "**", "14-14-12-10", "39.1", "14.4", "7.0", "468(0)", null, null, null, "[西] 渡辺薫彦", "シルクレーシング", "72.0", "202401010105", "2022104604", "01093", "01155"]], "shape": {"columns": 25, "rows": 47181}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>着順</th>\n", "      <th>枠番</th>\n", "      <th>馬番</th>\n", "      <th>馬名</th>\n", "      <th>性齢</th>\n", "      <th>斤量</th>\n", "      <th>騎手</th>\n", "      <th>タイム</th>\n", "      <th>着差</th>\n", "      <th>ﾀｲﾑ指数</th>\n", "      <th>...</th>\n", "      <th>調教ﾀｲﾑ</th>\n", "      <th>厩舎ｺﾒﾝﾄ</th>\n", "      <th>備考</th>\n", "      <th>調教師</th>\n", "      <th>馬主</th>\n", "      <th>賞金(万円)</th>\n", "      <th>race_id</th>\n", "      <th>horse_id</th>\n", "      <th>jockey_id</th>\n", "      <th>trainer_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>ポッドベイダー</td>\n", "      <td>牡2</td>\n", "      <td>55.0</td>\n", "      <td>佐々木大</td>\n", "      <td>1:08.8</td>\n", "      <td>NaN</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[東] 上原佑紀</td>\n", "      <td>小川眞査雄</td>\n", "      <td>550.0</td>\n", "      <td>202401010101</td>\n", "      <td>2022105244</td>\n", "      <td>01197</td>\n", "      <td>01192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>ニシノクードクール</td>\n", "      <td>牝2</td>\n", "      <td>55.0</td>\n", "      <td>武藤雅</td>\n", "      <td>1:09.1</td>\n", "      <td>1.3/4</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[東] 武藤善則</td>\n", "      <td>西山茂行</td>\n", "      <td>220.0</td>\n", "      <td>202401010101</td>\n", "      <td>2022106999</td>\n", "      <td>01169</td>\n", "      <td>01064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>ロードヴェルト</td>\n", "      <td>牡2</td>\n", "      <td>55.0</td>\n", "      <td>横山武史</td>\n", "      <td>1:09.4</td>\n", "      <td>1.3/4</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 牧浦充徳</td>\n", "      <td>ロードホースクラブ</td>\n", "      <td>140.0</td>\n", "      <td>202401010101</td>\n", "      <td>2022100639</td>\n", "      <td>01170</td>\n", "      <td>01113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>ルージュアマリア</td>\n", "      <td>牝2</td>\n", "      <td>55.0</td>\n", "      <td>永野猛蔵</td>\n", "      <td>1:10.0</td>\n", "      <td>3.1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[東] 黒岩陽一</td>\n", "      <td>東京ホースレーシング</td>\n", "      <td>83.0</td>\n", "      <td>202401010101</td>\n", "      <td>2022105762</td>\n", "      <td>01188</td>\n", "      <td>01133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>ロードヴァルカン</td>\n", "      <td>牡2</td>\n", "      <td>54.0</td>\n", "      <td>角田大河</td>\n", "      <td>1:10.1</td>\n", "      <td>クビ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 中村直也</td>\n", "      <td>ロードホースクラブ</td>\n", "      <td>55.0</td>\n", "      <td>202401010101</td>\n", "      <td>2022100660</td>\n", "      <td>01199</td>\n", "      <td>01186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47176</th>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>グランデスフィーダ</td>\n", "      <td>牡4</td>\n", "      <td>58.0</td>\n", "      <td>荻野極</td>\n", "      <td>1:46.6</td>\n", "      <td>2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 池江泰寿</td>\n", "      <td>ＤＭＭドリームクラブ</td>\n", "      <td>NaN</td>\n", "      <td>202410030812</td>\n", "      <td>2020104913</td>\n", "      <td>01160</td>\n", "      <td>01071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47177</th>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "      <td>アメリカンチーフ</td>\n", "      <td>牡3</td>\n", "      <td>54.0</td>\n", "      <td>西塚洸二</td>\n", "      <td>1:46.8</td>\n", "      <td>1.1/4</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 音無秀孝</td>\n", "      <td>吉澤ホールディングス</td>\n", "      <td>NaN</td>\n", "      <td>202410030812</td>\n", "      <td>2021110123</td>\n", "      <td>01200</td>\n", "      <td>01002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47178</th>\n", "      <td>13</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>シブースト</td>\n", "      <td>牝4</td>\n", "      <td>53.0</td>\n", "      <td>吉村誠之</td>\n", "      <td>1:49.0</td>\n", "      <td>大</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 高野友和</td>\n", "      <td>ロードホースクラブ</td>\n", "      <td>NaN</td>\n", "      <td>202410030812</td>\n", "      <td>2020100160</td>\n", "      <td>01216</td>\n", "      <td>01117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47179</th>\n", "      <td>14</td>\n", "      <td>8</td>\n", "      <td>15</td>\n", "      <td>ペイシャコパ</td>\n", "      <td>牝3</td>\n", "      <td>50.0</td>\n", "      <td>河原田菜</td>\n", "      <td>1:49.1</td>\n", "      <td>3/4</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 坂口智康</td>\n", "      <td>北所直人</td>\n", "      <td>NaN</td>\n", "      <td>202410030812</td>\n", "      <td>2021101090</td>\n", "      <td>01204</td>\n", "      <td>01170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47180</th>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>オリーボーレン</td>\n", "      <td>牝3</td>\n", "      <td>53.0</td>\n", "      <td>坂井瑠星</td>\n", "      <td>1:54.7</td>\n", "      <td>大</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[西] 藤岡健一</td>\n", "      <td>シルクレーシング</td>\n", "      <td>NaN</td>\n", "      <td>202410030812</td>\n", "      <td>2021105739</td>\n", "      <td>01163</td>\n", "      <td>01062</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>47181 rows × 25 columns</p>\n", "</div>"], "text/plain": ["       着順  枠番  馬番         馬名  性齢    斤量    騎手     タイム     着差 ﾀｲﾑ指数  ... 調教ﾀｲﾑ  \\\n", "0       1   5   5    ポッドベイダー  牡2  55.0  佐々木大  1:08.8    NaN    **  ...   NaN   \n", "1       2   2   2  ニシノクードクール  牝2  55.0   武藤雅  1:09.1  1.3/4    **  ...   NaN   \n", "2       3   3   3    ロードヴェルト  牡2  55.0  横山武史  1:09.4  1.3/4    **  ...   NaN   \n", "3       4   1   1   ルージュアマリア  牝2  55.0  永野猛蔵  1:10.0  3.1/2    **  ...   NaN   \n", "4       5   4   4   ロードヴァルカン  牡2  54.0  角田大河  1:10.1     クビ    **  ...   NaN   \n", "...    ..  ..  ..        ...  ..   ...   ...     ...    ...   ...  ...   ...   \n", "47176  11   4   7  グランデスフィーダ  牡4  58.0   荻野極  1:46.6      2    **  ...   NaN   \n", "47177  12   6  10   アメリカンチーフ  牡3  54.0  西塚洸二  1:46.8  1.1/4    **  ...   NaN   \n", "47178  13   5   8      シブースト  牝4  53.0  吉村誠之  1:49.0      大    **  ...   NaN   \n", "47179  14   8  15     ペイシャコパ  牝3  50.0  河原田菜  1:49.1    3/4    **  ...   NaN   \n", "47180  15   1   1    オリーボーレン  牝3  53.0  坂井瑠星  1:54.7      大    **  ...   NaN   \n", "\n", "       厩舎ｺﾒﾝﾄ  備考       調教師          馬主  賞金(万円)       race_id    horse_id  \\\n", "0         NaN NaN  [東] 上原佑紀       小川眞査雄   550.0  202401010101  2022105244   \n", "1         NaN NaN  [東] 武藤善則        西山茂行   220.0  202401010101  2022106999   \n", "2         NaN NaN  [西] 牧浦充徳   ロードホースクラブ   140.0  202401010101  2022100639   \n", "3         NaN NaN  [東] 黒岩陽一  東京ホースレーシング    83.0  202401010101  2022105762   \n", "4         NaN NaN  [西] 中村直也   ロードホースクラブ    55.0  202401010101  2022100660   \n", "...       ...  ..       ...         ...     ...           ...         ...   \n", "47176     NaN NaN  [西] 池江泰寿  ＤＭＭドリームクラブ     NaN  202410030812  2020104913   \n", "47177     NaN NaN  [西] 音無秀孝  吉澤ホールディングス     NaN  202410030812  2021110123   \n", "47178     NaN NaN  [西] 高野友和   ロードホースクラブ     NaN  202410030812  2020100160   \n", "47179     NaN NaN  [西] 坂口智康        北所直人     NaN  202410030812  2021101090   \n", "47180     NaN NaN  [西] 藤岡健一    シルクレーシング     NaN  202410030812  2021105739   \n", "\n", "      jockey_id trainer_id  \n", "0         01197      01192  \n", "1         01169      01064  \n", "2         01170      01113  \n", "3         01188      01133  \n", "4         01199      01186  \n", "...         ...        ...  \n", "47176     01160      01071  \n", "47177     01200      01002  \n", "47178     01216      01117  \n", "47179     01204      01170  \n", "47180     01163      01062  \n", "\n", "[47181 rows x 25 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["race_results = pd.read_pickle(output_dir / \"race_results_2024.pickle\")\n", "race_results"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "生年月日", "rawType": "object", "type": "string"}, {"name": "調教師", "rawType": "object", "type": "string"}, {"name": "馬主", "rawType": "object", "type": "string"}, {"name": "生産者", "rawType": "object", "type": "string"}, {"name": "産地", "rawType": "object", "type": "string"}, {"name": "セリ取引価格", "rawType": "object", "type": "string"}, {"name": "獲得賞金", "rawType": "object", "type": "string"}, {"name": "通算成績", "rawType": "object", "type": "string"}, {"name": "主な勝鞍", "rawType": "object", "type": "unknown"}, {"name": "近親馬", "rawType": "object", "type": "unknown"}, {"name": "horse_name", "rawType": "object", "type": "string"}, {"name": "trainer_id", "rawType": "object", "type": "string"}, {"name": "owner_id", "rawType": "object", "type": "string"}, {"name": "breeder_id", "rawType": "object", "type": "unknown"}, {"name": "father_name", "rawType": "object", "type": "string"}, {"name": "father_id", "rawType": "object", "type": "string"}, {"name": "mother_name", "rawType": "object", "type": "string"}, {"name": "mother_id", "rawType": "object", "type": "string"}, {"name": "mother_father_name", "rawType": "object", "type": "string"}, {"name": "mother_father_id", "rawType": "object", "type": "string"}, {"name": "sibling_ids", "rawType": "object", "type": "unknown"}, {"name": "募集情報", "rawType": "object", "type": "unknown"}], "ref": "a833ad08-9c64-4c66-907c-a786555fbf1e", "rows": [["2022103037", "2022年6月23日", "森田直行 (栗東)", "ディアレストクラブ", "ディアレストクラブ", "浦河町", "-", "72万円 (中央)", "2戦0勝 [0-0-0-2]", null, "サパテアール, ダンサクドゥーロ", "ジンガブリッツ (Jingga Blitz)", "01142", "013803", "064203", "オルフェーヴル", "2008102636", "ファーレサルティ", "2006103200", "ダンスインザダーク", "1993109188", "2019102514, 2014106091", null], ["2021101444", "2021年3月7日", "西園正都 (栗東)", "里見治", "下河辺牧場", "日高町", "-", "3,324万円 (中央) /2,210万円 (地方)", "8戦2勝 [2-2-1-3]", "23'ヤマボウシ賞(1勝クラス)", "サンライズジャスト, ショウナンアーチー", "サトノフェニックス (<PERSON><PERSON>)", "01028", "790031", "913124", "ヘニーヒューズ", "000a011155", "マーメイドティアラ", "2009102448", "シンボリクリスエス", "1999110099", "2019101668, 2018100443", null], ["2022104682", "2022年1月18日", "田中博康 (美浦)", "鈴木剛史", "ノーザンファーム", "安平町", "6,600万円 (2022年 セレクトセール)", "2,454万円 (中央)", "4戦1勝 [1-1-1-1]", "24'2歳新馬", "エスポワールの2024", "アロヒアリイ (<PERSON><PERSON><PERSON>)", "01162", "955033", "373126", "ドゥラメンテ", "2012104511", "エスポワール", "2016104514", "オルフェーヴル", "2008102636", "2024106219", null], ["2021102673", "2021年3月30日", "森澤友貴 (兵庫)", "大八木信行", "川越ファーム", "浦河町", "-", "223万円 (中央) /218万円 (地方)", "12戦2勝 [2-1-1-8]", "第28回大日本プロレスCC2一", "ラスティンガール, ラブリーガール", "ダイシンアレス (<PERSON><PERSON> Ares)", "05567", "929002", "310039", "スワーヴリチャード", "2014106083", "カラレイア", "2015104985", "エンパイアメーカー", "000a010542", "2020106349, 2022102696", null], ["2018105012", "2018年3月19日", "友道康夫 (栗東)", "金子真人ホールディングス", "ノーザンファーム", "安平町", "-", "3億2,967万円 (中央)", "13戦5勝 [5-1-4-3]", "25'京都記念(G2)", "ボレアス, キラウエア", "ヨーホーレイク (Yoho Lake)", "01061", "708800", "373126", "ディープインパクト", "2002100816", "クロウキャニオン", "2002106979", "フレンチデピュティ", "000a00013a", "2008103136, 2007103169", null], ["2021106881", "2021年3月18日", "村田一誠 (美浦)", "居城寿与", "北勝ファーム", "新冠町", "-", "223万円 (中央) /415万円 (地方)", "15戦3勝 [3-1-2-9]", "C2十一 十二", "ゴールドスピーダー, ゴールドビルダー", "ゴールドモーション (Gold Motion)", "01190", "712033", "003467", "ゴールドアクター", "2011105266", "ジェラテリア", "2009103243", "フジキセキ", "1992109618", "2020105837, 2017102005", null], ["2020105649", "2020年4月12日", "橋口慎介 (栗東)", "前田幸貴", "ノースヒルズ", "新冠町", "-", "1,753万円 (中央)", "9戦2勝 [2-1-0-6]", "23'3歳以上1勝クラス", "ポンポネット, ピリナマアナ", "モンネトワ (<PERSON>)", "01154", "117031", "810464", "キズナ", "2010105827", "ユメノトビラ", "2014110053", "<PERSON><PERSON>", "000a0111fe", "2021106651, 2022106661", null], ["2022106666", "2022年5月14日", "中竹和也 (栗東)", "前田葉子", "ノースヒルズ", "新冠町", "-", "3,305万円 (中央)", "8戦2勝 [2-2-2-2]", "25'かささぎ賞(1勝クラス)", "エントシャイデン, ビアンフェ", "ドゥアムール (<PERSON><PERSON> Am<PERSON>)", "01039", "245030", "810464", "ロードカナロア", "2008103552", "ルシュクル", "2006104724", "サクラバクシンオー", "1989108341", "2015100635, 2017101832", null], ["2019110127", "2019年4月15日", "斉藤崇史 (栗東)", "里見治", "St<PERSON> Bloodstock", "愛", "-", "1,968万円 (中央) /379万円 (地方)", "24戦4勝 [4-4-3-13]", "24'3歳以上1勝クラス", null, "サトノプリエール (<PERSON><PERSON>)", "01151", "790031", "100736", "Dark Angel", "000a0122b4", "Soul of <PERSON><PERSON>", "000a01baed", "Perfect Soul", "000a01186e", null, null], ["2020104980", "2020年4月3日", "井樋一也 (金沢)", "宮崎豊治", "秋田牧場", "新冠町", "-", "847万円 (中央)", "8戦1勝 [1-0-0-7]", "23'3歳新馬", "ジェイデン, ゴースフロイデ", "ヒメナデシコ (<PERSON><PERSON><PERSON><PERSON>)", "05781", "x0a13e", "430333", "シルバーステート", "2013105903", "ナデシコニッポン", "2010103018", "アドマイヤジャパン", "2002102324", "2019100010, 2018101405", null], ["2022104764", "2022年1月20日", "田中博康 (美浦)", "サンデーレーシング", "ノーザンファーム", "安平町", "-", "3,927万円 (中央)", "4戦2勝 [2-1-0-1]", "25'フリージア賞(1勝クラス)", "グロリアーナの2024, ティルベリー", "ヴァルキリーバース (<PERSON><PERSON><PERSON>)", "01162", "226800", "373126", "エピファネイア", "2010104155", "グロリアーナ", "2016104610", "ハーツクライ", "2001103038", "2024106302, 2023106994", "1口:125万円/40口"], ["2020102755", "2020年3月16日", "水野貴広 (美浦)", "吉田照哉", "社台ファーム", "千歳市", "-", "2,298万円 (中央)", "16戦2勝 [2-1-1-12]", "23'３歳上５００万下", "ミッドナイトミニー, アスクヒロイズガイ", "フィンガークリック (Finger Click)", "01094", "758005", "393126", "マインドユアビスケッツ", "000a01392e", "アンバーミニー", "2014105747", "ダイワメジャー", "2001103114", "2019104643, 2021104952", null], ["2021106177", "2021年3月12日", "河嶋宏樹 (栗東)", "タマモ", "小泉牧場", "新冠町", "-", "2,410万円 (中央)", "12戦2勝 [2-3-1-6]", "25'4歳以上1勝クラス", "タマモトリノ, タマモベローナ", "タマモナポリ (Tamamo Napoli)", "01198", "515800", "510339", "カレンブラックヒル", "2009106230", "タマモリド", "2011102557", "ワイルドラッシュ", "000a0003a1", "2022106199, 2019100182", null], ["2021101392", "2021年5月1日", "松浦裕之 (地方)", "石田勇", "下河辺牧場", "日高町", "2,145万円 (2023年 JRAブリーズアップセール)", "198万円 (地方)", "29戦1勝 [1-3-5-20]", "３歳Ｃ２四組", "サトノスバル, ダノンブリザード", "ファインデイ (Fine Day)", "05571", "724031", "913124", "レイデオロ", "2014106201", "コンクエストハーラネイト", "000a013898", "Harlan's Holiday", "000a010cdf", "2018100391, 2019101618", null], ["2021106107", "2021年4月11日", "上原博之 (美浦)", "鈴木義孝", "オリエント牧場", "新冠町", "-", "0万円", "5戦0勝 [0-0-0-5]", null, "プリティキャット, エレガントジェシー", "シベルクーナ (<PERSON><PERSON>)", "00423", "820009", "700331", "ダイワメジャー", "2001103114", "ファイヤードラゴン", "2015102350", "ジャングルポケット", "1998101786", "2022107221, 2020105098", null], ["2021101047", "2021年4月8日", "尾関知人 (美浦)", "湘南", "天羽牧場", "日高町", "-", "0万円", "3戦0勝 [0-0-0-3]", null, "ティカル, テイエムヒショウ", "ショウナンアラレ (<PERSON><PERSON><PERSON>)", "01103", "679800", "003341", "サトノダイヤモンド", "2013106101", "マヤノマヤ", "2006105137", "タイキシャトル", "1994109686", "2015101021, 2020101216", null], ["2022105027", "2022年1月20日", "小林真也 (栗東)", "サンデーレーシング", "ノーザンファーム", "安平町", "-", "1,625万円 (中央)", "6戦1勝 [1-0-1-4]", "24'2歳新馬", "ランプロファイア, テウメッサ", "プロクレイア (Prokleia)", "01185", "226800", "373126", "エピファネイア", "2010104155", "プロクリス", "2011104095", "キングカメハメハ", "2001103460", "2019105409, 2021105757", "1口:100万円/40口"], ["2020102775", "2020年4月29日", "田中範雄 (地方)", "（株）ビープロジェク", "社台ファーム", "千歳市", "990万円 (2022年 千葉サラブレッドセール)", "603万円 (中央) /4,660万円 (地方)", "22戦6勝 [6-4-4-8]", "菊水賞", "ヴェイルドスケール, オメガデラックス", "ベラジオソノダラブ (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "05289", "x0ab74", "393126", "ロゴタイプ", "2010103783", "ヴェイルドクリス", "2005102348", "シンボリクリスエス", "1999110099", "2012104147, 2017104677", null], ["2022105528", "2022年4月6日", "渡辺薫彦 (栗東)", "栗本依利子", "社台ファーム", "千歳市", "3,080万円 (2023年 セレクトセール)", "0万円", "3戦0勝 [0-0-0-3]", null, "フェアリープト, サンデーミラージュ", "クリノクーチャン (<PERSON><PERSON>)", "01155", "116034", "393126", "サートゥルナーリア", "2016104505", "ミセスリンゼイ", "000a0120d1", "Theatrical", "000a001bed", "2020103068, 2017104944", null], ["2021110065", "2021年3月11日", "谷潔 (栗東)", "松本好雄", "<PERSON>", "米", "-", "1,570万円 (中央)", "12戦2勝 [2-0-1-9]", "25'4歳以上1勝クラス", null, "メイショウトム (Meish<PERSON> Tom)", "00431", "523005", "850737", "パレスマリス", "000a014a1a", "Nothing But Tom", "000a01d28f", "Street Cry", "000a0103ba", null, null], ["2021110034", "2021年4月15日", "堀宣行 (美浦)", "ゴドルフィン", "Godolphin", "愛", "-", "55万円 (中央)", "3戦0勝 [0-0-0-3]", null, "リマスタリング, リエンカウンター", "アイカムインピース (I Come in Peace)", "01070", "003060", "800708", "Ribchester", "000a01353b", "Reunite", "000a01278e", "Kingmambo", "000a001d7e", "2020110059, 2018110052", null], ["2021102548", "2021年3月28日", "大島静夫 (佐賀)", "北原大史", "中脇一幸", "様似町", "-", "8万円 (地方)", "15戦0勝 [0-0-0-15]", null, "メイショウアテンの2023, メイショウクロバト", "メイショウマダケ (<PERSON><PERSON><PERSON>)", "05059", "x097fd", "510002", "メイショウサムソン", "2003102205", "メイショウアテン", "2014102976", "ヴァーミリアン", "2002100877", "2023101936, 2022102549", null], ["2022107134", "2022年5月1日", "中村直也 (栗東)", "八木良司", "八木牧場", "新冠町", "-", "1,900万円 (中央) /700万円 (地方)", "8戦2勝 [2-1-1-4]", "25'3歳1勝クラス", "タガノネクステージ, タガノジェロディ", "タガノマカシヤ (<PERSON><PERSON> Ma<PERSON>)", "01186", "859002", "700318", "アメリカンペイトリオット", "000a013bba", "タガノディーバ", "2007106606", "フジキセキ", "1992109618", "2018102386, 2016100964", null], ["2018105873", "2018年4月16日", "平山真希 (地方)", "谷口久和", "浦河育成牧場", "浦河町", "-", "1,290万円 (中央) /732万円 (地方)", "47戦8勝 [8-1-2-36]", "21'３歳上５００万下", "ミキノプリンス, ミキノグランプリ", "ミキノバスドラム (<PERSON><PERSON><PERSON> Bass Drum)", "a02a6", "918002", "500030", "ミキノバンジョー", "2007100811", "ミキノモナコ", "2004101197", "ディアブロ", "000a0000e9", "2019103418, 2011106559", null], ["2021103282", "2021年2月8日", "杉山晴紀 (栗東)", "三木正浩", "三嶋牧場", "浦河町", "-", "6,167万円 (中央)", "12戦4勝 [4-0-2-6]", "25'鳴門S(１６００万下)", "ユーヴェットシーの2024, ココロヅヨサ", "ジャスティンアース (Justin Earth)", "01157", "195031", "400018", "キズナ", "2010105827", "ユーヴェットシー", "000a01597a", "<PERSON><PERSON><PERSON>", "000a0124d5", "2024102312, 2023105041", null], ["2021100353", "2021年4月13日", "稻田彰宏 (兵庫)", "上田真澄", "ヒサイファーム", "新ひだか町", "440万円 (2022年 北海道オータムセール)", "196万円 (地方)", "25戦4勝 [4-1-3-17]", "C19", "レコルダーレ, バララララララ", "アストリット (Astrit)", "05427", "x04c13", "933027", "スワーヴリチャード", "2014106083", "ラクリモーサ", "2009105982", "ウォーエンブレム", "000a00fa35", "2016105266, 2020100336", null], ["2021102527", "2021年4月30日", "水野貴史 (地方)", "河野勇樹", "スイートファーム", "様似町", "506万円 (2022年 北海道セプテンバーセール)", "55万円 (中央) /257万円 (地方)", "10戦3勝 [3-0-0-7]", "ランチタイムチャレンジ", "ケイティベガ, ケイティレインボー", "キミハレモネード (Kimiwa Lemonade)", "a02c6", "949031", "800005", "タリスマニック", "000a013425", "ファンディータ", "2011102989", "パイロ", "000a01176a", "2020106170, 2019104239", null], ["2021109037", "2021年4月8日", "九日俊光 (佐賀)", "田中俊彰", "スプリングファーム", "青森県", "341万円 (2022年 北海道サマーセール)", "110万円 (地方)", "17戦1勝 [1-1-0-15]", "3歳C1", "イダクァイマ, ジュンスターホース", "タナバリアシハヤイ (<PERSON><PERSON>)", "05005", "126031", "933570", "ウインバリアシオン", "2008103206", "アドマイヤヒラリー", "2002100896", "<PERSON><PERSON>", "000a001ce7", "2012104515, 2014105796", null], ["2019102553", "2019年4月17日", "飯田祐史 (栗東)", "高昭牧場", "高昭牧場", "浦河町", "-", "1,541万円 (中央)", "21戦1勝 [1-1-1-18]", "21'2歳未勝利", "メイショウユウスイ, メイショウソウビ", "ゴールドローズ (Gold Rose)", "01139", "117800", "100046", "ゴールドシップ", "2009102739", "カクテルローズ", "2007106496", "タニノギムレット", "1999100226", "2018103115, 2014103798", null], ["2020102941", "2020年3月2日", "川添明弘 (金沢)", "桑田照夫", "社台ファーム", "千歳市", "-", "1,578万円 (中央) /12万円 (地方)", "36戦0勝 [0-3-5-28]", null, "カムニャック, キープカルム", "ラバタンシン (Lavatanssin)", "a02f5", "800031", "393126", "エピファネイア", "2010104155", "ダンスアミーガ", "2011104307", "サクラバクシンオー", "1989108341", "2022105402, 2021105119", null], ["2019110074", "2019年2月27日", "黒岩陽一 (美浦)", "長谷川祐司", "Three Chimneys Farm LLC & Whisper Hill Farm LLC", "米", "-", "3,065万円 (中央)", "23戦2勝 [2-1-3-17]", "22'3歳以上1勝クラス", null, "フィフティシェビー (Fifty Chevy)", "01133", "196031", "170732", "Tapit", "000a011248", "Stopchargingmaria", "000a01a807", "Tale of the Cat", "000a002276", null, null], ["2022103546", "2022年3月6日", "千田輝彦 (栗東)", "岡田牧雄", "岡田スタッド", "新ひだか町", "-", "3,375万円 (中央)", "4戦2勝 [2-1-1-0]", "25'つばき賞(1勝クラス)", "パラダイスガーデンの2024, ガーデンオブエデン", "パラディレーヌ (<PERSON><PERSON>)", "01120", "851009", "233071", "キズナ", "2010105827", "パラダイスガーデン", "2012110030", "Closing Argument", "000a011594", "2024104150, 2021103524", null], ["2020105872", "2020年3月23日", "中竹和也 (栗東)", "ローレルレーシング", "松浦牧場", "新冠町", "-", "1,597万円 (中央) /27万円 (地方)", "19戦2勝 [2-1-1-15]", "23'３歳上５００万下", "ディアグリスター, ドウディ", "ヴォードヴィル (Vaudeville)", "01039", "032800", "430316", "ルーラーシップ", "2007103143", "ストームイメージ", "2001102785", "ダンスインザダーク", "1993109188", "2010106032, 2014100786", "1口:6万円/200口"], ["2020103612", "2020年3月20日", "森山英雄 (笠松)", "山邉浩", "ノーザンファーム", "安平町", "-", "437万円 (中央) /295万円 (地方)", "16戦2勝 [2-1-0-13]", "B5組", "グランスエーニョ, レッドセニョール", "モンテイゾラ (Monte Isola)", "05071", "522030", "373126", "イスラボニータ", "2011103565", "ポルケテスエーニョ", "000a0137a3", "Medaglia d'Oro", "000a010a65", "2018105265, 2021105779", null], ["2019105541", "2019年2月17日", "石坂公一 (栗東)", "東京ホースレーシング", "ノーザンファーム", "安平町", "-", "2,920万円 (中央)", "18戦2勝 [2-3-2-11]", "24'3歳以上1勝クラス", "ルージュイストリア, レッドロムルス", "ルージュシェノン (<PERSON>)", "01166", "180800", "373126", "アジアエクスプレス", "2011110091", "レッドクラウディア", "2009106164", "アグネスタキオン", "1998101516", "2020103701, 2018105363", "1口:4.5万円/400口"], ["2021100323", "2021年3月27日", "瀬戸幸一 (岩手)", "徳増栄治", "沼田照秋", "新ひだか町", "495万円 (2022年 北海道サマーセール)", "178万円 (地方)", "32戦1勝 [1-4-6-21]", "C2二組", "サンケイスマイル, ブレイクザアイス", "ハーツケリー (<PERSON>'s Kelly)", "05481", "469033", "933029", "ベストウォーリア", "2010110127", "チェストケハーツ", "2013102496", "ハーツクライ", "2001103038", "2020100320, 2019103219", null], ["2022100318", "2022年4月30日", "蛯名利弘 (美浦)", "畑佐博", "富塚ファーム", "浦河町", "-", "0万円", "5戦0勝 [0-0-0-5]", null, "スターオブエース, ミューオン", "ブルーフラット (Blue Flat)", "01131", "078006", "900543", "ビッグアーサー", "2011103328", "ヤマカツサユリ", "2011103685", "マンハッタンカフェ", "1998101554", "2020100862, 2019102694", null], ["2022104842", "2022年2月8日", "高橋義忠 (栗東)", "シルクレーシング", "ノーザンファーム", "安平町", "-", "24万円 (地方)", "4戦0勝 [0-1-0-3]", null, "ジーナアイリスの2024, ジーナアイリスの2023", "ジーナア<PERSON>トレ<PERSON> (Geena Actress)", "01129", "506800", "373126", "ニューイヤーズデイ", "000a014589", "ジーナアイリス", "2016104547", "ゴールドアリュール", "1999106689", "2024103154, 2023107063", "1口:4万円/500口"], ["2019101680", "2019年2月20日", "竹内正洋 (美浦)", "フィールドレーシング", "下河辺牧場", "日高町", "-", "1,830万円 (中央)", "18戦2勝 [2-0-1-15]", "23'3歳以上1勝クラス", "ロイヤルスピリッツの2022, リジル", "ロイヤルソウル (Royal Soul)", "01152", "463031", "913124", "ホッコータルマエ", "2009100921", "ロイヤルスピリッツ", "2012101401", "ハーツクライ", "2001103038", "2022101468, 2021101457", null], ["2022102295", "2022年4月3日", "藤原英昭 (栗東)", "廣崎利洋ＨＤ", "ＡＳＫＳＴＵＤ", "平取町", "-", "700万円 (中央)", "4戦1勝 [1-0-1-2]", "25'3歳未勝利", "アスクビートルズ, ストレイトアスク", "マドモアゼルアスク (<PERSON><PERSON><PERSON>)", "01055", "037803", "020541", "エピファネイア", "2010104155", "ストレイトガール", "2009100301", "フジキセキ", "1992109618", "2020101026, 2021102312", null], ["2021100545", "2021年5月11日", "河内洋 (栗東)", "山本能成", "米田牧場", "新ひだか町", "-", "0万円", "2戦0勝 [0-0-0-2]", null, "ワンダービスケット, ワンダーカラフル", "ワンダージョイ (<PERSON> Joy)", "01072", "184033", "303062", "ロジャーバローズ", "2016106065", "ワンダースキー", "2007104031", "ストラヴィンスキー", "000a00fa41", "2017102750, 2020100542", null], ["2021103558", "2021年3月7日", "稲益貴弘 (船橋)", "平川浩之", "岡田スタッド", "新ひだか町", "-", "458万円 (地方)", "12戦6勝 [6-3-1-2]", "C2十一 十二", "スパーキングブルー, ミラクルブルー", "プライムブルー (Prime Blue)", "05748", "136007", "233071", "マインドユアビスケッツ", "000a01392e", "レジェンドブルー", "2006100820", "ステイゴールド", "1994108729", "2020103958, 2022103587", null], ["2021104862", "2021年2月2日", "坂口智康 (栗東)", "サンデーレーシング", "社台コーポレーション白老ファーム", "白老町", "-", "83万円 (中央) /107万円 (地方)", "5戦2勝 [2-0-0-3]", "C21組", "スーパーフェイバー, シングザットソング", "アールアンドビー (R and B)", "01170", "226800", "341126", "キズナ", "2010105827", "ザガールインザットソング", "000a013cf1", "My Golden Song", "000a013be7", "2018105506, 2020102660", "1口:150万円/40口"], ["2021100599", "2021年5月2日", "櫻田康二 (岩手)", "永田和彦", "エムエム、ヤマダファーム", "浦河町", "-", "236万円 (地方)", "16戦4勝 [4-2-2-8]", "C1", "トレゾア, ヴォランテ", "フリーセント (Fliessend)", "a024c", "875006", "010057", "マインドユアビスケッツ", "000a01392e", "ローザディアマント", "2008103280", "スペシャルウィーク", "1995103211", "2017102797, 2020100593", null], ["2022103898", "2022年3月25日", "大根田裕 (栗東)", "田頭勇貴", "静内白井牧場", "新ひだか町", "-", "550万円 (中央)", "7戦1勝 [1-0-0-6]", "24'2歳未勝利", "ムーンオンタイド, インパルスベルン", "ピーチマカロン (<PERSON><PERSON>)", "01032", "855030", "133083", "ホッコータルマエ", "2009100921", "ムーンフライト", "2011104163", "アドマイヤムーン", "2003102991", "2021103885, 2018103852", null], ["2020103464", "2020年2月11日", "新谷功一 (栗東)", "後藤和成", "ノーザンファーム", "安平町", "5,170万円 (2021年 セレクトセール)", "6,772万円 (中央)", "15戦4勝 [4-1-3-7]", "24'2024ファイナルS(3勝クラス)", "リーディングパート, ストライクショット", "ゴートゥファースト (Go to First)", "01172", "106803", "373126", "ルーラーシップ", "2007103143", "タイトルパート", "2006103260", "アグネスタキオン", "1998101516", "2017105410, 2013105959", null], ["2021104207", "2021年3月29日", "辻野泰之 (栗東)", "東京ホースレーシング", "友田牧場", "新ひだか町", "-", "193万円 (中央)", "5戦0勝 [0-0-0-5]", null, "レッドラファーガ, フォーサイドナイン", "レッドモンストル (Red Monstre)", "01183", "180800", "233086", "レッドファルクス", "2011104416", "ツインテール", "2008103146", "Tale of the Cat", "000a002276", "2015103216, 2020106661", "1口:5万円/400口"], ["2019110125", "2019年9月5日", "石坂公一 (栗東)", "吉田和美", "Kia Ora Stud Pty Ltd", "豪", "-", "2,418万円 (中央)", "14戦2勝 [2-3-1-8]", "24'4歳以上1勝クラス", "Miss, Debutante", "モディカ (Modica)", "01166", "138007", "560731", "American Pharoah", "000a012aad", "More Than Real", "000a01bb13", "More Than Ready", "000a00fcdd", ", ", null], ["2020103469", "2020年3月14日", "別府真司 (高知)", "組）志士十二組合", "ノーザンファーム", "安平町", "-", "550万円 (中央) /264万円 (地方)", "18戦3勝 [3-4-0-11]", "23'3歳未勝利", "ダノンフィオーレ, ダノンホイットニー", "ダノンフレア (<PERSON><PERSON>)", "a0044", "x09f6f", "373126", "ロードカナロア", "2008103552", "ダノンチェリー", "2015104691", "ディープインパクト", "2002100816", "2022104894, 2021105639", null], ["2022105209", "2022年3月8日", "今野貞一 (栗東)", "谷掛龍夫", "社台ファーム", "千歳市", "5,500万円 (2023年 セレクトセール)", "1,007万円 (中央)", "4戦1勝 [1-0-0-3]", "24'2歳未勝利", "アストラサンタンの2023", "ラッキーベイ (Lucky Bay)", "01128", "727008", "393126", "モーリス", "2011100655", "アストラサンタン", "2015104627", "ハーツクライ", "2001103038", "2023103312", null]], "shape": {"columns": 22, "rows": 11786}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>生年月日</th>\n", "      <th>調教師</th>\n", "      <th>馬主</th>\n", "      <th>生産者</th>\n", "      <th>産地</th>\n", "      <th>セリ取引価格</th>\n", "      <th>獲得賞金</th>\n", "      <th>通算成績</th>\n", "      <th>主な勝鞍</th>\n", "      <th>近親馬</th>\n", "      <th>...</th>\n", "      <th>owner_id</th>\n", "      <th>breeder_id</th>\n", "      <th>father_name</th>\n", "      <th>father_id</th>\n", "      <th>mother_name</th>\n", "      <th>mother_id</th>\n", "      <th>mother_father_name</th>\n", "      <th>mother_father_id</th>\n", "      <th>sibling_ids</th>\n", "      <th>募集情報</th>\n", "    </tr>\n", "    <tr>\n", "      <th>horse_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022103037</th>\n", "      <td>2022年6月23日</td>\n", "      <td>森田直行 (栗東)</td>\n", "      <td>ディアレストクラブ</td>\n", "      <td>ディアレストクラブ</td>\n", "      <td>浦河町</td>\n", "      <td>-</td>\n", "      <td>72万円 (中央)</td>\n", "      <td>2戦0勝 [0-0-0-2]</td>\n", "      <td>NaN</td>\n", "      <td>サパテアール, ダンサクドゥーロ</td>\n", "      <td>...</td>\n", "      <td>013803</td>\n", "      <td>064203</td>\n", "      <td>オルフェーヴル</td>\n", "      <td>2008102636</td>\n", "      <td>ファーレサルティ</td>\n", "      <td>2006103200</td>\n", "      <td>ダンスインザダーク</td>\n", "      <td>1993109188</td>\n", "      <td>2019102514, 2014106091</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021101444</th>\n", "      <td>2021年3月7日</td>\n", "      <td>西園正都 (栗東)</td>\n", "      <td>里見治</td>\n", "      <td>下河辺牧場</td>\n", "      <td>日高町</td>\n", "      <td>-</td>\n", "      <td>3,324万円 (中央) /2,210万円 (地方)</td>\n", "      <td>8戦2勝 [2-2-1-3]</td>\n", "      <td>23'ヤマボウシ賞(1勝クラス)</td>\n", "      <td>サンライズジャスト, ショウナンアーチー</td>\n", "      <td>...</td>\n", "      <td>790031</td>\n", "      <td>913124</td>\n", "      <td>ヘニーヒューズ</td>\n", "      <td>000a011155</td>\n", "      <td>マーメイドティアラ</td>\n", "      <td>2009102448</td>\n", "      <td>シンボリクリスエス</td>\n", "      <td>1999110099</td>\n", "      <td>2019101668, 2018100443</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022104682</th>\n", "      <td>2022年1月18日</td>\n", "      <td>田中博康 (美浦)</td>\n", "      <td>鈴木剛史</td>\n", "      <td>ノーザンファーム</td>\n", "      <td>安平町</td>\n", "      <td>6,600万円 (2022年 セレクトセール)</td>\n", "      <td>2,454万円 (中央)</td>\n", "      <td>4戦1勝 [1-1-1-1]</td>\n", "      <td>24'2歳新馬</td>\n", "      <td>エスポワールの2024</td>\n", "      <td>...</td>\n", "      <td>955033</td>\n", "      <td>373126</td>\n", "      <td>ドゥラメンテ</td>\n", "      <td>2012104511</td>\n", "      <td>エスポワール</td>\n", "      <td>2016104514</td>\n", "      <td>オルフェーヴル</td>\n", "      <td>2008102636</td>\n", "      <td>2024106219</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021102673</th>\n", "      <td>2021年3月30日</td>\n", "      <td>森澤友貴 (兵庫)</td>\n", "      <td>大八木信行</td>\n", "      <td>川越ファーム</td>\n", "      <td>浦河町</td>\n", "      <td>-</td>\n", "      <td>223万円 (中央) /218万円 (地方)</td>\n", "      <td>12戦2勝 [2-1-1-8]</td>\n", "      <td>第28回大日本プロレスCC2一</td>\n", "      <td>ラスティンガール, ラブリーガール</td>\n", "      <td>...</td>\n", "      <td>929002</td>\n", "      <td>310039</td>\n", "      <td>スワーヴリチャード</td>\n", "      <td>2014106083</td>\n", "      <td>カラレイア</td>\n", "      <td>2015104985</td>\n", "      <td>エンパイアメーカー</td>\n", "      <td>000a010542</td>\n", "      <td>2020106349, 2022102696</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018105012</th>\n", "      <td>2018年3月19日</td>\n", "      <td>友道康夫 (栗東)</td>\n", "      <td>金子真人ホールディングス</td>\n", "      <td>ノーザンファーム</td>\n", "      <td>安平町</td>\n", "      <td>-</td>\n", "      <td>3億2,967万円 (中央)</td>\n", "      <td>13戦5勝 [5-1-4-3]</td>\n", "      <td>25'京都記念(G2)</td>\n", "      <td>ボレアス, キラウエア</td>\n", "      <td>...</td>\n", "      <td>708800</td>\n", "      <td>373126</td>\n", "      <td>ディープインパクト</td>\n", "      <td>2002100816</td>\n", "      <td>クロウキャニオン</td>\n", "      <td>2002106979</td>\n", "      <td>フレンチデピュティ</td>\n", "      <td>000a00013a</td>\n", "      <td>2008103136, 2007103169</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022106657</th>\n", "      <td>2022年4月1日</td>\n", "      <td>大久保龍 (栗東)</td>\n", "      <td>ノースヒルズ</td>\n", "      <td>ノースヒルズ</td>\n", "      <td>新冠町</td>\n", "      <td>-</td>\n", "      <td>0万円</td>\n", "      <td>1戦0勝 [0-0-0-1]</td>\n", "      <td>NaN</td>\n", "      <td>クータモ, マエストロライト</td>\n", "      <td>...</td>\n", "      <td>788800</td>\n", "      <td>810464</td>\n", "      <td>マクフィ</td>\n", "      <td>000a011c77</td>\n", "      <td>ブライトムーン</td>\n", "      <td>2014100616</td>\n", "      <td>ルーラーシップ</td>\n", "      <td>2007103143</td>\n", "      <td>2023104407, 2021106646</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021110119</th>\n", "      <td>2021年8月23日</td>\n", "      <td>茶木太樹 (栗東)</td>\n", "      <td>吉田和美</td>\n", "      <td>Allam Racing Syndicate</td>\n", "      <td>豪</td>\n", "      <td>-</td>\n", "      <td>1,820万円 (中央)</td>\n", "      <td>6戦2勝 [2-2-0-2]</td>\n", "      <td>24'3歳以上1勝クラス</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>138007</td>\n", "      <td>670738</td>\n", "      <td>Capitalist</td>\n", "      <td>000a01d389</td>\n", "      <td>Catwalk</td>\n", "      <td>000a01da36</td>\n", "      <td>Foxwedge</td>\n", "      <td>000a01b82b</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019105226</th>\n", "      <td>2019年4月25日</td>\n", "      <td>木村哲也 (美浦)</td>\n", "      <td>サンデーレーシング</td>\n", "      <td>ノーザンファーム</td>\n", "      <td>安平町</td>\n", "      <td>-</td>\n", "      <td>1億2,472万円 (中央)</td>\n", "      <td>12戦3勝 [3-1-2-6]</td>\n", "      <td>22'クイーンC(G3)</td>\n", "      <td>ラフェリシテ, オールアットワンス</td>\n", "      <td>...</td>\n", "      <td>226800</td>\n", "      <td>373126</td>\n", "      <td>ハービンジャー</td>\n", "      <td>000a011996</td>\n", "      <td>シュプリームギフト</td>\n", "      <td>2008103205</td>\n", "      <td>ディープインパクト</td>\n", "      <td>2002100816</td>\n", "      <td>2016104494, 2018105061</td>\n", "      <td>1口:60万円/40口</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2019年1月26日</td>\n", "      <td>上原博之 (美浦)</td>\n", "      <td>兼子尚也</td>\n", "      <td>高橋啓</td>\n", "      <td>平取町</td>\n", "      <td>-</td>\n", "      <td>1,430万円 (中央) /414万円 (地方)</td>\n", "      <td>29戦6勝 [6-3-4-16]</td>\n", "      <td>C2三</td>\n", "      <td>ダニーボーイ, サンマルリアン</td>\n", "      <td>...</td>\n", "      <td>138031</td>\n", "      <td>430362</td>\n", "      <td>キタサンブラック</td>\n", "      <td>2012102013</td>\n", "      <td>デイドリーム</td>\n", "      <td>2012100904</td>\n", "      <td>アドマイヤムーン</td>\n", "      <td>2003102991</td>\n", "      <td>2017101131, 2020101086</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021107336</th>\n", "      <td>2021年2月21日</td>\n", "      <td>村上頼章 (大井)</td>\n", "      <td>酒井孝敏</td>\n", "      <td>宝寄山拓樹</td>\n", "      <td>日高町</td>\n", "      <td>-</td>\n", "      <td>110万円 (地方)</td>\n", "      <td>26戦1勝 [1-1-2-22]</td>\n", "      <td>菊月賞</td>\n", "      <td>サヨノスピークロウ</td>\n", "      <td>...</td>\n", "      <td>714031</td>\n", "      <td>360232</td>\n", "      <td>ホークビル</td>\n", "      <td>000a01327c</td>\n", "      <td>ナムカーン</td>\n", "      <td>2016103621</td>\n", "      <td>キングズベスト</td>\n", "      <td>000a0022d2</td>\n", "      <td>2022102021</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11786 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                  生年月日        調教師            馬主                     生産者   産地  \\\n", "horse_id                                                                       \n", "2022103037  2022年6月23日  森田直行 (栗東)     ディアレストクラブ               ディアレストクラブ  浦河町   \n", "2021101444   2021年3月7日  西園正都 (栗東)           里見治                   下河辺牧場  日高町   \n", "2022104682  2022年1月18日  田中博康 (美浦)          鈴木剛史                ノーザンファーム  安平町   \n", "2021102673  2021年3月30日  森澤友貴 (兵庫)         大八木信行                  川越ファーム  浦河町   \n", "2018105012  2018年3月19日  友道康夫 (栗東)  金子真人ホールディングス                ノーザンファーム  安平町   \n", "...                ...        ...           ...                     ...  ...   \n", "2022106657   2022年4月1日  大久保龍 (栗東)        ノースヒルズ                  ノースヒルズ  新冠町   \n", "2021110119  2021年8月23日  茶木太樹 (栗東)          吉田和美  Allam Racing Syndicate    豪   \n", "2019105226  2019年4月25日  木村哲也 (美浦)     サンデーレーシング                ノーザンファーム  安平町   \n", "2019101133  2019年1月26日  上原博之 (美浦)          兼子尚也                     高橋啓  平取町   \n", "2021107336  2021年2月21日  村上頼章 (大井)          酒井孝敏                   宝寄山拓樹  日高町   \n", "\n", "                             セリ取引価格                        獲得賞金  \\\n", "horse_id                                                          \n", "2022103037                        -                   72万円 (中央)   \n", "2021101444                        -  3,324万円 (中央) /2,210万円 (地方)   \n", "2022104682  6,600万円 (2022年 セレクトセール)                2,454万円 (中央)   \n", "2021102673                        -      223万円 (中央) /218万円 (地方)   \n", "2018105012                        -              3億2,967万円 (中央)   \n", "...                             ...                         ...   \n", "2022106657                        -                         0万円   \n", "2021110119                        -                1,820万円 (中央)   \n", "2019105226                        -              1億2,472万円 (中央)   \n", "2019101133                        -    1,430万円 (中央) /414万円 (地方)   \n", "2021107336                        -                  110万円 (地方)   \n", "\n", "                        通算成績              主な勝鞍                   近親馬  ...  \\\n", "horse_id                                                              ...   \n", "2022103037    2戦0勝 [0-0-0-2]               NaN      サパテアール, ダンサクドゥーロ  ...   \n", "2021101444    8戦2勝 [2-2-1-3]  23'ヤマボウシ賞(1勝クラス)  サンライズジャスト, ショウナンアーチー  ...   \n", "2022104682    4戦1勝 [1-1-1-1]           24'2歳新馬           エスポワールの2024  ...   \n", "2021102673   12戦2勝 [2-1-1-8]   第28回大日本プロレスCC2一     ラスティンガール, ラブリーガール  ...   \n", "2018105012   13戦5勝 [5-1-4-3]       25'京都記念(G2)           ボレアス, キラウエア  ...   \n", "...                      ...               ...                   ...  ...   \n", "2022106657    1戦0勝 [0-0-0-1]               Na<PERSON>        クータモ, マエストロライト  ...   \n", "2021110119    6戦2勝 [2-2-0-2]      24'3歳以上1勝クラス                   NaN  ...   \n", "2019105226   12戦3勝 [3-1-2-6]      22'クイーンC(G3)     ラフェリシテ, オールアットワンス  ...   \n", "2019101133  29戦6勝 [6-3-4-16]               C2三       ダニーボーイ, サンマルリアン  ...   \n", "2021107336  26戦1勝 [1-1-2-22]               菊月賞             サヨノスピークロウ  ...   \n", "\n", "           owner_id breeder_id father_name   father_id mother_name  \\\n", "horse_id                                                             \n", "2022103037   013803     064203     オルフェーヴル  2008102636    ファーレサルティ   \n", "2021101444   790031     913124     ヘニーヒューズ  000a011155   マーメイドティアラ   \n", "2022104682   955033     373126      ドゥラメンテ  2012104511      エスポワール   \n", "2021102673   929002     310039   スワーヴリチャード  2014106083       カラレイア   \n", "2018105012   708800     373126   ディープインパクト  2002100816    クロウキャニオン   \n", "...             ...        ...         ...         ...         ...   \n", "2022106657   788800     810464        マクフィ  000a011c77     ブライトムーン   \n", "2021110119   138007     670738  Capitalist  000a01d389     Catwalk   \n", "2019105226   226800     373126     ハービンジャー  000a011996   シュプリームギフト   \n", "2019101133   138031     430362    キタサンブラック  2012102013      デイドリーム   \n", "2021107336   714031     360232       ホークビル  000a01327c       ナムカーン   \n", "\n", "             mother_id mother_father_name mother_father_id  \\\n", "horse_id                                                     \n", "2022103037  2006103200          ダンスインザダーク       1993109188   \n", "2021101444  2009102448          シンボリクリスエス       1999110099   \n", "2022104682  2016104514            オルフェーヴル       2008102636   \n", "2021102673  2015104985          エンパイアメーカー       000a010542   \n", "2018105012  2002106979          フレンチデピュティ       000a00013a   \n", "...                ...                ...              ...   \n", "2022106657  2014100616            ルーラーシップ       2007103143   \n", "2021110119  000a01da36           Foxwedge       000a01b82b   \n", "2019105226  2008103205          ディープインパクト       2002100816   \n", "2019101133  2012100904           アドマイヤムーン       2003102991   \n", "2021107336  2016103621            キングズベスト       000a0022d2   \n", "\n", "                       sibling_ids         募集情報  \n", "horse_id                                         \n", "2022103037  2019102514, 2014106091          NaN  \n", "2021101444  2019101668, 2018100443          NaN  \n", "2022104682              2024106219          NaN  \n", "2021102673  2020106349, 2022102696          NaN  \n", "2018105012  2008103136, 2007103169          NaN  \n", "...                            ...          ...  \n", "2022106657  2023104407, 2021106646          NaN  \n", "2021110119                     NaN          NaN  \n", "2019105226  2016104494, 2018105061  1口:60万円/40口  \n", "2019101133  2017101131, 2020101086          NaN  \n", "2021107336              2022102021          NaN  \n", "\n", "[11786 rows x 22 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["horse_info = pd.read_pickle(output_dir / \"race_horses_horse_info_2024.pickle\")\n", "horse_info"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "日付", "rawType": "object", "type": "string"}, {"name": "開催", "rawType": "object", "type": "string"}, {"name": "天気", "rawType": "object", "type": "unknown"}, {"name": "R", "rawType": "float64", "type": "float"}, {"name": "レース名", "rawType": "object", "type": "string"}, {"name": "映像", "rawType": "float64", "type": "float"}, {"name": "頭数", "rawType": "float64", "type": "float"}, {"name": "枠番", "rawType": "Int64", "type": "integer"}, {"name": "馬番", "rawType": "Int64", "type": "integer"}, {"name": "オッズ", "rawType": "Float64", "type": "float"}, {"name": "人気", "rawType": "Int64", "type": "integer"}, {"name": "着順", "rawType": "Int64", "type": "integer"}, {"name": "騎手", "rawType": "object", "type": "string"}, {"name": "斤量", "rawType": "Float64", "type": "float"}, {"name": "距離", "rawType": "object", "type": "string"}, {"name": "馬場", "rawType": "object", "type": "unknown"}, {"name": "馬場指数", "rawType": "object", "type": "unknown"}, {"name": "タイム", "rawType": "object", "type": "unknown"}, {"name": "着差", "rawType": "float64", "type": "float"}, {"name": "ﾀｲﾑ指数", "rawType": "object", "type": "unknown"}, {"name": "通過", "rawType": "object", "type": "unknown"}, {"name": "ペース", "rawType": "object", "type": "unknown"}, {"name": "上り", "rawType": "float64", "type": "float"}, {"name": "馬体重", "rawType": "object", "type": "string"}, {"name": "厩舎ｺﾒﾝﾄ", "rawType": "float64", "type": "float"}, {"name": "備考", "rawType": "float64", "type": "float"}, {"name": "勝ち馬(2着馬)", "rawType": "object", "type": "unknown"}, {"name": "賞金", "rawType": "int64", "type": "integer"}, {"name": "rank_binary", "rawType": "int64", "type": "integer"}, {"name": "体重", "rawType": "float64", "type": "float"}, {"name": "体重変化", "rawType": "float64", "type": "float"}], "ref": "fcc39dbe-4393-45af-87b9-55838978e60a", "rows": [["2022103037", "2025/03/22", "1阪神7", "晴", "5.0", "3歳未勝利", null, "15.0", "2", "2", "139.7", "13", "12", "高倉稜", "57.0", "芝2400", "良", "**", "2:34.6", "1.4", "**", "3-3-4-4", "37.8-34.0", "35.2", "440(+18)", null, null, "ダノンカゼルタ", "0", "0", "440.0", "18.0"], ["2022103037", "2024/12/01", "7京都2", "晴", "6.0", "2歳新馬", null, "7.0", "5", "5", "43.5", "6", "5", "吉田隼人", "56.0", "ダ1800", "良", "**", "1:57.3", "2.5", "**", "5-5-5-4", "38.9-36.6", "38.6", "422(0)", null, null, "ソニックステップ", "72", "0", "422.0", "0.0"], ["2021101444", "2025/05/18", "2京都8", "曇", "11.0", "栗東S(L)", null, "16.0", "5", "9", "12.4", "7", "8", "和田竜二", "57.0", "ダ1400", "稍", "**", "1:23.2", "0.8", "**", "7-7", "34.7-35.8", "35.9", "500(+7)", null, null, "コンティノアール", "0", "0", "500.0", "7.0"], ["2021101444", "2024/09/03", "盛岡", "晴", "10.0", "不来方賞(JpnII)", null, "12.0", "1", "1", "5.9", "4", "3", "和田竜二", "56.0", "ダ2000", "稍", "**", "2:04.4", "1.2", "**", "3-3-3-3", "0.0-36.2", "37.2", "493(+3)", null, null, "サンライズジパング", "800", "1", "493.0", "3.0"], ["2021101444", "2024/08/04", "2新潟4", "晴", "7.0", "レパードS(GIII)", null, "15.0", "8", "14", "52.9", "11", "2", "和田竜二", "57.0", "ダ1800", "良", "**", "1:51.4", "0.2", "**", "2-2-2-2", "35.7-37.8", "37.9", "490(0)", null, null, "ミッキーファイト", "1515", "1", "490.0", "0.0"], ["2021101444", "2024/02/24", "キングア", null, null, "サウジダービー(GIII)", null, "12.0", null, "10", null, "6", "10", "モレイラ", "55.0", "ダ1600", "良", null, null, null, null, null, null, null, "計不", null, null, null, "0", "0", null, null], ["2021101444", "2023/12/13", "川崎", "晴", "11.0", "全日本2歳優駿【国際(JpnI)", null, "12.0", "7", "9", "6.1", "3", "5", "和田竜二", "56.0", "ダ1600", "稍", "**", "1:46.1", "2.6", "**", "8-8-7-6", "36.6-40.1", "42.1", "483(-6)", null, null, "フォーエバーヤング", "210", "0", "483.0", "-6.0"], ["2021101444", "2023/11/22", "園田", "晴", "11.0", "兵庫ジュニアグランプ(JpnII)", null, "12.0", "2", "2", "2.9", "2", "2", "和田竜二", "55.0", "ダ1400", "良", "**", "1:29.4", "0.0", "**", "6-6-4-1", "0.0-39.3", "38.8", "489(+1)", null, null, "イーグルノワール", "1200", "1", "489.0", "1.0"], ["2021101444", "2023/09/30", "4阪神8", "晴", "9.0", "ヤマボウシ賞(1勝クラス)", null, "9.0", "1", "1", "3.5", "2", "1", "和田竜二", "55.0", "ダ1400", "良", "**", "1:25.8", "0.0", "**", "6-4", "34.9-38.6", "37.9", "488(+12)", null, null, "(ナスティウェザー)", "1088", "1", "488.0", "12.0"], ["2021101444", "2023/06/17", "3阪神5", "晴", "5.0", "2歳新馬", null, "13.0", "5", "6", "4.0", "2", "1", "和田竜二", "55.0", "ダ1200", "良", "**", "1:13.3", "-0.6", "**", "3-3", "36.2-37.1", "36.8", "476(0)", null, null, "(ボビーニクビッタケ)", "720", "1", "476.0", "0.0"], ["2021102673", "2025/03/11", "園田", "曇", "12.0", "B2二", null, "11.0", "4", "4", "8.0", "3", "11", "大山真吾", "56.0", "ダ1400", "重", "**", "1:35.9", "4.6", "**", "2-2-2-6", "0.0-39.6", "44.0", "491(+5)", null, null, "シンメクローサ", "0", "0", "491.0", "5.0"], ["2021102673", "2024/12/31", "園田", "曇", "9.0", "B2", null, "12.0", "7", "9", "1.7", "1", "11", "下原理", "56.0", "ダ1230", "良", "**", "1:23.2", "2.8", "**", "2-2-2-4", "0.0-40.4", "43.1", "486(-3)", null, null, "ホクザンバーリイ", "0", "0", "486.0", "-3.0"], ["2021102673", "2024/11/19", "園田", "曇", "9.0", "C1一", null, "11.0", "8", "11", "1.1", "1", "2", "下原理", "57.0", "ダ1400", "稍", "**", "1:31.9", "0.0", "**", "1-1-1-1", "0.0-41.6", "41.6", "489(0)", null, null, "スズヴァレット", "38", "1", "489.0", "0.0"], ["2021102673", "2024/10/16", "園田", "曇", "9.0", "C1二", null, "7.0", "3", "3", "1.3", "1", "1", "下原理", "56.0", "ダ1400", "良", "**", "1:31.2", "-0.5", "**", "1-1-1-1", "0.0-39.9", "39.9", "489(+5)", null, null, "(ゼットシルヴィア)", "110", "1", "489.0", "5.0"], ["2021102673", "2024/09/27", "園田", "晴", "10.0", "第28回大日本プロレ(C2)", null, "10.0", "6", "6", "5.1", "2", "1", "下原理", "55.0", "ダ1400", "良", "**", "1:30.8", "-1.1", "**", "1-1-1-1", "0.0-39.8", "39.8", "484(+14)", null, null, "(トレーネ)", "70", "1", "484.0", "14.0"], ["2021102673", "2024/08/18", "2中京4", "晴", "4.0", "3歳未勝利", null, "16.0", "3", "5", "12.8", "6", "13", "鮫島克駿", "57.0", "ダ1400", "良", "**", "1:27.9", "2.6", "**", "1-1", "34.0-39.1", "41.7", "470(+12)", null, null, "ベルダイナフェロー", "0", "0", "470.0", "12.0"], ["2021102673", "2024/02/17", "2京都7", "曇", "5.0", "3歳未勝利", null, "14.0", "4", "5", "34.4", "7", "13", "ルメート", "57.0", "芝2400", "良", "**", "2:33.4", "5.7", "**", "7-7-9-10", "36.1-36.5", "41.5", "458(-6)", null, null, "ベトルス", "0", "0", "458.0", "-6.0"], ["2021102673", "2024/01/28", "2京都2", "晴", "3.0", "3歳未勝利", null, "16.0", "5", "10", "9.4", "3", "4", "ルメール", "57.0", "ダ1400", "良", "**", "1:26.9", "1.6", "**", "3-3", "35.1-37.8", "39.0", "464(-2)", null, null, "ストレングス", "83", "0", "464.0", "-2.0"], ["2021102673", "2023/11/25", "3京都7", "晴", "2.0", "2歳未勝利", null, "13.0", "2", "2", "18.8", "4", "7", "北村友一", "56.0", "芝1600", "良", "**", "1:37.2", "1.1", "**", "9-8", "36.1-34.9", "35.3", "466(+2)", null, null, "フェリーニ", "0", "0", "466.0", "2.0"], ["2021102673", "2023/10/29", "2京都9", "晴", "3.0", "2歳未勝利", null, "13.0", "4", "5", "13.9", "5", "3", "北村友一", "56.0", "芝1600", "良", "**", "1:35.1", "0.6", "**", "4-4", "34.0-36.8", "36.5", "464(-6)", null, null, "ルージュプレジール", "140", "1", "464.0", "-6.0"], ["2021102673", "2023/10/09", "2京都3", "曇", "3.0", "2歳未勝利", null, "9.0", "3", "3", "6.1", "4", "8", "岩田康誠", "56.0", "芝1800", "重", "**", "1:51.5", "2.8", "**", "1-1", "35.8-36.6", "39.4", "470(+6)", null, null, "ストラクチャー", "0", "0", "470.0", "6.0"], ["2021102673", "2023/09/10", "4阪神2", "晴", "5.0", "2歳新馬", null, "7.0", "6", "6", "3.5", "2", "6", "岩田康誠", "55.0", "芝1800", "良", "**", "1:49.3", "1.0", "**", "3-3", "36.9-34.5", "35.1", "464(0)", null, null, "サークルオブジョイ", "0", "0", "464.0", "0.0"], ["2022104682", "2025/04/20", "3中山8", "曇", "11.0", "皐月賞(GI)", null, "18.0", "7", "13", "56.5", "11", "8", "横山和生", "57.0", "芝2000", "良", "**", "1:57.9", "0.9", "**", "17-18-3-2", "34.5-34.8", "35.5", "492(-2)", null, null, "ミュージアムマイル", "0", "0", "492.0", "-2.0"], ["2022104682", "2025/03/09", "2中山4", "晴", "11.0", "報知弥生ディープ記念(GII)", null, "14.0", "5", "7", "9.9", "5", "3", "横山和生", "57.0", "芝2000", "稍", "**", "2:01.4", "0.1", "**", "10-10-13-8", "35.9-37.2", "36.1", "494(-2)", null, null, "ファウストラーゼン", "1414", "1", "494.0", "-2.0"], ["2022104682", "2025/01/06", "1中山2", "小雨", "7.0", "3歳1勝クラス", null, "12.0", "8", "11", "1.6", "1", "2", "戸崎圭太", "57.0", "芝2000", "良", "**", "2:00.8", "0.1", "**", "6-6-7-4", "35.3-36.8", "35.7", "496(+8)", null, null, "ゴーソーファー", "320", "1", "496.0", "8.0"], ["2022104682", "2024/11/02", "5東京1", "小雨", "5.0", "2歳新馬", null, "10.0", "1", "1", "1.7", "1", "1", "戸崎圭太", "56.0", "芝2000", "稍", "**", "2:02.1", "-0.4", "**", "4-5-5", "37.4-34.7", "34.3", "488(0)", null, null, "(ディベルティスマン)", "720", "1", "488.0", "0.0"], ["2018105012", "2025/04/06", "2阪神4", "晴", "11.0", "大阪杯(GI)", null, "15.0", "4", "7", "13.6", "8", "3", "岩田望来", "58.0", "芝2000", "良", "**", "1:56.5", "0.3", "**", "14-14-14-14", "34.9-35.1", "33.5", "526(-8)", null, null, "ベラジオオペラ", "7546", "1", "526.0", "-8.0"], ["2018105012", "2025/02/16", "1京都6", "曇", "11.0", "京都記念(GII)", null, "12.0", "1", "1", "9.5", "5", "1", "岩田望来", "57.0", "芝2200", "稍", "**", "2:15.7", "-0.1", "**", "5-5-6-4", "37.0-34.6", "34.2", "534(+20)", null, null, "(リビアングラス)", "6285", "1", "534.0", "20.0"], ["2018105012", "2024/10/06", "4東京2", "曇", "11.0", "毎日王冠(GII)", null, "14.0", "7", "12", "5.5", "3", "7", "岩田望来", "57.0", "芝1800", "良", "**", "1:45.4", "0.3", "**", "7-5-6", "35.3-33.7", "33.4", "514(-4)", null, null, "シックスペンス", "0", "0", "514.0", "-4.0"], ["2018105012", "2024/06/01", "4京都1", "晴", "11.0", "鳴尾記念(GIII)", null, "13.0", "6", "9", "2.5", "1", "1", "岩田望来", "57.0", "芝2000", "良", "**", "1:57.2", "0.0", "**", "5-5-5-5", "34.9-34.4", "33.9", "518(0)", null, null, "(ボッケリーニ)", "4349", "1", "518.0", "0.0"], ["2018105012", "2024/05/05", "1新潟4", "晴", "11.0", "新潟大賞典(GIII)", null, "16.0", "1", "1", "5.4", "2", "3", "荻野極", "59.0", "芝2000", "良", "**", "2:00.3", "0.2", "**", "9-10", "36.9-34.4", "33.5", "518(-4)", null, null, "ヤマニンサルバム", "1108", "1", "518.0", "-4.0"], ["2018105012", "2024/03/10", "1中京2", "晴", "11.0", "金鯱賞(GII)", null, "13.0", "5", "6", "20.9", "6", "3", "藤岡康太", "57.0", "芝2000", "良", "**", "1:58.5", "0.9", "**", "2-3-3-2", "35.0-34.7", "35.4", "522(+14)", null, null, "プログノーシス", "1713", "1", "522.0", "14.0"], ["2018105012", "2022/01/16", "1中京6", "晴", "11.0", "日経新春杯(GII)", null, "16.0", "5", "10", "5.6", "3", "1", "川田将雅", "55.0", "芝2200", "良", "**", "2:11.7", "-0.1", "**", "9-9-9-9", "36.0-35.1", "34.4", "508(+4)", null, null, "(ステラヴェローチェ)", "5813", "1", "508.0", "4.0"], ["2018105012", "2021/05/30", "2東京12", "晴", "11.0", "東京優駿(GI)", null, "17.0", "4", "8", "18.4", "6", "7", "川田将雅", "57.0", "芝2400", "良", "**", "2:23.1", "0.6", "**", "9-8-11-12", "35.0-33.9", "33.8", "504(0)", null, null, "シャフリヤール", "0", "0", "504.0", "0.0"], ["2018105012", "2021/04/18", "3中山8", "晴", "11.0", "皐月賞(GI)", null, "16.0", "3", "6", "21.5", "11", "5", "岩田望来", "57.0", "芝2000", "稍", "**", "2:01.2", "0.6", "**", "10-13-13-8", "36.3-37.0", "36.6", "504(-2)", null, null, "エフフォーリア", "1100", "0", "504.0", "-2.0"], ["2018105012", "2021/02/07", "1中京12", "晴", "11.0", "きさらぎ賞(GIII)", null, "11.0", "3", "3", "3.4", "2", "2", "武豊", "56.0", "芝2000", "良", "**", "2:01.0", "0.0", "**", "9-9-9-9", "37.4-35.6", "34.9", "506(+4)", null, null, "ラーゴム", "1511", "1", "506.0", "4.0"], ["2018105012", "2020/12/26", "5中山7", "晴", "11.0", "ホープフルS(GI)", null, "15.0", "2", "2", "13.1", "4", "3", "武豊", "55.0", "芝2000", "良", "**", "2:03.1", "0.3", "**", "10-9-9-7", "37.2-36.8", "36.4", "502(+2)", null, null, "ダノンザキッド", "1815", "1", "502.0", "2.0"], ["2018105012", "2020/10/17", "4京都3", "雨", "9.0", "紫菊賞(1勝クラス)", null, "5.0", "4", "4", "2.9", "1", "1", "福永祐一", "55.0", "芝2000", "重", "**", "2:04.6", "0.0", "**", "3-3-3-4", "38.4-35.2", "34.9", "500(+8)", null, null, "(グラティトゥー)", "1024", "1", "500.0", "8.0"], ["2018105012", "2020/07/12", "4阪神4", "晴", "5.0", "2歳新馬", null, "7.0", "6", "6", "1.4", "1", "1", "武豊", "54.0", "芝1800", "稍", "**", "1:51.4", "-0.1", "**", "4-3", "38.1-35.8", "35.7", "492(0)", null, null, "(セファーラジエル)", "700", "1", "492.0", "0.0"], ["2021106881", "2025/05/04", "2東京4", "晴", "7.0", "4歳以上1勝クラス", null, "10.0", "5", "5", "22.1", "7", "8", "田辺裕信", "58.0", "ダ2100", "稍", "**", "2:14.2", "1.2", "**", "2-2-2-2", "32.4-36.9", "38.0", "472(-21)", null, null, "セイウンガレオン", "0", "0", "472.0", "-21.0"], ["2021106881", "2025/03/10", "船橋", "晴", "8.0", "はまぐり賞(C2)", null, "11.0", "3", "3", "2.3", "1", "1", "篠谷葵", "56.0", "ダ1600", "稍", "**", "1:45.9", "-0.1", "**", "10-10-10-8", "38.5-40.6", "39.4", "493(-1)", null, null, "(アルファマドンナ)", "150", "1", "493.0", "-1.0"], ["2021106881", "2025/02/11", "船橋", "晴", "6.0", "馬い!八丈島レモンを(C2)", null, "12.0", "4", "4", "7.4", "3", "1", "篠谷葵", "56.0", "ダ1500", "良", "**", "1:36.6", "-0.2", "**", "5-5-4-3", "31.5-39.7", "39.3", "494(+4)", null, null, "(リボルトバレット)", "100", "1", "494.0", "4.0"], ["2021106881", "2025/01/21", "船橋", "晴", "3.0", "C2四　五", null, "14.0", "1", "1", "10.6", "6", "2", "篠谷葵", "56.0", "ダ1500", "良", "**", "1:37.5", "0.2", "**", "5-4-4-5", "30.8-40.4", "40.3", "490(+5)", null, null, "レオボヌール", "40", "1", "490.0", "5.0"], ["2021106881", "2024/12/17", "船橋", "晴", "6.0", "馬い!野菜!たがやす(C2五)", null, "13.0", "7", "11", "15.3", "5", "6", "篠谷葵", "56.0", "ダ1500", "良", "**", "1:38.0", "2.1", "**", "5-6-6-5", "31.3-39.0", "40.7", "485(+7)", null, null, "ルミナスボディ", "0", "0", "485.0", "7.0"], ["2021106881", "2024/11/26", "船橋", "晴", "5.0", "C2四　五", null, "9.0", "6", "6", "4.3", "2", "3", "石崎駿", "56.0", "ダ1500", "良", "**", "1:37.9", "0.4", "**", "6-6-5-5", "32.5-39.6", "39.5", "478(+4)", null, null, "エクレールスカイ", "25", "1", "478.0", "4.0"], ["2021106881", "2024/11/01", "船橋", "曇", "5.0", "C2十一　十二", null, "12.0", "8", "11", "5.3", "2", "1", "篠谷葵", "56.0", "ダ1500", "稍", "**", "1:38.8", "-0.1", "**", "4-4-3-1", "31.8-41.1", "40.9", "474(+4)", null, null, "(グランハバナブルー)", "100", "1", "474.0", "4.0"], ["2021106881", "2024/09/24", "船橋", "晴", "3.0", "3歳　261P", null, "12.0", "1", "1", "6.8", "4", "6", "篠谷葵", "56.0", "ダ1500", "良", "**", "1:40.4", "1.5", "**", "5-5-4-4", "31.7-41.0", "42.3", "470(+8)", null, null, "レオボヌール", "0", "0", "470.0", "8.0"], ["2021106881", "2024/08/11", "2札幌2", "晴", "6.0", "3歳以上1勝クラス", null, "13.0", "6", "9", "118.6", "11", "7", "川端海翼", "52.0", "ダ1700", "良", "**", "1:47.6", "1.3", "**", "5-5-5-6", "30.5-39.2", "39.4", "462(-4)", null, null, "ダノンスウィッチ", "0", "0", "462.0", "-4.0"], ["2021106881", "2024/08/03", "1札幌5", "晴", "6.0", "3歳未勝利", null, "16.0", "3", "6", "23.4", "7", "16", "横山和生", "57.0", "芝2000", "良", "**", "2:04.8", "4.0", "**", "14-15-16-16", "35.2-36.0", "38.3", "466(0)", null, null, "マイネルバーテクス", "0", "0", "466.0", "0.0"], ["2021106881", "2024/06/15", "3東京5", "晴", "2.0", "3歳未勝利", null, "16.0", "6", "11", "6.5", "3", "7", "田辺裕信", "57.0", "ダ2100", "良", "**", "2:15.0", "1.3", "**", "4-4-3-2", "30.2-38.3", "39.4", "466(+6)", null, null, "シグムンド", "0", "0", "466.0", "6.0"]], "shape": {"columns": 31, "rows": 164139}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日付</th>\n", "      <th>開催</th>\n", "      <th>天気</th>\n", "      <th>R</th>\n", "      <th>レース名</th>\n", "      <th>映像</th>\n", "      <th>頭数</th>\n", "      <th>枠番</th>\n", "      <th>馬番</th>\n", "      <th>オッズ</th>\n", "      <th>...</th>\n", "      <th>ペース</th>\n", "      <th>上り</th>\n", "      <th>馬体重</th>\n", "      <th>厩舎ｺﾒﾝﾄ</th>\n", "      <th>備考</th>\n", "      <th>勝ち馬(2着馬)</th>\n", "      <th>賞金</th>\n", "      <th>rank_binary</th>\n", "      <th>体重</th>\n", "      <th>体重変化</th>\n", "    </tr>\n", "    <tr>\n", "      <th>horse_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022103037</th>\n", "      <td>2025/03/22</td>\n", "      <td>1阪神7</td>\n", "      <td>晴</td>\n", "      <td>5.0</td>\n", "      <td>3歳未勝利</td>\n", "      <td>NaN</td>\n", "      <td>15.0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>139.7</td>\n", "      <td>...</td>\n", "      <td>37.8-34.0</td>\n", "      <td>35.2</td>\n", "      <td>440(+18)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ダノンカゼルタ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>440.0</td>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022103037</th>\n", "      <td>2024/12/01</td>\n", "      <td>7京都2</td>\n", "      <td>晴</td>\n", "      <td>6.0</td>\n", "      <td>2歳新馬</td>\n", "      <td>NaN</td>\n", "      <td>7.0</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>43.5</td>\n", "      <td>...</td>\n", "      <td>38.9-36.6</td>\n", "      <td>38.6</td>\n", "      <td>422(0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ソニックステップ</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>422.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021101444</th>\n", "      <td>2025/05/18</td>\n", "      <td>2京都8</td>\n", "      <td>曇</td>\n", "      <td>11.0</td>\n", "      <td>栗東S(L)</td>\n", "      <td>NaN</td>\n", "      <td>16.0</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>12.4</td>\n", "      <td>...</td>\n", "      <td>34.7-35.8</td>\n", "      <td>35.9</td>\n", "      <td>500(+7)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>コンティノアール</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>500.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021101444</th>\n", "      <td>2024/09/03</td>\n", "      <td>盛岡</td>\n", "      <td>晴</td>\n", "      <td>10.0</td>\n", "      <td>不来方賞(JpnII)</td>\n", "      <td>NaN</td>\n", "      <td>12.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5.9</td>\n", "      <td>...</td>\n", "      <td>0.0-36.2</td>\n", "      <td>37.2</td>\n", "      <td>493(+3)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>サンライズジパング</td>\n", "      <td>800</td>\n", "      <td>1</td>\n", "      <td>493.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021101444</th>\n", "      <td>2024/08/04</td>\n", "      <td>2新潟4</td>\n", "      <td>晴</td>\n", "      <td>7.0</td>\n", "      <td>レパードS(GIII)</td>\n", "      <td>NaN</td>\n", "      <td>15.0</td>\n", "      <td>8</td>\n", "      <td>14</td>\n", "      <td>52.9</td>\n", "      <td>...</td>\n", "      <td>35.7-37.8</td>\n", "      <td>37.9</td>\n", "      <td>490(0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ミッキーファイト</td>\n", "      <td>1515</td>\n", "      <td>1</td>\n", "      <td>490.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2022/02/19</td>\n", "      <td>2小倉3</td>\n", "      <td>雨</td>\n", "      <td>2.0</td>\n", "      <td>3歳未勝利</td>\n", "      <td>NaN</td>\n", "      <td>18.0</td>\n", "      <td>7</td>\n", "      <td>14</td>\n", "      <td>6.4</td>\n", "      <td>...</td>\n", "      <td>34.6-35.9</td>\n", "      <td>36.0</td>\n", "      <td>440(-2)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ニホンピロポート</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>440.0</td>\n", "      <td>-2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2022/01/30</td>\n", "      <td>1小倉6</td>\n", "      <td>曇</td>\n", "      <td>2.0</td>\n", "      <td>3歳未勝利</td>\n", "      <td>NaN</td>\n", "      <td>16.0</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>6.1</td>\n", "      <td>...</td>\n", "      <td>34.1-35.2</td>\n", "      <td>35.3</td>\n", "      <td>442(-2)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>タッカーシルバー</td>\n", "      <td>130</td>\n", "      <td>1</td>\n", "      <td>442.0</td>\n", "      <td>-2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2021/12/19</td>\n", "      <td>6阪神6</td>\n", "      <td>晴</td>\n", "      <td>4.0</td>\n", "      <td>2歳未勝利</td>\n", "      <td>NaN</td>\n", "      <td>18.0</td>\n", "      <td>7</td>\n", "      <td>14</td>\n", "      <td>16.5</td>\n", "      <td>...</td>\n", "      <td>34.4-36.2</td>\n", "      <td>39.9</td>\n", "      <td>444(-6)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>デルマグレムリン</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>444.0</td>\n", "      <td>-6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2021/10/30</td>\n", "      <td>4阪神7</td>\n", "      <td>晴</td>\n", "      <td>2.0</td>\n", "      <td>2歳未勝利</td>\n", "      <td>NaN</td>\n", "      <td>10.0</td>\n", "      <td>8</td>\n", "      <td>10</td>\n", "      <td>9.9</td>\n", "      <td>...</td>\n", "      <td>36.6-33.5</td>\n", "      <td>33.7</td>\n", "      <td>450(+12)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ビーアストニッシド</td>\n", "      <td>200</td>\n", "      <td>1</td>\n", "      <td>450.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019101133</th>\n", "      <td>2021/10/09</td>\n", "      <td>4東京1</td>\n", "      <td>曇</td>\n", "      <td>5.0</td>\n", "      <td>2歳新馬</td>\n", "      <td>NaN</td>\n", "      <td>16.0</td>\n", "      <td>6</td>\n", "      <td>11</td>\n", "      <td>7.1</td>\n", "      <td>...</td>\n", "      <td>37.0-33.8</td>\n", "      <td>34.7</td>\n", "      <td>438(0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>モカフラワー</td>\n", "      <td>110</td>\n", "      <td>0</td>\n", "      <td>438.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>164139 rows × 31 columns</p>\n", "</div>"], "text/plain": ["                    日付    開催 天気     R         レース名  映像    頭数  枠番  馬番    オッズ  \\\n", "horse_id                                                                      \n", "2022103037  2025/03/22  1阪神7  晴   5.0        3歳未勝利 NaN  15.0   2   2  139.7   \n", "2022103037  2024/12/01  7京都2  晴   6.0         2歳新馬 NaN   7.0   5   5   43.5   \n", "2021101444  2025/05/18  2京都8  曇  11.0       栗東S(L) NaN  16.0   5   9   12.4   \n", "2021101444  2024/09/03    盛岡  晴  10.0  不来方賞(JpnII) NaN  12.0   1   1    5.9   \n", "2021101444  2024/08/04  2新潟4  晴   7.0  レパードS(GIII) NaN  15.0   8  14   52.9   \n", "...                ...   ... ..   ...          ...  ..   ...  ..  ..    ...   \n", "2019101133  2022/02/19  2小倉3  雨   2.0        3歳未勝利 NaN  18.0   7  14    6.4   \n", "2019101133  2022/01/30  1小倉6  曇   2.0        3歳未勝利 NaN  16.0   5   9    6.1   \n", "2019101133  2021/12/19  6阪神6  晴   4.0        2歳未勝利 NaN  18.0   7  14   16.5   \n", "2019101133  2021/10/30  4阪神7  晴   2.0        2歳未勝利 NaN  10.0   8  10    9.9   \n", "2019101133  2021/10/09  4東京1  曇   5.0         2歳新馬 NaN  16.0   6  11    7.1   \n", "\n", "            ...        ペース    上り       馬体重  厩舎ｺﾒﾝﾄ  備考   勝ち馬(2着馬)    賞金  \\\n", "horse_id    ...                                                           \n", "2022103037  ...  37.8-34.0  35.2  440(+18)     NaN NaN    ダノンカゼルタ     0   \n", "2022103037  ...  38.9-36.6  38.6    422(0)     NaN NaN   ソニックステップ    72   \n", "2021101444  ...  34.7-35.8  35.9   500(+7)     NaN NaN   コンティノアール     0   \n", "2021101444  ...   0.0-36.2  37.2   493(+3)     NaN NaN  サンライズジパング   800   \n", "2021101444  ...  35.7-37.8  37.9    490(0)     NaN NaN   ミッキーファイト  1515   \n", "...         ...        ...   ...       ...     ...  ..        ...   ...   \n", "2019101133  ...  34.6-35.9  36.0   440(-2)     NaN NaN   ニホンピロポート    78   \n", "2019101133  ...  34.1-35.2  35.3   442(-2)     NaN NaN   タッカーシルバー   130   \n", "2019101133  ...  34.4-36.2  39.9   444(-6)     NaN NaN   デルマグレムリン     0   \n", "2019101133  ...  36.6-33.5  33.7  450(+12)     NaN NaN  ビーアストニッシド   200   \n", "2019101133  ...  37.0-33.8  34.7    438(0)     NaN NaN     モカフラワー   110   \n", "\n", "           rank_binary     体重  体重変化  \n", "horse_id                             \n", "2022103037           0  440.0  18.0  \n", "2022103037           0  422.0   0.0  \n", "2021101444           0  500.0   7.0  \n", "2021101444           1  493.0   3.0  \n", "2021101444           1  490.0   0.0  \n", "...                ...    ...   ...  \n", "2019101133           0  440.0  -2.0  \n", "2019101133           1  442.0  -2.0  \n", "2019101133           0  444.0  -6.0  \n", "2019101133           1  450.0  12.0  \n", "2019101133           0  438.0   0.0  \n", "\n", "[164139 rows x 31 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["horse_results = pd.read_pickle(output_dir / \"race_horses_horse_results_2024.pickle\")\n", "horse_results"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-29 20:48:01,917 - module.comprehensive_data_integrator - INFO - 設定の初期化とバリデーションに成功しました。\n", "2025-05-29 20:48:01,919 - module.horse_processor - INFO - 馬HTMLファイルのインデックスを構築中...\n", "2025-05-29 20:48:02,111 - module.horse_processor - INFO - 馬HTMLファイルのインデックス構築完了。52246件 (0.19秒)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最終的な実行設定:\n", "  default_years: ['2022']\n", "  use_pickle_source: True\n", "  pickle_base_dir: output\n", "  race_info_filename_template: race_info_{year}.pickle\n", "  race_results_filename_template: race_results_{year}.pickle\n", "  horse_info_pickle_filename_template: race_horses_horse_info_{year}.pickle\n", "  horse_results_pickle_filename_template: race_horses_horse_results_{year}.pickle\n", "  enable_custom_feature_pickle: False\n", "  include_race_info: True\n", "  include_horse_info: True\n", "  include_past_performance: True\n", "  performance_window_races: [3, 5, 10]\n", "  parallel: True\n", "  max_workers: 12\n", "  save_output: False\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 20:48:02,276 - module.comprehensive_data_integrator - INFO - 包括的な表形式データの生成を開始します\n", "2025-05-29 20:48:02,278 - module.comprehensive_data_integrator - INFO - 1. レース情報とレース結果を取得中 (年度: 2022, pickle使用: True)\n", "2025-05-29 20:48:02,279 - module.race_data_processor - INFO - レースpickleファイルの処理を開始: year=2022, race_id=None, base_dir=output\n", "2025-05-29 20:48:02,281 - module.race_data_processor - INFO - レース情報pickleファイルを読み込み中: output\\race_info_2022.pickle\n", "2025-05-29 20:48:02,287 - module.race_data_processor - INFO - レース情報読み込み完了: 3456件\n", "2025-05-29 20:48:02,288 - module.race_data_processor - INFO - レース結果pickleファイルを読み込み中: output\\race_results_2022.pickle\n", "2025-05-29 20:48:02,354 - module.race_data_processor - INFO - レース結果読み込み完了: 47220件\n", "2025-05-29 20:48:02,417 - module.horse_processor - INFO - 馬HTMLファイルのインデックスを構築中...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "ComprehensiveDataIntegrator のインスタンス化に成功しました。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 20:48:02,562 - module.horse_processor - INFO - 馬HTMLファイルのインデックス構築完了。52246件 (0.14秒)\n", "2025-05-29 20:48:04,198 - module.comprehensive_data_integrator - INFO - レース情報: 3456件\n", "2025-05-29 20:48:04,198 - module.comprehensive_data_integrator - INFO - レース結果: 47220件\n", "2025-05-29 20:48:04,199 - module.comprehensive_data_integrator - INFO - 2. ベースデータを作成中...\n", "2025-05-29 20:48:04,216 - module.comprehensive_data_integrator - INFO - 3. レース情報を統合中...\n", "2025-05-29 20:48:04,257 - module.comprehensive_data_integrator - INFO - レース情報統合後: 47220件, 33カラム\n", "2025-05-29 20:48:04,258 - module.comprehensive_data_integrator - INFO - 4. 馬基本情報を統合中...\n", "2025-05-29 20:48:04,265 - module.comprehensive_data_integrator - INFO - レースから抽出した馬ID数: 11557頭\n", "2025-05-29 20:48:04,267 - module.comprehensive_data_integrator - INFO - Pickleから馬基本情報を取得試行 (対象ID: 11557件)\n", "2025-05-29 20:48:04,402 - module.comprehensive_data_integrator - INFO - 馬基本情報統合後: 47220件, 55カラム\n", "2025-05-29 20:48:04,402 - module.comprehensive_data_integrator - INFO - 5. 馬過去成績統計を統合中...\n", "2025-05-29 20:48:04,409 - module.comprehensive_data_integrator - INFO - レースから抽出した馬ID数: 11557頭\n", "2025-05-29 20:48:04,410 - module.comprehensive_data_integrator - INFO - 馬過去成績統計の統合を開始します。\n", "2025-05-29 20:48:04,411 - module.comprehensive_data_integrator - INFO - Pickleから馬過去成績を取得試行...\n", "2025-05-29 20:48:04,642 - module.comprehensive_data_integrator - INFO - 取得・標準化後の馬過去成績データ: 281236件\n", "2025-05-29 20:48:04,861 - module.comprehensive_data_integrator - INFO - 対象馬ID (11557件) で過去成績データをフィルタリングしました。残りの過去成績データ: 281236件\n", "2025-05-29 20:48:05,014 - module.comprehensive_data_integrator - INFO - 過去成績の統計計算対象カラム: ['着順', '人気', 'オッズ', '賞金', '斤量', 'タイム', '上り', '体重', '体重変化']\n", "過去戦績マージ中: 100%|██████████| 108/108 [00:05<00:00, 18.32it/s]\n", "2025-05-29 20:48:10,994 - module.race_data_processor - INFO - 過去戦績のマージが完了しました。処理後の行数: 47220\n", "2025-05-29 20:48:11,016 - module.comprehensive_data_integrator - INFO - 馬過去成績統計の統合が完了しました。処理後の行数: 47220\n", "2025-05-29 20:48:11,045 - module.comprehensive_data_integrator - INFO - 過去成績統計統合後: 47220件, 93カラム\n", "2025-05-29 20:48:11,046 - module.comprehensive_data_integrator - INFO - 包括的データ生成完了: 47220件, 93カラム\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "`generate_comprehensive_table` の実行が完了しました。\n", "\n", "✅ 包括的データ生成完了:\n", "   データ件数: 47,220件\n", "   カラム数: 93個\n", "   ユニークレース数: 3456\n", "   ユニーク馬数: 11557\n", "\n", "📋 サンプルデータ (先頭5行):\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "着順", "rawType": "object", "type": "unknown"}, {"name": "枠番", "rawType": "int64", "type": "integer"}, {"name": "馬番", "rawType": "int64", "type": "integer"}, {"name": "馬名", "rawType": "object", "type": "string"}, {"name": "性齢", "rawType": "object", "type": "string"}, {"name": "斤量", "rawType": "float64", "type": "float"}, {"name": "騎手", "rawType": "object", "type": "string"}, {"name": "タイム", "rawType": "object", "type": "string"}, {"name": "着差", "rawType": "object", "type": "unknown"}, {"name": "ﾀｲﾑ指数", "rawType": "object", "type": "string"}, {"name": "通過", "rawType": "object", "type": "string"}, {"name": "上り", "rawType": "float64", "type": "float"}, {"name": "単勝", "rawType": "object", "type": "unknown"}, {"name": "人気", "rawType": "float64", "type": "float"}, {"name": "馬体重", "rawType": "object", "type": "string"}, {"name": "調教ﾀｲﾑ", "rawType": "float64", "type": "float"}, {"name": "厩舎ｺﾒﾝﾄ", "rawType": "float64", "type": "float"}, {"name": "備考", "rawType": "float64", "type": "float"}, {"name": "調教師", "rawType": "object", "type": "string"}, {"name": "馬主", "rawType": "object", "type": "string"}, {"name": "賞金(万円)", "rawType": "float64", "type": "float"}, {"name": "race_id", "rawType": "string", "type": "string"}, {"name": "horse_id", "rawType": "object", "type": "string"}, {"name": "jockey_id", "rawType": "string", "type": "string"}, {"name": "trainer_id", "rawType": "string", "type": "string"}, {"name": "開催", "rawType": "object", "type": "string"}, {"name": "レース名", "rawType": "object", "type": "string"}, {"name": "date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "course_len", "rawType": "int64", "type": "integer"}, {"name": "weather", "rawType": "object", "type": "string"}, {"name": "race_type", "rawType": "object", "type": "string"}, {"name": "ground_state", "rawType": "object", "type": "string"}, {"name": "around", "rawType": "object", "type": "string"}, {"name": "生年月日", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "調教師_horse_info", "rawType": "object", "type": "string"}, {"name": "馬主_horse_info", "rawType": "object", "type": "string"}, {"name": "生産者", "rawType": "object", "type": "string"}, {"name": "産地", "rawType": "object", "type": "string"}, {"name": "セリ取引価格", "rawType": "object", "type": "string"}, {"name": "獲得賞金", "rawType": "object", "type": "string"}, {"name": "通算成績", "rawType": "object", "type": "string"}, {"name": "主な勝鞍", "rawType": "object", "type": "string"}, {"name": "近親馬", "rawType": "object", "type": "string"}, {"name": "horse_name", "rawType": "object", "type": "string"}, {"name": "trainer_id_horse_info", "rawType": "string", "type": "string"}, {"name": "owner_id", "rawType": "object", "type": "string"}, {"name": "breeder_id", "rawType": "object", "type": "string"}, {"name": "father_name", "rawType": "object", "type": "string"}, {"name": "father_id", "rawType": "string", "type": "string"}, {"name": "mother_name", "rawType": "object", "type": "string"}, {"name": "mother_id", "rawType": "string", "type": "string"}, {"name": "mother_father_name", "rawType": "object", "type": "string"}, {"name": "mother_father_id", "rawType": "string", "type": "string"}, {"name": "sibling_ids", "rawType": "string", "type": "string"}, {"name": "募集情報", "rawType": "object", "type": "unknown"}, {"name": "着順_last_3R_mean", "rawType": "Float64", "type": "float"}, {"name": "人気_last_3R_mean", "rawType": "Float64", "type": "float"}, {"name": "オッズ_last_3R_mean", "rawType": "Float64", "type": "float"}, {"name": "賞金_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_3R_mean", "rawType": "Float64", "type": "float"}, {"name": "タイム_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_last_5R_mean", "rawType": "Float64", "type": "float"}, {"name": "人気_last_5R_mean", "rawType": "Float64", "type": "float"}, {"name": "オッズ_last_5R_mean", "rawType": "Float64", "type": "float"}, {"name": "賞金_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_5R_mean", "rawType": "Float64", "type": "float"}, {"name": "タイム_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_last_10R_mean", "rawType": "Float64", "type": "float"}, {"name": "人気_last_10R_mean", "rawType": "Float64", "type": "float"}, {"name": "オッズ_last_10R_mean", "rawType": "Float64", "type": "float"}, {"name": "賞金_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_10R_mean", "rawType": "Float64", "type": "float"}, {"name": "タイム_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_all_R_mean", "rawType": "Float64", "type": "float"}, {"name": "人気_all_R_mean", "rawType": "Float64", "type": "float"}, {"name": "オッズ_all_R_mean", "rawType": "Float64", "type": "float"}, {"name": "賞金_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_all_R_mean", "rawType": "Float64", "type": "float"}, {"name": "タイム_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "last_race_date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "interval_days", "rawType": "float64", "type": "float"}], "ref": "7c14bc46-4d2b-49ec-aa21-39dff54b8916", "rows": [["0", "1", "8", "15", "ニシノアナ", "牝3", "51.0", "横山琉人", "1:12.5", null, "**", "2-2", "37.6", "6.8", "4.0", "456(+4)", null, null, null, "[東] 相沢郁", "西山茂行", "520.0", "202206010101", "2019103610", "01192", "01020", "06", "3歳未勝利", "2022-01-05 00:00:00", "1200", "晴", "ダート", "良", "", "2019-03-03 00:00:00", "川島洋人 (北海道)", "西山茂行", "杵臼牧場", "浦河町", "-", "1,540万円 (中央) /60万円 (地方)", "29戦2勝 [2-1-2-24]", "22'3歳以上1勝クラス", "ブルベアモル, ポセイドンバローズ", "ニシノアナ (<PERSON><PERSON><PERSON>)", "05706", "897009", "400032", "ドレフォン", "000a013b82", "<PERSON><PERSON><PERSON><PERSON>", "000a013b81", "レッドセイリング", "2010104204", "2017105999, 2022102752", null, "9.666666666666666", "8.333333333333334", "35.36666666666667", "43.333333333333336", "53.0", null, "39.766666666666666", "454.0", "-5.333333333333333", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "2021-12-12 00:00:00", "24.0"], ["1", "2", "5", "10", "トラストパッキャオ", "牝3", "54.0", "菅原明良", "1:12.5", "クビ", "**", "1-1", "37.7", "57.2", "12.0", "458(+2)", null, null, null, "[東] 高木登", "菅波雅巳", "210.0", "202206010101", "2019100855", "01179", "01088", "06", "3歳未勝利", "2022-01-05 00:00:00", "1200", "晴", "ダート", "良", "", "2019-02-14 00:00:00", "田中守 (高知)", "菅波雅巳", "松浦牧場", "新冠町", "-", "1,578万円 (中央) /90万円 (地方)", "19戦3勝 [3-2-0-14]", "22'3歳以上1勝クラス", "サンナイト, ジュランビル", "トラストパッキャオ (Trust Pacquiao)", "05580", "125008", "430316", "ヘニーヒューズ", "000a011155", "Meadow Flyer", "000a01117d", "アリー", "000a011502", "2014100781, 2016100794", null, "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "2021-09-19 00:00:00", "108.0"], ["2", "3", "2", "4", "マイネルシトラス", "牡3", "56.0", "柴田大知", "1:12.5", "クビ", "**", "4-3", "37.3", "3.7", "1.0", "518(-2)", null, null, null, "[東] 武市康男", "サラブレッドクラブ・ラフィアン", "130.0", "202206010101", "2019103542", "01009", "01089", "06", "3歳未勝利", "2022-01-05 00:00:00", "1200", "晴", "ダート", "良", "", "2019-05-06 00:00:00", "打越勇児 (高知)", "（株）サラブレッドク", "金石牧場", "浦河町", "-", "3,129万円 (中央) /3,049万円 (地方)", "40戦10勝 [10-4-9-17]", "大高坂賞", "マイネルブリオン, マイネルサンタアナ", "マイネルシトラス (Meiner Citrus)", "a02b0", "546800", "010035", "ジョーカプチーノ", "2006100529", "ジョープシケ", "2000102239", "マイネオレンジ", "2010105940", "2022102681, 2016100734", "1口:15万円/100口", "3.3333333333333335", "6.0", "25.066666666666663", "127.0", "55.0", null, "37.96666666666667", "518.0", "2.0", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "2021-12-11 00:00:00", "25.0"], ["3", "4", "1", "2", "ピカリエ", "牝3", "54.0", "伊藤工真", "1:12.8", "1.1/2", "**", "10-7", "37.2", "16.0", "9.0", "486(+6)", null, null, null, "[東] 金成貴史", "リトルブルーファーム", "78.0", "202206010101", "2019104288", "01119", "01132", "06", "3歳未勝利", "2022-01-05 00:00:00", "1200", "晴", "ダート", "良", "", "2019-03-14 00:00:00", "金成貴史 (美浦)", "リトルブルーファーム", "リトルブルーファーム", "清水町", "-", "4,171万円 (中央)", "26戦2勝 [2-5-4-15]", "24'４歳上５００万下", "オヒナサマ, アマクサマンボ", "ピカリエ (Pi<PERSON><PERSON>)", "01132", "064803", "150100", "エイシンヒカリ", "2011101273", "キャタリナ", "000a00b4d3", "バンデリータ", "2003103432", "2014104165, 2010106217", null, "11.0", "15.333333333333334", "234.79999999999998", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.79999999999998", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.79999999999998", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.79999999999998", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "2021-12-12 00:00:00", "24.0"], ["4", "5", "8", "16", "ブラッドライン", "牡3", "56.0", "Ｍ．デム", "1:13.2", "2.1/2", "**", "4-5", "38.0", "10.0", "5.0", "478(-2)", null, null, null, "[東] 伊藤大士", "前原敏行", "52.0", "202206010101", "2019101003", "05212", "01109", "06", "3歳未勝利", "2022-01-05 00:00:00", "1200", "晴", "ダート", "良", "", "2019-03-31 00:00:00", "田島寿一 (川崎)", "神田雅行", "村田牧場", "新冠町", "1,705万円 (2021年 千葉サラブレッドセール)", "180万円 (中央) /908万円 (地方)", "23戦6勝 [6-5-2-10]", "馬い!八幡平バイオレット記念", "ハセノブライアン, ハセノエクスプレス", "ブラッドライン (Bloodline)", "05569", "x0905f", "400317", "ローレルゲレイロ", "2004100933", "ビッグテンビー", "1998100880", "フルオブスターズ", "2013102086", "2022107078, 2018102358", null, "6.333333333333333", "3.0", "7.0", "42.666666666666664", "54.666666666666664", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.666666666666664", "54.666666666666664", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.666666666666664", "54.666666666666664", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.666666666666664", "54.666666666666664", null, "37.96666666666667", "474.0", "4.0", "2021-12-18 00:00:00", "18.0"]], "shape": {"columns": 93, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>着順</th>\n", "      <th>枠番</th>\n", "      <th>馬番</th>\n", "      <th>馬名</th>\n", "      <th>性齢</th>\n", "      <th>斤量</th>\n", "      <th>騎手</th>\n", "      <th>タイム</th>\n", "      <th>着差</th>\n", "      <th>ﾀｲﾑ指数</th>\n", "      <th>...</th>\n", "      <th>人気_all_R_mean</th>\n", "      <th>オッズ_all_R_mean</th>\n", "      <th>賞金_all_R_mean</th>\n", "      <th>斤量_all_R_mean</th>\n", "      <th>タイム_all_R_mean</th>\n", "      <th>上り_all_R_mean</th>\n", "      <th>体重_all_R_mean</th>\n", "      <th>体重変化_all_R_mean</th>\n", "      <th>last_race_date</th>\n", "      <th>interval_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>15</td>\n", "      <td>ニシノアナ</td>\n", "      <td>牝3</td>\n", "      <td>51.0</td>\n", "      <td>横山琉人</td>\n", "      <td>1:12.5</td>\n", "      <td>NaN</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>7.0</td>\n", "      <td>28.3</td>\n", "      <td>32.500000</td>\n", "      <td>53.25</td>\n", "      <td>NaN</td>\n", "      <td>38.850000</td>\n", "      <td>457.500000</td>\n", "      <td>-4.000000</td>\n", "      <td>2021-12-12</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>10</td>\n", "      <td>トラストパッキャオ</td>\n", "      <td>牝3</td>\n", "      <td>54.0</td>\n", "      <td>菅原明良</td>\n", "      <td>1:12.5</td>\n", "      <td>クビ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>8.0</td>\n", "      <td>24.75</td>\n", "      <td>0.000000</td>\n", "      <td>54.0</td>\n", "      <td>NaN</td>\n", "      <td>39.250000</td>\n", "      <td>455.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2021-09-19</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>マイネルシトラス</td>\n", "      <td>牡3</td>\n", "      <td>56.0</td>\n", "      <td>柴田大知</td>\n", "      <td>1:12.5</td>\n", "      <td>クビ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>8.4</td>\n", "      <td>52.08</td>\n", "      <td>76.200000</td>\n", "      <td>55.0</td>\n", "      <td>NaN</td>\n", "      <td>37.880000</td>\n", "      <td>516.000000</td>\n", "      <td>1.600000</td>\n", "      <td>2021-12-11</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>ピカリエ</td>\n", "      <td>牝3</td>\n", "      <td>54.0</td>\n", "      <td>伊藤工真</td>\n", "      <td>1:12.8</td>\n", "      <td>1.1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>15.333333</td>\n", "      <td>234.8</td>\n", "      <td>17.000000</td>\n", "      <td>54.0</td>\n", "      <td>NaN</td>\n", "      <td>38.000000</td>\n", "      <td>477.333333</td>\n", "      <td>-0.666667</td>\n", "      <td>2021-12-12</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>16</td>\n", "      <td>ブラッドライン</td>\n", "      <td>牡3</td>\n", "      <td>56.0</td>\n", "      <td>Ｍ．デム</td>\n", "      <td>1:13.2</td>\n", "      <td>2.1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>3.0</td>\n", "      <td>7.0</td>\n", "      <td>42.666667</td>\n", "      <td>54.666667</td>\n", "      <td>NaN</td>\n", "      <td>37.966667</td>\n", "      <td>474.000000</td>\n", "      <td>4.000000</td>\n", "      <td>2021-12-18</td>\n", "      <td>18.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 93 columns</p>\n", "</div>"], "text/plain": ["  着順  枠番  馬番         馬名  性齢    斤量    騎手     タイム     着差 ﾀｲﾑ指数  ...  \\\n", "0  1   8  15      ニシノアナ  牝3  51.0  横山琉人  1:12.5    NaN    **  ...   \n", "1  2   5  10  トラストパッキャオ  牝3  54.0  菅原明良  1:12.5     クビ    **  ...   \n", "2  3   2   4   マイネルシトラス  牡3  56.0  柴田大知  1:12.5     クビ    **  ...   \n", "3  4   1   2       ピカリエ  牝3  54.0  伊藤工真  1:12.8  1.1/2    **  ...   \n", "4  5   8  16    ブラッドライン  牡3  56.0  Ｍ．デム  1:13.2  2.1/2    **  ...   \n", "\n", "  人気_all_R_mean  オッズ_all_R_mean 賞金_all_R_mean  斤量_all_R_mean タイム_all_R_mean  \\\n", "0           7.0            28.3     32.500000          53.25            NaN   \n", "1           8.0           24.75      0.000000           54.0            NaN   \n", "2           8.4           52.08     76.200000           55.0            NaN   \n", "3     15.333333           234.8     17.000000           54.0            NaN   \n", "4           3.0             7.0     42.666667      54.666667            NaN   \n", "\n", "   上り_all_R_mean  体重_all_R_mean  体重変化_all_R_mean last_race_date interval_days  \n", "0      38.850000     457.500000        -4.000000     2021-12-12          24.0  \n", "1      39.250000     455.000000         1.000000     2021-09-19         108.0  \n", "2      37.880000     516.000000         1.600000     2021-12-11          25.0  \n", "3      38.000000     477.333333        -0.666667     2021-12-12          24.0  \n", "4      37.966667     474.000000         4.000000     2021-12-18          18.0  \n", "\n", "[5 rows x 93 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 データ概要:\n", "  status: success\n", "  total_records: 47220\n", "  total_columns: 93\n", "  unique_races: 3456\n", "  unique_horses: 11557\n", "  data_columns:\n", "    race_info_columns: 1個\n", "    horse_info_columns: 11個\n", "    performance_columns: 37個\n", "    basic_columns: 7個\n", "  missing_data_ratio (上位5件):\n", "    調教ﾀｲﾑ: 100.00%\n", "    厩舎ｺﾒﾝﾄ: 100.00%\n", "    備考: 100.00%\n", "    タイム_last_3R_mean: 100.00%\n", "    タイム_last_5R_mean: 100.00%\n"]}], "source": ["# %% [markdown]\n", "# # ComprehensiveDataIntegrator の利用サンプル\n", "# \n", "# このノートブックでは、`ComprehensiveDataIntegrator` を使用して、\n", "# レース情報、レース結果、馬の基本情報、馬の過去成績を統合した\n", "# 包括的なデータテーブルを生成する方法を示します。\n", "\n", "# %%\n", "import os\n", "import pandas as pd\n", "import logging\n", "\n", "# 必要なモジュールをインポート\n", "# (プロジェクトのルートディレクトリからの相対パスで指定することを想定)\n", "# 必要に応じて sys.path にプロジェクトのパスを追加してください。\n", "# import sys\n", "# sys.path.append('F:/keiba__AI_2025') # ご自身のプロジェクトパスに合わせてください\n", "\n", "from module.comprehensive_data_integrator import ComprehensiveDataIntegrator, load_config_from_file, merge_settings\n", "from module.constants import LocalPaths, ComprehensiveIntegratorConfig # 設定クラスもインポート\n", "\n", "# %% [markdown]\n", "# ## 1. ロギング設定\n", "# \n", "# 詳細な処理状況を確認するためにロギングを設定します。\n", "\n", "# %%\n", "logging.basicConfig(level=logging.INFO,\n", "                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "                    handlers=[logging.StreamHandler()])\n", "logger = logging.getLogger() # ルートロガーを取得\n", "logger.setLevel(logging.INFO) # INFOレベル以上のログを出力\n", "# --- 設定の選択 (上記いずれかの方法で config_dict を準備) ---\n", "# 例: Python辞書で設定する場合\n", "config_dict_for_integrator = {\n", "    # \"default_year\": \"2023\", # generate_comprehensive_table の引数で指定する場合は不要\n", "    \"default_years\": [\"2022\"],\n", "    \"use_pickle_source\": True, # pickleファイルから読み込むことを指定\n", "    \"pickle_base_dir\": output_dir, #pickleファイルが格納されているディレクトリ\n", "    #各pickleファイルのファイル名テンプレート (デフォルト値の例)\n", "    \"race_info_filename_template\": \"race_info_{year}.pickle\", #RaceProcessor側でデフォルトを持つ想定\n", "    \"race_results_filename_template\": \"race_results_{year}.pickle\", #RaceProcessor側でデフォルトを持つ想定\n", "    \"horse_info_pickle_filename_template\": \"race_horses_horse_info_{year}.pickle\", #ComprehensiveIntegratorConfigで定義可能\n", "    \"horse_results_pickle_filename_template\": \"race_horses_horse_results_{year}.pickle\", #ComprehensiveIntegratorConfigで定義可能\n", "\n", "    # カスタム特徴量pickle (使用しない場合は enable_custom_feature_pickle: False)\n", "    \"enable_custom_feature_pickle\": False,\n", "    # \"custom_feature_pickle_filename_template\": \"custom_features_{year}.pickle\",\n", "\n", "    \"include_race_info\": True,\n", "    \"include_horse_info\": True,\n", "    \"include_past_performance\": True,\n", "    \"performance_window_races\": [3, 5, 10], # 直近3, 5, 10レースの成績を集計\n", "    \"parallel\": True,\n", "    \"max_workers\": os.cpu_count() or 1, # 環境に合わせて調整\n", "    \"save_output\": False # NotebookではまずFalseで結果確認\n", "}\n", "\n", "# 設定ファイル (config.json) があればそれも読み込む (オプション)\n", "# config_file_path = \"config.json\" \n", "# file_config = load_config_from_file(config_file_path)\n", "# final_settings = merge_settings(file_config, argparse.Namespace(**config_dict_for_integrator)) # argparse.Namespaceでラップ\n", "\n", "# ここでは直接 config_dict_for_integrator を使用\n", "final_settings = config_dict_for_integrator\n", "\n", "print(\"\\n最終的な実行設定:\")\n", "for key, value in final_settings.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# %% [markdown]\n", "# ## 3. `ComprehensiveDataIntegrator` のインスタンス化\n", "# \n", "# 準備した設定で `ComprehensiveDataIntegrator` のインスタンスを作成します。\n", "\n", "# %%\n", "try:\n", "    integrator = ComprehensiveDataIntegrator(config=final_settings)\n", "    print(\"\\nComprehensiveDataIntegrator のインスタンス化に成功しました。\")\n", "except Exception as e:\n", "    logger.error(f\"ComprehensiveDataIntegrator のインスタンス化中にエラーが発生しました: {e}\", exc_info=True)\n", "    integrator = None # エラー時は None にしておく\n", "\n", "# %% [markdown]\n", "# ## 4. `generate_comprehensive_table` メソッドの実行\n", "# \n", "# 包括的なデータテーブルを生成します。\n", "# `years` 引数で処理対象の年度を明示的に指定することも可能です。\n", "# \n", "# **注意:** 初回実行時やHTMLからデータを取得する場合、時間がかかることがあります。\n", "# pickleファイルが存在しない場合、`HorseProcessor` や `RaceProcessor` がHTMLからの読み込みにフォールバックしようとします。\n", "# その際にHTMLファイルが存在しないと、データが空になる可能性があります。\n", "# \n", "# 事前に `RaceDataProcessor` や `HorseDataProcessor` のスクリプトを実行して、\n", "# 必要なpickleファイル (`race_info_{year}.pickle`, `race_results_{year}.pickle`, `horse_info_{year}.pickle`, `horse_results_{year}.pickle` など) を\n", "# `pickle_base_dir` で指定したディレクトリに配置しておいてください。\n", "\n", "# %%\n", "if integrator:\n", "    try:\n", "        # generate_comprehensive_table の引数で設定を上書きすることも可能\n", "        # ここではインスタンス化時の設定を使用\n", "        comprehensive_df = integrator.generate_comprehensive_table(\n", "            # years=[\"2023\"], # 特定の年だけ処理したい場合\n", "            # use_pickle_source=True # メソッド呼び出し時にも指定可能\n", "        )\n", "        \n", "        print(\"\\n`generate_comprehensive_table` の実行が完了しました。\")\n", "    except Exception as e:\n", "        logger.error(f\"`generate_comprehensive_table` の実行中にエラーが発生しました: {e}\", exc_info=True)\n", "        comprehensive_df = pd.DataFrame() # エラー時は空のDataFrame\n", "else:\n", "    print(\"Integratorのインスタンスが作成されていないため、処理をスキップします。\")\n", "    comprehensive_df = pd.DataFrame()\n", "\n", "# %% [markdown]\n", "# ## 5. 結果の確認と利用\n", "\n", "# %%\n", "if not comprehensive_df.empty:\n", "    print(f\"\\n✅ 包括的データ生成完了:\")\n", "    print(f\"   データ件数: {len(comprehensive_df):,}件\")\n", "    print(f\"   カラム数: {len(comprehensive_df.columns)}個\")\n", "    if 'race_id' in comprehensive_df.columns:\n", "        print(f\"   ユニークレース数: {comprehensive_df['race_id'].nunique()}\")\n", "    if 'horse_id' in comprehensive_df.columns:\n", "        print(f\"   ユニーク馬数: {comprehensive_df['horse_id'].nunique()}\")\n", "\n", "    print(\"\\n📋 サンプルデータ (先頭5行):\")\n", "    display(comprehensive_df.head())\n", "\n", "    print(\"\\n📊 データ概要:\")\n", "    summary = integrator.get_data_summary()\n", "    for key, value in summary.items():\n", "        if key == \"data_columns\":\n", "            print(f\"  {key}:\")\n", "            for cat, cols in value.items():\n", "                print(f\"    {cat}: {len(cols)}個\")\n", "        elif key == \"missing_data_ratio\":\n", "            print(f\"  {key} (上位5件):\")\n", "            sorted_missing = sorted(value.items(), key=lambda item: item[1], reverse=True)\n", "            for col, ratio in sorted_missing[:5]:\n", "                if ratio > 0:\n", "                    print(f\"    {col}: {ratio:.2%}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "    \n", "    # カラム一覧\n", "    # print(\"\\nカラム一覧:\")\n", "    # for col in comprehensive_df.columns:\n", "    #     print(f\"- {col}\")\n", "\n", "else:\n", "    print(\"\\n❌ 生成されたデータは空です。ログを確認してエラーの原因を調査してください。\")\n", "    print(\"考えられる原因:\")\n", "    print(\"- 指定したpickleファイルが存在しない、またはパスが間違っている。\")\n", "    print(\"- pickleファイルの中身が空、または期待する形式ではない。\")\n", "    print(\"- HTMLからの読み込みにフォールバックしたが、HTMLファイルが存在しない。\")\n", "    print(\"- データ処理中に何らかのエラーが発生した。\")\n", "\n", "# %% [markdown]\n", "# ## 6. (オプション) 結果のファイル保存\n", "# \n", "# 生成されたデータをCSVやPickle形式で保存します。\n", "\n", "# %%\n", "# save_output を True にして実行する場合\n", "# if integrator and not comprehensive_df.empty and final_settings.get(\"save_output\", False):\n", "#     try:\n", "#         current_years = final_settings.get('default_years', [])\n", "#         if isinstance(current_years, list) and len(current_years) > 1:\n", "#             year_range_str = f\"{min(current_years)}-{max(current_years)}\"\n", "#             filename_prefix_for_save = f\"{final_settings.get('filename_prefix', integrator.config.filename_prefix)}_multi_year_{year_range_str}\"\n", "#             save_year_param = None\n", "#         else:\n", "#             filename_prefix_for_save = final_settings.get('filename_prefix', integrator.config.filename_prefix)\n", "#             save_year_param = current_years[0] if isinstance(current_years, list) and current_years else final_settings.get('default_year', integrator.config.default_year)\n", "        \n", "#         pickle_path, csv_path = integrator.save_comprehensive_table(\n", "#             filename_prefix=filename_prefix_for_save,\n", "#             year=save_year_param, # 単一年度の場合のみ年を指定\n", "#             save_pickle=True,\n", "#             save_csv=True\n", "#         )\n", "#         print(\"\\n💾 ファイル保存完了:\")\n", "#         if pickle_path:\n", "#             print(f\"   Pickle: {os.path.abspath(pickle_path)}\")\n", "#         if csv_path:\n", "#             print(f\"   CSV: {os.path.abspath(csv_path)}\")\n", "#     except Exception as e:\n", "#         logger.error(f\"ファイル保存中にエラー: {e}\", exc_info=True)\n", "# elif integrator and comprehensive_df.empty:\n", "#     print(\"データが空のため、ファイル保存はスキップされました。\")\n", "# else:\n", "#     print(\"ファイル保存は設定されていません (save_output=False)。\")\n", "\n", "# %% [markdown]\n", "# ---\n", "# これで、`ComprehensiveDataIntegrator` を使用してデータを統合し、結果を確認する基本的な流れは完了です。\n", "# 設定や処理対象のデータを変更して、さまざまなパターンのデータ統合を試してみてください。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 関連度スコア生成後 ---\n", "        race_id    horse_id  着順  num_starters_in_race  relevance_score\n", "0  202206010101  2019103610   1                    16               16\n", "1  202206010101  2019100855   2                    16               15\n", "2  202206010101  2019103542   3                    16               14\n", "3  202206010101  2019104288   4                    16               13\n", "4  202206010101  2019101003   5                    16               12\n", "特徴量として使用するカラム: ['枠番', '馬番', '馬名', '性齢', '斤量', 'タイム', '着差', 'ﾀｲﾑ指数', '通過', '上り', '人気', '馬体重', 'jockey_id', 'trainer_id', '開催', 'course_len', 'weather', 'race_type', 'ground_state', 'owner_id', 'breeder_id', 'father_id', 'mother_id', 'mother_father_id', '着順_last_3R_mean', '人気_last_3R_mean', 'オッズ_last_3R_mean', '賞金_last_3R_mean', '斤量_last_3R_mean', 'タイム_last_3R_mean', '上り_last_3R_mean', '体重_last_3R_mean', '体重変化_last_3R_mean', '着順_last_5R_mean', '人気_last_5R_mean', 'オッズ_last_5R_mean', '賞金_last_5R_mean', '斤量_last_5R_mean', 'タイム_last_5R_mean', '上り_last_5R_mean', '体重_last_5R_mean', '体重変化_last_5R_mean', '着順_last_10R_mean', '人気_last_10R_mean', 'オッズ_last_10R_mean', '賞金_last_10R_mean', '斤量_last_10R_mean', 'タイム_last_10R_mean', '上り_last_10R_mean', '体重_last_10R_mean', '体重変化_last_10R_mean', '着順_all_R_mean', '人気_all_R_mean', 'オッズ_all_R_mean', '賞金_all_R_mean', '斤量_all_R_mean', 'タイム_all_R_mean', '上り_all_R_mean', '体重_all_R_mean', '体重変化_all_R_mean', 'interval_days']\n", "自動決定された分割日 (この日より前のデータを学習、以降をテスト): 2022-11-28\n", "学習データ: X_train (42750, 61), y_train (42750,), group_train 3168 groups\n", "学習データのカテゴリカル特徴量: ['jockey_id', 'trainer_id', 'father_id', 'mother_id', 'mother_father_id']\n", "テストデータ: X_test (4090, 61), y_test (4090,), group_test 288 groups\n", "テストデータのカテゴリカル特徴量: ['jockey_id', 'trainer_id', 'father_id', 'mother_id', 'mother_father_id']\n", "\n", "予測結果（テストデータ）:\n"]}], "source": ["from datetime import timedelta\n", "import pandas as pd # pandas をインポート\n", "import numpy as np # numpy をインポート (ダミーデータ用)\n", "from module.constants import HorseInfoCols # HorseInfoCols.BIRTHDAY を使用するためにインポート\n", "\n", "# from module.constants import ResultsCols # 定義されている場合\n", "actual_rank_col = '着順'\n", "# actual_rank_col = ResultsCols.RANK # 定数を使用する場合\n", "\n", "# '着順' カラムの存在を確認 (ResultsCols.RANK を使うのが望ましい)\n", "if actual_rank_col not in comprehensive_df.columns:\n", "    if 'rank' in comprehensive_df.columns: # 'rank' カラムでフォールバック\n", "        actual_rank_col = 'rank'\n", "        print(f\"警告: '着順' カラムが見つからないため、'{actual_rank_col}' カラムを使用します。\")\n", "    else:\n", "        raise KeyError(f\"関連度スコアの生成に必要な着順カラム ('着順' または 'rank') がデータフレームに存在しません。\")\n", "\n", "# 1. '着順' カラムを数値型に変換\n", "comprehensive_df[actual_rank_col] = pd.to_numeric(comprehensive_df[actual_rank_col], errors='coerce')\n", "\n", "# NaNが含まれている場合の処理\n", "if comprehensive_df[actual_rank_col].isnull().any():\n", "    num_rows_before_dropna = len(comprehensive_df)\n", "    # 数値に変換できなかった行 (NaNになった行) を除外\n", "    comprehensive_df.dropna(subset=[actual_rank_col], inplace=True)\n", "    num_rows_after_dropna = len(comprehensive_df)\n", "    num_dropped = num_rows_before_dropna - num_rows_after_dropna\n", "    if num_dropped > 0:\n", "        print(f\"情報: '{actual_rank_col}' カラムに数値変換できない値、または欠損値が含まれていたため、{num_dropped}行が除外されました。\")\n", "\n", "    if comprehensive_df.empty:\n", "        raise ValueError(\n", "            f\"'{actual_rank_col}' カラムの数値変換不能な行を除外した結果、DataFrameが空になりました。\"\n", "            f\"有効な着順データを持つ行が存在しません。\"\n", "        )\n", "    comprehensive_df[actual_rank_col] = comprehensive_df[actual_rank_col].astype(int)\n", "else:\n", "    # NaN がない場合でも、pd.to_numeric は float を返すことがあるため int に変換\n", "    # (例: \"1\" が 1.0 になる)。元々全て整数で欠損がなければこの変換は厳密には不要ですが、\n", "    # 確実性を期すために行います。\n", "    if not comprehensive_df.empty: # DataFrameが空でないことを確認\n", "        comprehensive_df[actual_rank_col] = comprehensive_df[actual_rank_col].astype(int)\n", "    elif actual_rank_col in comprehensive_df.columns: # カラムは存在するがDFが空の場合\n", "        # comprehensive_df が初期状態で空の場合、このelseブロックは実行されるが、\n", "        # comprehensive_df[actual_rank_col] は空のSeriesなので astype(int) は問題ない。\n", "        # ただし、この状況は通常、入力データが空であることを意味する。\n", "        pass\n", "\n", "\n", "# 2. 数値型に変換したカラムを使って計算\n", "# comprehensive_dfが空の場合、以降の処理でエラーが発生する可能性があるため、チェックを追加\n", "if comprehensive_df.empty:\n", "    # このエラーは、上記の NaN 処理で DataFrame が空になった場合やすでに捕捉されているか、\n", "    # または初期データが空だった場合に発生します。\n", "    print(\"警告: 関連度スコア計算の前にDataFrameが空です。関連度スコアは計算されません。\")\n", "    # この後の処理で空のDataFrameを扱うことになるため、各処理が空のDataFrameに対応しているか確認が必要。\n", "    # もしくは、ここで raise ValueError(\"処理対象のデータがありません。\") のように処理を中断する。\n", "else:\n", "    # 各レースの出走頭数（= そのレースでの最大着順）を取得します。\n", "    comprehensive_df['num_starters_in_race'] = comprehensive_df.groupby('race_id')[actual_rank_col].transform('max')\n", "    # 関連度スコアを計算します: (出走頭数 - 実際の着順 + 1)\n", "    # comprehensive_df['relevance_score'] = comprehensive_df['num_starters_in_race'] - comprehensive_df[actual_rank_col] + 1\n", "    def calculate_relative_scores(series_rank): # 引数は各グループの着順Series\n", "        max_rank_in_group = series_rank.max()\n", "        return max_rank_in_group + 1 - series_rank\n", "\n", "    comprehensive_df['relevance_score'] = comprehensive_df.groupby('race_id')[actual_rank_col].transform(calculate_relative_scores)\n", "\n", "y_label_column_name = 'relevance_score'\n", "print(\"--- 関連度スコア生成後 ---\")\n", "if not comprehensive_df.empty:\n", "    print(comprehensive_df[['race_id', 'horse_id', actual_rank_col, 'num_starters_in_race', y_label_column_name]].head())\n", "else:\n", "    # y_label_column_name が 'relevance_score' であり、これが生成されていない可能性があるため、\n", "    # 表示するカラムを調整するか、メッセージのみ表示する。\n", "    print(f\"DataFrameが空のため、関連度スコアを含む情報を表示できません。表示予定のカラム: ['race_id', 'horse_id', '{actual_rank_col}', 'num_starters_in_race', '{y_label_column_name}']\")\n", "\n", "\n", "# # --- 関連度スコアの生成 (固定スコア) ---\n", "\n", "# # `label_gain` を指定しなくてもモデルはスコアが高いものを上位にしようと学習します。\n", "\n", "# 特徴量の選択\n", "# 'rank' カラムは、もし actual_rank_col と同じなら y_label_column_name の元データなので除外。\n", "# もし 'rank' が actual_rank_col と異なり、かつ特徴量として使いたくない別のカラムなら、リストに含める。\n", "# ここでは、actual_rank_col (元 '着順') を特徴量から除外する。\n", "columns_to_exclude = [\n", "    'race_id', 'horse_id', 'date', y_label_column_name, actual_rank_col,\n", "    'last_race_date', # datetime64型なので除外 (interval_days を使用)\n", "    HorseInfoCols.BIRTHDAY,  # datetime64型なので除外 (年齢カラムを使用)\n", "    '騎手', '単勝', '調教ﾀｲﾑ', '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考', '調教師', '馬主', '賞金(万円)', 'レース名',\n", "    'around', '調教師_horse_info', '馬主_horse_info', '生産者', '産地', 'セリ取引価格', '獲得賞金', '通算成績', '主な勝鞍', '近親馬', 'horse_name', 'trainer_id_horse_info',\n", "    'father_name', 'mother_father_name', 'mother_name' ,'sibling_ids', '募集情報',\n", "    'タイム', '着差', 'ﾀｲﾑ指数', '通過', '上り', '人気', 'weather', 'race_type', 'ground_state', 'mother_id' ,\n", "]\n", "if 'rank' in comprehensive_df.columns and 'rank' != actual_rank_col: # 'rank'が別カラムならそれも除外\n", "    columns_to_exclude.append('rank')\n", "# num_starters_in_race も relevance_score の計算に使ったので、リークを避けるため除外した方が良い場合がある。\n", "# ただし、レースの規模を示す特徴量として有効な場合もあるので、ここでは残す。\n", "columns_to_exclude.append('num_starters_in_race')\n", "feature_columns = [\n", "    col for col in comprehensive_df.columns\n", "        if col not in columns_to_exclude\n", "]\n", "print(f\"特徴量として使用するカラム: {feature_columns}\")\n", "\n", "# 欠損値処理 (データ型に応じて処理を分岐)\n", "for col in feature_columns:\n", "    if comprehensive_df[col].dtype == pd.StringDtype():\n", "        # StringDtype のカラムは、カテゴリカル特徴量として扱うことを想定し、\n", "        # 欠損値を表す特定の文字列で埋める。\n", "        comprehensive_df[col] = comprehensive_df[col].fillna(\"_NaN_\") # または pd.NA や \"_UNKNOWN_\"\n", "        # LightGBMでカテゴリカル特徴量として扱う場合、pd.NAより具体的な文字列が良い場合がある。\n", "    elif pd.api.types.is_numeric_dtype(comprehensive_df[col].dtype):\n", "        # 既に数値型のカラム\n", "        comprehensive_df[col] = comprehensive_df[col].fillna(-999.0)\n", "    elif comprehensive_df[col].dtype == 'object':\n", "        # object型のカラムは、まず数値への変換を試みる\n", "        try:\n", "            comprehensive_df[col] = pd.to_numeric(comprehensive_df[col], errors='coerce').fillna(-999.0)\n", "        except Exception:\n", "            # 数値に変換できないobject型（文字列など）は、特定の文字列で埋める\n", "            comprehensive_df[col] = comprehensive_df[col].fillna(\"_UNKNOWN_\")\n", "    # 他の型（datetimeなど）は、必要に応じて個別の欠損値処理を行う\n", "# データをレースIDと日付でソート\n", "\n", "# --- 2. データの分割 (時系列を考慮し、自動的に分割日を決定) ---\n", "if 'date' not in comprehensive_df.columns:\n", "    raise KeyError(\"データ分割に必要な 'date' カラムがデータフレームに存在しません。\")\n", "if not pd.api.types.is_datetime64_any_dtype(comprehensive_df['date']):\n", "    comprehensive_df['date'] = pd.to_datetime(comprehensive_df['date'], errors='coerce')\n", "    if comprehensive_df['date'].isnull().any():\n", "# 日付変換でNaNが発生した場合の処理（例：警告、エラー、除外など）\n", "        print(\"警告: 'date' カラムに日付に変換できない値が含まれており、NaNになりました。\")\n", "        # raise ValueError(\"日付カラム ('date') に不正な値が含まれています。変換できませんでした。\")\n", "\n", "if comprehensive_df.empty or comprehensive_df['date'].isnull().all():\n", "    raise ValueError(\"データフレームが空か、有効な日付データがありません。分割できません。\")\n", "\n", "# 利用可能なデータの最大日付を取得\n", "max_data_date = comprehensive_df['date'].max()\n", "\n", "# 分割戦略: 例えば、最新のN日間をテストデータとする\n", "test_period_days = 30  # 例: 最新の30日間をテストデータとする\n", "\n", "if pd.isna(max_data_date):\n", "    raise ValueError(\"最大日付が取得できませんでした。\")\n", "\n", "# split_date をここで定義\n", "split_date = max_data_date - timedelta(days=test_period_days)\n", "\n", "# もし、学習データが極端に少なくなる場合や、テストデータが存在しない場合の調整\n", "min_train_races = 5 # 学習データとして最低限確保したいレース数（目安）\n", "min_test_races = 1  # テストデータとして最低限確保したいレース数（目安）\n", "\n", "# 全レース数を取得\n", "total_unique_races = comprehensive_df['race_id'].nunique()\n", "\n", "# 学習データとテストデータのレース数を計算\n", "potential_train_races = comprehensive_df[comprehensive_df['date'] < split_date]['race_id'].nunique()\n", "potential_test_races = comprehensive_df[comprehensive_df['date'] >= split_date]['race_id'].nunique()\n", "\n", "if potential_train_races < min_train_races and total_unique_races > (min_train_races + min_test_races):\n", "    # 学習データが少なすぎる場合、テスト期間を短縮して学習データを増やす\n", "    # (ただし、テストデータが最低限確保できる範囲で)\n", "    print(f\"警告: 自動決定されたsplit_date ({split_date.strftime('%Y-%m-%d')}) では学習レース数が不足 ({potential_train_races} レース)。\")\n", "    # split_date を調整するロジック (例: テストレース数がmin_test_racesになるようにsplit_dateを前にずらす)\n", "    # ここでは簡略化のため、日付ベースの分割を優先し、警告のみとします。\n", "    # より高度な調整が必要な場合は、レースIDのリストで分割点を決めるなどの方法も考えられます。\n", "    # 例: sorted_race_ids = comprehensive_df['race_id'].unique()\n", "    #      split_race_id_index = len(sorted_race_ids) - min_test_races\n", "    #      if split_race_id_index > 0:\n", "    #          split_race_id = sorted_race_ids[split_race_id_index]\n", "    #          split_date = comprehensive_df[comprehensive_df['race_id'] == split_race_id]['date'].min()\n", "    #          print(f\"split_dateを {split_date.strftime('%Y-%m-%d')} に調整しました。\")\n", "elif potential_test_races < min_test_races and total_unique_races > (min_train_races + min_test_races):\n", "    # テストデータが少なすぎる場合、学習期間を短縮してテストデータを増やす\n", "    print(f\"警告: 自動決定されたsplit_date ({split_date.strftime('%Y-%m-%d')}) ではテストレース数が不足 ({potential_test_races} レース)。\")\n", "    # split_date を調整するロジック (例: 学習レース数がmin_train_racesになるようにsplit_dateを後ろにずらす)\n", "elif comprehensive_df['date'].min() >= split_date and comprehensive_df['date'].nunique() > 1 :\n", "    # 全データがテスト期間に入ってしまう場合 (かつ日付が複数ある場合)\n", "    # データの中間点で分割する\n", "    sorted_unique_dates = sorted(comprehensive_df['date'].unique())\n", "    split_point_index = len(sorted_unique_dates) // 2 # split_point_index の定義を追加\n", "    split_date = pd.to_datetime(sorted_unique_dates[split_point_index])\n", "    print(f\"警告: 自動決定されたsplit_dateでは学習データがありません。\"\n", "          f\"データの中間点 ({split_date.strftime('%Y-%m-%d')}) で分割します。\")\n", "elif comprehensive_df['date'].nunique() <= 1:\n", "    # 日付が1種類しかない場合は学習とテストに分割できない\n", "    print(\"警告: データに含まれる日付が1種類のみのため、学習データとテストデータに分割できません。全データを学習に使用します。\")\n", "    split_date = max_data_date + timed<PERSON>ta(days=1) # 全データが学習データになるように\n", "\n", "print(f\"自動決定された分割日 (この日より前のデータを学習、以降をテスト): {split_date.strftime('%Y-%m-%d')}\")\n", "\n", "# データを分割\n", "train_df = comprehensive_df[comprehensive_df['date'] < split_date].copy()\n", "test_df = comprehensive_df[comprehensive_df['date'] >= split_date].copy()\n", "\n", "# X_test のダミー定義 (実際のモデル学習・予測部分で適切に定義される想定)\n", "# --- 3. 特徴量、ラベル、グループ情報の準備 ---\n", "X_train, y_train, group_train = pd.DataFrame(), pd.Series(dtype='float64'), []\n", "X_test, y_test, group_test = pd.DataFrame(), pd.Series(dtype='float64'), []\n", "categorical_features = [] # カテゴリカル特徴量のリストを初期化\n", "\n", "# y_label_column_name と feature_columns がこのスコープで定義されていることを確認\n", "# y_label_column_name は 'relevance_score'\n", "# feature_columns は既に定義済み\n", "\n", "if not train_df.empty:\n", "    # object型とStringDtypeをcategory型に変換 (LightGBMが推奨する型)\n", "    # categorical_features リストもここで作成\n", "    categorical_features = [col for col in feature_columns if train_df[col].dtype.name == 'object' or isinstance(train_df[col].dtype, pd.StringDtype)]\n", "    for col in categorical_features:\n", "        train_df[col] = train_df[col].astype('category')\n", "        if not test_df.empty and col in test_df.columns: # test_dfにも適用\n", "             test_df[col] = test_df[col].astype('category')\n", "\n", "    X_train = train_df[feature_columns]\n", "    y_train = train_df[y_label_column_name]\n", "    group_train = train_df.groupby('race_id').size().tolist()\n", "    print(f\"学習データ: X_train {X_train.shape}, y_train {y_train.shape}, group_train {len(group_train)} groups\")\n", "    print(f\"学習データのカテゴリカル特徴量: {[col for col in X_train.columns if X_train[col].dtype.name == 'category']}\")\n", "\n", "if not test_df.empty:\n", "    X_test = test_df[feature_columns]\n", "    y_test = test_df[y_label_column_name]\n", "    group_test = test_df.groupby('race_id').size().tolist()\n", "    print(f\"テストデータ: X_test {X_test.shape}, y_test {y_test.shape}, group_test {len(group_test)} groups\")\n", "    print(f\"テストデータのカテゴリカル特徴量: {[col for col in X_test.columns if X_test[col].dtype.name == 'category']}\")\n", "\n", "if train_df.empty:\n", "    print(f\"警告: 学習データが空です。split_date ({split_date.strftime('%Y-%m-%d')}) を確認してください。\")\n", "    # train_dfが空の場合、test_data_with_predictions もこの時点では未定義のはずなので、以下の行はコメントアウトまたは削除\n", "    # test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')\n", "\n", "\n", "# 以下の部分は予測結果の表示ロジックの一部であり、エラー箇所とは直接関係ありませんが、\n", "# test_df が空でないこと、およびモデルが学習済みであることを確認する文脈で使われるべきです。\n", "# このdiffでは、エラーの直接的な原因である split_date の未定義問題に焦点を当てています。\n", "# test_data_with_predictions の定義や X_test の存在確認は、モデル学習・予測のフェーズで行われます。\n", "if not test_df.empty: # test_dfが空でない場合のみ予測関連の処理を行う想定\n", "    test_data_with_predictions = test_df.copy() # 予測スコアなどを追加する前のコピー\n", "    # 実際の予測スコアを代入する必要があります。ここではダミーとしてランダムな値を入れます。\n", "    # この部分はモデルの予測結果で置き換えてください。\n", "    if not test_data_with_predictions.empty:\n", "        test_data_with_predictions['predicted_score'] = np.random.rand(len(test_data_with_predictions))\n", "    # test_data_with_predictions の定義と predicted_score の代入がここより前にある想定\n", "    # このブロックはモデル学習・予測後なので、test_data_with_predictions は定義済みのはず\n", "    # ただし、このコードスニペットの文脈では未定義の可能性があるため、ダミーの定義を追加するか、\n", "    # 実際のモデル学習・予測コードがこの前にあることを前提とする。\n", "    # 前回の修正で test_data_with_predictions の初期化とダミーの predicted_score を追加済み。\n", "    # ここでは、その前提で進める。\n", "    if 'test_data_with_predictions' in locals() and 'predicted_score' in test_data_with_predictions.columns:\n", "        test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')\n", "        print(\"\\n予測結果（テストデータ）:\")\n", "    else:\n", "        print(\"\\n予測に必要な test_data_with_predictions または predicted_score が未定義です。\")\n", "        print(test_data_with_predictions[['race_id', 'horse_id', 'predicted_score', 'predicted_rank']].head())\n", "elif not X_test.empty: # X_test が空でない（つまりテストデータの特徴量がある）が、モデルが学習されていない場合など\n", "    print(\"\\nモデルが学習されていない、またはテストデータの特徴量が空のため、テストデータの予測はスキップされました。\")\n", "else:\n", "    print(\"\\nテストデータがないため、予測はスキップされました。\")\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[I 2025-05-29 21:51:45,214] A new study created in memory with name: no-name-f96891ee-2e12-4fbe-97e9-3e7bc9edcb1c\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optunaによるハイパーパラメータ最適化を開始します...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-05-29 21:51:45,911] Trial 0 finished with value: 0.5838003583516818 and parameters: {'n_estimators': 400, 'learning_rate': 0.0862735828664018, 'num_leaves': 114, 'max_depth': 7, 'min_child_samples': 12, 'subsample': 0.6, 'colsample_bytree': 0.6, 'reg_alpha': 2.9154431891537547, 'reg_lambda': 0.2537815508265665}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:46,589] Trial 1 finished with value: 0.5735120995137172 and parameters: {'n_estimators': 800, 'learning_rate': 0.005318033256270142, 'num_leaves': 146, 'max_depth': 9, 'min_child_samples': 14, 'subsample': 0.6, 'colsample_bytree': 0.6, 'reg_alpha': 0.016480446427978974, 'reg_lambda': 0.12561043700013558}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:46,934] Trial 2 finished with value: 0.5714734739087117 and parameters: {'n_estimators': 500, 'learning_rate': 0.011963764382790322, 'num_leaves': 98, 'max_depth': 4, 'min_child_samples': 18, 'subsample': 0.7, 'colsample_bytree': 0.8, 'reg_alpha': 1.382623217936987, 'reg_lambda': 0.006290644294586149}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:47,445] Trial 3 finished with value: 0.5767911082082758 and parameters: {'n_estimators': 600, 'learning_rate': 0.029493012052163467, 'num_leaves': 21, 'max_depth': 7, 'min_child_samples': 12, 'subsample': 0.6, 'colsample_bytree': 1.0, 'reg_alpha': 7.2866537374910445, 'reg_lambda': 1.7123375973163988}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:47,918] Trial 4 finished with value: 0.5696922033476164 and parameters: {'n_estimators': 400, 'learning_rate': 0.006699530280754897, 'num_leaves': 108, 'max_depth': 6, 'min_child_samples': 10, 'subsample': 0.8, 'colsample_bytree': 0.6, 'reg_alpha': 4.337920697490942, 'reg_lambda': 0.010842262717330166}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:48,363] Trial 5 finished with value: 0.5743489455066901 and parameters: {'n_estimators': 700, 'learning_rate': 0.012720830454691846, 'num_leaves': 85, 'max_depth': 7, 'min_child_samples': 13, 'subsample': 1.0, 'colsample_bytree': 0.9, 'reg_alpha': 5.727904470799623, 'reg_lambda': 3.7958531426706403}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:48,665] Trial 6 finished with value: 0.5742304432516643 and parameters: {'n_estimators': 600, 'learning_rate': 0.07913270952323785, 'num_leaves': 27, 'max_depth': 4, 'min_child_samples': 7, 'subsample': 0.7, 'colsample_bytree': 0.7, 'reg_alpha': 0.01217295809836997, 'reg_lambda': 2.0651425578959257}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:48,890] Trial 7 finished with value: 0.5677098124753992 and parameters: {'n_estimators': 400, 'learning_rate': 0.011600433752378412, 'num_leaves': 88, 'max_depth': 4, 'min_child_samples': 41, 'subsample': 0.6, 'colsample_bytree': 1.0, 'reg_alpha': 1.2273800987852967, 'reg_lambda': 0.0062353771356731605}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:49,499] Trial 8 finished with value: 0.5732620365350344 and parameters: {'n_estimators': 100, 'learning_rate': 0.057532041236250865, 'num_leaves': 111, 'max_depth': 8, 'min_child_samples': 40, 'subsample': 0.6, 'colsample_bytree': 0.7, 'reg_alpha': 0.0029072088906598446, 'reg_lambda': 2.8340904295147746}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:49,729] Trial 9 finished with value: 0.5711750651739945 and parameters: {'n_estimators': 700, 'learning_rate': 0.013473432786208842, 'num_leaves': 23, 'max_depth': 5, 'min_child_samples': 19, 'subsample': 0.9, 'colsample_bytree': 0.9, 'reg_alpha': 3.53875886477924, 'reg_lambda': 0.0774211647399625}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:50,338] Trial 10 finished with value: 0.5836292898291432 and parameters: {'n_estimators': 1000, 'learning_rate': 0.03883941904924757, 'num_leaves': 56, 'max_depth': 10, 'min_child_samples': 28, 'subsample': 0.8, 'colsample_bytree': 0.7, 'reg_alpha': 0.3115344470285826, 'reg_lambda': 0.19151000143366553}. Best is trial 0 with value: 0.5838003583516818.\n", "[I 2025-05-29 21:51:51,011] Trial 11 finished with value: 0.589124060695561 and parameters: {'n_estimators': 1000, 'learning_rate': 0.04092194611489157, 'num_leaves': 53, 'max_depth': 10, 'min_child_samples': 29, 'subsample': 0.8, 'colsample_bytree': 0.7, 'reg_alpha': 0.19641014244241747, 'reg_lambda': 0.23266686800485914}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:51,524] Trial 12 finished with value: 0.5885356107028249 and parameters: {'n_estimators': 200, 'learning_rate': 0.09104780740275108, 'num_leaves': 58, 'max_depth': 10, 'min_child_samples': 27, 'subsample': 0.9, 'colsample_bytree': 0.6, 'reg_alpha': 0.13386691672349949, 'reg_lambda': 0.4211779188761303}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:51,957] Trial 13 finished with value: 0.584289607067467 and parameters: {'n_estimators': 100, 'learning_rate': 0.04828835753810256, 'num_leaves': 58, 'max_depth': 10, 'min_child_samples': 30, 'subsample': 0.9, 'colsample_bytree': 0.7, 'reg_alpha': 0.13363938372894757, 'reg_lambda': 0.6097588209811619}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:52,632] Trial 14 finished with value: 0.5883197922507776 and parameters: {'n_estimators': 1000, 'learning_rate': 0.022857078704543867, 'num_leaves': 62, 'max_depth': 9, 'min_child_samples': 34, 'subsample': 0.9, 'colsample_bytree': 0.8, 'reg_alpha': 0.03980364072244551, 'reg_lambda': 0.03276424659224867}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:53,041] Trial 15 finished with value: 0.5834472743859143 and parameters: {'n_estimators': 200, 'learning_rate': 0.09855084035302168, 'num_leaves': 43, 'max_depth': 9, 'min_child_samples': 22, 'subsample': 1.0, 'colsample_bytree': 0.6, 'reg_alpha': 0.2735519562189285, 'reg_lambda': 0.5668051618087335}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:53,453] Trial 16 finished with value: 0.5831158523872347 and parameters: {'n_estimators': 900, 'learning_rate': 0.059696113706008525, 'num_leaves': 70, 'max_depth': 10, 'min_child_samples': 35, 'subsample': 0.8, 'colsample_bytree': 0.7, 'reg_alpha': 0.06595808519267578, 'reg_lambda': 0.001129059573387675}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:53,908] Trial 17 finished with value: 0.5805526063017032 and parameters: {'n_estimators': 300, 'learning_rate': 0.03400332274727427, 'num_leaves': 41, 'max_depth': 8, 'min_child_samples': 24, 'subsample': 0.9, 'colsample_bytree': 0.8, 'reg_alpha': 0.5472387296689019, 'reg_lambda': 9.164825797898322}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:54,558] Trial 18 finished with value: 0.5831666627761183 and parameters: {'n_estimators': 800, 'learning_rate': 0.020046978124375746, 'num_leaves': 42, 'max_depth': 8, 'min_child_samples': 44, 'subsample': 0.7, 'colsample_bytree': 0.6, 'reg_alpha': 0.002294543057875093, 'reg_lambda': 0.039552923862035586}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:55,014] Trial 19 finished with value: 0.5826345875645805 and parameters: {'n_estimators': 300, 'learning_rate': 0.062484578820713337, 'num_leaves': 76, 'max_depth': 3, 'min_child_samples': 33, 'subsample': 0.8, 'colsample_bytree': 0.8, 'reg_alpha': 0.013089633771852812, 'reg_lambda': 0.5531302324504381}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:55,544] Trial 20 finished with value: 0.5799865325426131 and parameters: {'n_estimators': 200, 'learning_rate': 0.04590059083055748, 'num_leaves': 131, 'max_depth': 9, 'min_child_samples': 50, 'subsample': 1.0, 'colsample_bytree': 0.7, 'reg_alpha': 0.11991711105978577, 'reg_lambda': 1.0834251055376005}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:56,373] Trial 21 finished with value: 0.5873727020267473 and parameters: {'n_estimators': 1000, 'learning_rate': 0.022948583061240656, 'num_leaves': 56, 'max_depth': 10, 'min_child_samples': 35, 'subsample': 0.9, 'colsample_bytree': 0.8, 'reg_alpha': 0.038709523366490875, 'reg_lambda': 0.041545587370386425}. Best is trial 11 with value: 0.589124060695561.\n", "[I 2025-05-29 21:51:57,052] Trial 22 finished with value: 0.5906827722755413 and parameters: {'n_estimators': 900, 'learning_rate': 0.0204774064845345, 'num_leaves': 68, 'max_depth': 9, 'min_child_samples': 25, 'subsample': 0.9, 'colsample_bytree': 0.9, 'reg_alpha': 0.04557128234885994, 'reg_lambda': 0.019433174506300534}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:51:57,574] Trial 23 finished with value: 0.58182369733104 and parameters: {'n_estimators': 900, 'learning_rate': 0.019029390835061763, 'num_leaves': 68, 'max_depth': 10, 'min_child_samples': 25, 'subsample': 0.9, 'colsample_bytree': 0.9, 'reg_alpha': 0.22989940083339724, 'reg_lambda': 0.016008342415291248}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:51:58,119] Trial 24 finished with value: 0.5785144107412961 and parameters: {'n_estimators': 900, 'learning_rate': 0.026150616198161794, 'num_leaves': 48, 'max_depth': 9, 'min_child_samples': 30, 'subsample': 0.8, 'colsample_bytree': 0.9, 'reg_alpha': 0.5738894024284005, 'reg_lambda': 0.27802803750946603}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:51:58,966] Trial 25 finished with value: 0.5715680440562065 and parameters: {'n_estimators': 800, 'learning_rate': 0.017126320255226618, 'num_leaves': 31, 'max_depth': 8, 'min_child_samples': 20, 'subsample': 1.0, 'colsample_bytree': 1.0, 'reg_alpha': 0.00499681545862005, 'reg_lambda': 0.00145466932282724}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:51:59,579] Trial 26 finished with value: 0.5772314693758565 and parameters: {'n_estimators': 700, 'learning_rate': 0.008958626797788305, 'num_leaves': 76, 'max_depth': 10, 'min_child_samples': 26, 'subsample': 0.9, 'colsample_bytree': 0.6, 'reg_alpha': 0.05391582437926523, 'reg_lambda': 0.08128970211157377}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:52:00,156] Trial 27 finished with value: 0.5886168326760423 and parameters: {'n_estimators': 900, 'learning_rate': 0.03490658441601191, 'num_leaves': 93, 'max_depth': 9, 'min_child_samples': 29, 'subsample': 0.8, 'colsample_bytree': 0.9, 'reg_alpha': 0.09349657526839845, 'reg_lambda': 0.0034732124654332495}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:52:00,680] Trial 28 finished with value: 0.5802550150045138 and parameters: {'n_estimators': 900, 'learning_rate': 0.03422590778797496, 'num_leaves': 98, 'max_depth': 6, 'min_child_samples': 17, 'subsample': 0.7, 'colsample_bytree': 0.9, 'reg_alpha': 0.020611646057583945, 'reg_lambda': 0.0021488362306202984}. Best is trial 22 with value: 0.5906827722755413.\n", "[I 2025-05-29 21:52:01,348] Trial 29 finished with value: 0.5778738166559012 and parameters: {'n_estimators': 1000, 'learning_rate': 0.01600361815616753, 'num_leaves': 125, 'max_depth': 7, 'min_child_samples': 39, 'subsample': 0.8, 'colsample_bytree': 0.9, 'reg_alpha': 0.0011000027119649842, 'reg_lambda': 0.0025996262052871114}. Best is trial 22 with value: 0.5906827722755413.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Optunaによるハイパーパラメータ最適化が完了しました。\n", "最適な試行:\n", "  Value (NDCG@3): 0.5906827722755413\n", "  Params: \n", "    n_estimators: 900\n", "    learning_rate: 0.0204774064845345\n", "    num_leaves: 68\n", "    max_depth: 9\n", "    min_child_samples: 25\n", "    subsample: 0.9\n", "    colsample_bytree: 0.9\n", "    reg_alpha: 0.04557128234885994\n", "    reg_lambda: 0.019433174506300534\n", "\n", "最終モデルを学習します...\n", "Optunaで見つかった最適なパラメータを使用します。\n", "Training until validation scores don't improve for 10 rounds\n", "Early stopping, best iteration is:\n", "[13]\tvalid_0's ndcg@1: 0.501183\tvalid_0's ndcg@2: 0.548503\tvalid_0's ndcg@3: 0.589091\tvalid_0's ndcg@4: 0.615488\tvalid_0's ndcg@5: 0.648423\n", "最終モデルの学習が完了しました。\n", "\n", "最終モデルでテストデータの予測を行います...\n", "\n", "予測結果（テストデータ）:\n", "            race_id    horse_id  predicted_score  predicted_rank  \\\n", "43104  202206050101  2020100258         0.136653             1.0   \n", "43105  202206050101  2020103079         0.093037             2.0   \n", "43106  202206050101  2020100656        -0.106566             6.0   \n", "43107  202206050101  2020100206        -0.348712            11.0   \n", "43108  202206050101  2020105525        -0.246416             8.0   \n", "\n", "       relevance_score  着順  \n", "43104               16   1  \n", "43105               15   2  \n", "43106               14   3  \n", "43107               13   4  \n", "43108               12   5  \n", "\n", "テストデータでの評価 (注意: これは簡易的な表示であり、NDCG等の指標計算は別途必要です)\n", "\n", "モデル評価 (検証データ):\n", "  ndcg@1: 0.5012\n", "  ndcg@2: 0.5485\n", "  ndcg@3: 0.5891\n", "  ndcg@4: 0.6155\n", "  ndcg@5: 0.6484\n", "\n", "特徴量の重要度:\n", "        feature_name  importance_score\n", "13        trainer_id               148\n", "9                 上り               122\n", "12         jockey_id                78\n", "23  mother_father_id                72\n", "10                人気                62\n"]}], "source": ["import lightgbm as lgb\n", "import optuna\n", "import pandas as pd\n", "import functools # functools.partial を使用するためにインポート\n", "\n", "# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。\n", "\n", "# --- Optunaによるハイパーパラメータ最適化 ---\n", "def objective_for_lgbm_ranker(trial, x_train_data, y_train_data, group_train_data,\n", "                              x_test_data, y_test_data, group_test_data,\n", "                              cat_features, eval_at_k):\n", "    \"\"\"Optunaの目的関数。LightGBM LGBMRankerのハイパーパラメータを最適化します。\"\"\"\n", "    params = {\n", "        'objective': 'lambdarank',\n", "        'metric': 'ndcg',\n", "        'boosting_type': 'gbdt',\n", "        'n_estimators': trial.suggest_int('n_estimators', 100, 1000, step=100),\n", "        'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.1, log=True),\n", "        'num_leaves': trial.suggest_int('num_leaves', 15, 150),\n", "        'max_depth': trial.suggest_int('max_depth', 3, 10),\n", "        'min_child_samples': trial.suggest_int('min_child_samples', 5, 50),\n", "        'subsample': trial.suggest_float('subsample', 0.6, 1.0, step=0.1),\n", "        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0, step=0.1),\n", "        'reg_alpha': trial.suggest_float('reg_alpha', 1e-3, 10.0, log=True),\n", "        'reg_lambda': trial.suggest_float('reg_lambda', 1e-3, 10.0, log=True),\n", "        'random_state': 42,\n", "        'n_jobs': -1,\n", "        'verbose': -1,\n", "    }\n", "\n", "    model = lgb.LGBMRanker(**params)\n", "    early_stopping_callback = lgb.early_stopping(\n", "        stopping_rounds=20,\n", "        verbose=False\n", "    )\n", "\n", "    # テストデータが存在し、かつ空でない場合にのみ評価セットとして使用\n", "    eval_set = []\n", "    eval_group = []\n", "    if x_test_data is not None and not x_test_data.empty and \\\n", "       y_test_data is not None and not y_test_data.empty and \\\n", "       group_test_data is not None and len(group_test_data) > 0:\n", "        eval_set = [(x_test_data, y_test_data)]\n", "        eval_group = [group_test_data]\n", "    else:\n", "        print(\"警告: テストデータが不完全なため、Optunaでの検証は学習データで行います（過学習のリスクあり）。\")\n", "        # 代替として学習データの一部を検証に使うか、CVを実装することも検討できます。\n", "        # ここでは簡略化のため、検証セットなし（または学習データ自身を検証に使う）とします。\n", "        # eval_set = [(x_train_data, y_train_data)] # 学習データで評価する場合\n", "        # eval_group = [group_train_data]\n", "        # より安全なのは、テストデータがない場合は高いスコアを返さないことです。\n", "        return -float('inf')\n", "\n", "\n", "    model.fit(\n", "        x_train_data,\n", "        y_train_data,\n", "        group=group_train_data,\n", "        eval_set=eval_set,\n", "        eval_group=eval_group,\n", "        eval_at=[eval_at_k], # 単一のkで評価\n", "        categorical_feature=cat_features,\n", "        callbacks=[early_stopping_callback]\n", "    )\n", "\n", "    if model.evals_result_ and 'valid_0' in model.evals_result_ and f'ndcg@{eval_at_k}' in model.evals_result_['valid_0']:\n", "        score = model.evals_result_['valid_0'][f'ndcg@{eval_at_k}'][-1]\n", "    else:\n", "        print(f\"警告: 評価スコア (ndcg@{eval_at_k}) を取得できませんでした。\")\n", "        score = -float('inf')\n", "\n", "    return score\n", "\n", "# --- データ準備とOptuna実行制御 ---\n", "# グローバルスコープの変数が存在するかどうかを確認\n", "data_is_ready = (\n", "    'X_train' in globals() and not X_train.empty and\n", "    'y_train' in globals() and not y_train.empty and\n", "    'group_train' in globals() and len(group_train) > 0 and\n", "    'categorical_features' in globals() and\n", "    'feature_columns' in globals()\n", ")\n", "# テストデータは任意とするが、存在すればOptunaや最終評価で使用\n", "test_data_is_ready = (\n", "    'X_test' in globals() and not X_test.empty and\n", "    'y_test' in globals() and not y_test.empty and\n", "    'group_test' in globals() and len(group_test) > 0\n", ")\n", "\n", "USE_OPTUNA = True  # Optunaによる最適化を実行するかどうか\n", "optimized_lgbm_ranker = None\n", "best_params_from_optuna = {}\n", "\n", "if USE_OPTUNA and data_is_ready:\n", "    print(\"Optunaによるハイパーパラメータ最適化を開始します...\")\n", "    # functools.partial を使って目的関数に必要なデータを渡す\n", "    # 評価するkの値を指定 (例: NDCG@3)\n", "    target_eval_k_for_optuna = 3\n", "    objective_with_data = functools.partial(\n", "        objective_for_lgbm_ranker,\n", "        x_train_data=X_train, y_train_data=y_train, group_train_data=group_train,\n", "        x_test_data=X_test if test_data_is_ready else None, # テストデータがない場合はNoneを渡す\n", "        y_test_data=y_test if test_data_is_ready else None,\n", "        group_test_data=group_test if test_data_is_ready else None,\n", "        cat_features=categorical_features,\n", "        eval_at_k=target_eval_k_for_optuna\n", "    )\n", "\n", "    study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=42))\n", "    study.optimize(objective_with_data, n_trials=30, timeout=1800)\n", "\n", "    print(\"\\nOptunaによるハイパーパラメータ最適化が完了しました。\")\n", "    print(\"最適な試行:\")\n", "    best_trial_result = study.best_trial\n", "    print(f\"  Value (NDCG@{target_eval_k_for_optuna}): {best_trial_result.value}\")\n", "    print(\"  Params: \")\n", "    for key, value in best_trial_result.params.items():\n", "        print(f\"    {key}: {value}\")\n", "    best_params_from_optuna = best_trial_result.params\n", "\n", "# --- 最終モデルの学習 ---\n", "if data_is_ready:\n", "    print(\"\\n最終モデルを学習します...\")\n", "    if USE_OPTUNA and best_params_from_optuna:\n", "        print(\"Optunaで見つかった最適なパラメータを使用します。\")\n", "        final_model_params = {\n", "            'objective': 'lambdarank',\n", "            'metric': 'ndcg',\n", "            'boosting_type': 'gbdt',\n", "            'random_state': 42,\n", "            'n_jobs': -1,\n", "            'verbose': -1,\n", "            **best_params_from_optuna\n", "        }\n", "    else:\n", "        print(\"Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。\")\n", "        final_model_params = {\n", "            'objective': 'lambdarank',\n", "            'metric': 'ndcg',\n", "            'n_estimators': 100,\n", "            'learning_rate': 0.05,\n", "            'num_leaves': 31,\n", "            'random_state': 42,\n", "            'n_jobs': -1,\n", "            'verbose': -1,\n", "        }\n", "\n", "    optimized_lgbm_ranker = lgb.LGBMRanker(**final_model_params)\n", "\n", "    eval_set_for_final_model = []\n", "    eval_group_for_final_model = []\n", "    callbacks_for_final_model = []\n", "\n", "    if test_data_is_ready:\n", "        eval_set_for_final_model = [(X_test, y_test)]\n", "        eval_group_for_final_model = [group_test]\n", "        # 最終モデル学習時にも早期停止を適用\n", "        callbacks_for_final_model.append(lgb.early_stopping(10, verbose=True))\n", "\n", "    optimized_lgbm_ranker.fit(\n", "        X_train,\n", "        y_train,\n", "        group=group_train,\n", "        eval_set=eval_set_for_final_model if eval_set_for_final_model else None,\n", "        eval_group=eval_group_for_final_model if eval_group_for_final_model else None,\n", "        categorical_feature=categorical_features,\n", "        callbacks=callbacks_for_final_model if callbacks_for_final_model else None\n", "    )\n", "    print(\"最終モデルの学習が完了しました。\")\n", "else:\n", "    print(\"学習データが不十分なため、モデル学習は実行されませんでした。\")\n", "\n", "# --- 学習済み最終モデルでテストデータの予測 ---\n", "if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, \"fitted_\") and optimized_lgbm_ranker.fitted_ and test_data_is_ready:\n", "    print(\"\\n最終モデルでテストデータの予測を行います...\")\n", "\n", "    # test_data_with_predictions の準備\n", "    if 'test_df' in globals() and not test_df.empty:\n", "        test_data_with_predictions = test_df.copy()\n", "    else:\n", "        print(\"警告: test_df が存在しないか空のため、予測結果を格納するDataFrameを作成できません。\")\n", "        test_data_with_predictions = pd.DataFrame()\n", "\n", "    if not test_data_with_predictions.empty:\n", "        predicted_scores_on_test = optimized_lgbm_ranker.predict(X_test)\n", "        test_data_with_predictions['predicted_score'] = predicted_scores_on_test\n", "        test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')\n", "\n", "        print(\"\\n予測結果（テストデータ）:\")\n", "        # y_label_column_name と actual_rank_col が存在するか確認\n", "        display_cols = ['race_id', 'horse_id', 'predicted_score', 'predicted_rank']\n", "        if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:\n", "            display_cols.append(y_label_column_name)\n", "        if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:\n", "            display_cols.append(actual_rank_col)\n", "        \n", "        # 実際に存在するカラムのみを選択して表示\n", "        valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]\n", "        print(test_data_with_predictions[valid_display_cols].head())\n", "\n", "        # (オプション) 最終モデルの評価\n", "        # ここでは予測スコアの表示に留めます。NDCG等の詳細な評価は別途関数を用意することを推奨します。\n", "        print(\"\\nテストデータでの評価 (注意: これは簡易的な表示であり、NDCG等の指標計算は別途必要です)\")\n", "\n", "elif not test_data_is_ready:\n", "    print(\"\\nテストデータがないため、予測はスキップされました。\")\n", "else: # モデルが学習されなかった場合\n", "    print(\"\\nモデルが学習されていないため、テストデータの予測はスキップされました。\")\n", "\n", "\n", "# --- 5. 評価 ---\n", "if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'best_score_') and optimized_lgbm_ranker.best_score_:\n", "    print(\"\\nモデル評価 (検証データ):\")\n", "    if 'valid_0' in optimized_lgbm_ranker.best_score_:\n", "        for eval_metric_name, value in optimized_lgbm_ranker.best_score_['valid_0'].items():\n", "            print(f\"  {eval_metric_name}: {value:.4f}\")\n", "    else:\n", "        print(\"  検証データでのスコアは記録されていません。fit時にeval_setが指定されなかったか、早期停止が作動しなかった可能性があります。\")\n", "elif optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'fitted_') and optimized_lgbm_ranker.fitted_:\n", "     print(\"\\nモデルは学習されましたが、検証スコア (best_score_) は利用できません。\")\n", "\n", "# 特徴量の重要度\n", "if optimized_lgbm_ranker and hasattr(optimized_lgbm_ranker, 'feature_importances_') and hasattr(optimized_lgbm_ranker, 'fitted_') and optimized_lgbm_ranker.fitted_:\n", "    if 'feature_columns' in globals() and len(feature_columns) == len(optimized_lgbm_ranker.feature_importances_):\n", "        feature_importance_df = pd.DataFrame({\n", "            'feature_name': feature_columns,\n", "            'importance_score': optimized_lgbm_ranker.feature_importances_\n", "        }).sort_values('importance_score', ascending=False)\n", "        print(\"\\n特徴量の重要度:\")\n", "        print(feature_importance_df.head())\n", "    else:\n", "        print(\"\\n特徴量の重要度を表示できませんでした。feature_columnsの定義またはモデルの学習状態を確認してください。\")\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "feature_name", "rawType": "object", "type": "string"}, {"name": "importance_score", "rawType": "int32", "type": "integer"}], "ref": "7e1ecb54-2c31-43cd-b95b-4dfe817529dc", "rows": [["19", "賞金(万円)", "8"], ["87", "num_starters_in_race", "8"], ["48", "sibling_ids", "3"], ["11", "単勝", "2"], ["47", "mother_father_id", "2"], ["62", "賞金_last_5R_mean", "2"], ["71", "賞金_last_10R_mean", "2"], ["10", "上り", "1"], ["63", "斤量_last_5R_mean", "1"], ["85", "体重変化_all_R_mean", "1"], ["7", "着差", "0"], ["9", "通過", "0"], ["12", "人気", "0"], ["13", "馬体重", "0"], ["14", "調教ﾀｲﾑ", "0"], ["8", "ﾀｲﾑ指数", "0"], ["15", "厩舎ｺﾒﾝﾄ", "0"], ["6", "タイム", "0"], ["5", "騎手", "0"], ["0", "枠番", "0"], ["4", "斤量", "0"], ["1", "馬番", "0"], ["2", "馬名", "0"], ["3", "性齢", "0"], ["23", "レース名", "0"], ["22", "開催", "0"], ["21", "trainer_id", "0"], ["20", "jockey_id", "0"], ["18", "馬主", "0"], ["17", "調教師", "0"], ["16", "備考", "0"], ["24", "course_len", "0"], ["32", "産地", "0"], ["33", "セリ取引価格", "0"], ["34", "獲得賞金", "0"], ["25", "weather", "0"], ["28", "around", "0"], ["29", "調教師_horse_info", "0"], ["26", "race_type", "0"], ["27", "ground_state", "0"], ["39", "trainer_id_horse_info", "0"], ["38", "horse_name", "0"], ["42", "father_name", "0"], ["40", "owner_id", "0"], ["43", "father_id", "0"], ["44", "mother_name", "0"], ["45", "mother_id", "0"], ["41", "breeder_id", "0"], ["46", "mother_father_name", "0"], ["49", "募集情報", "0"]], "shape": {"columns": 2, "rows": 88}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature_name</th>\n", "      <th>importance_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>賞金(万円)</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>num_starters_in_race</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>sibling_ids</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>単勝</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>mother_father_id</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>斤量_all_R_mean</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>上り_all_R_mean</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>タイム_all_R_mean</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>体重_all_R_mean</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>interval_days</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>88 rows × 2 columns</p>\n", "</div>"], "text/plain": ["            feature_name  importance_score\n", "19                賞金(万円)                 8\n", "87  num_starters_in_race                 8\n", "48           sibling_ids                 3\n", "11                    単勝                 2\n", "47      mother_father_id                 2\n", "..                   ...               ...\n", "81         斤量_all_R_mean                 0\n", "83         上り_all_R_mean                 0\n", "82        タイム_all_R_mean                 0\n", "84         体重_all_R_mean                 0\n", "86         interval_days                 0\n", "\n", "[88 rows x 2 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["feature_importance_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 2}