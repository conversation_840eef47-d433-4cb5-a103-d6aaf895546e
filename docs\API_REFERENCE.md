# API リファレンス

競馬AI予測システム統合版のAPIリファレンスです。

## 目次

1. [コアモジュール](#コアモジュール)
2. [予測システム](#予測システム)
3. [使用例](#使用例)

## コアモジュール

### データ処理 (core.processors)

#### RaceProcessor

レースデータの処理を行うクラス。

```python
from keiba_ai_system.core.processors import RaceProcessor

processor = RaceProcessor()
```

**主要メソッド:**

- `parse_race_html(html_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]`
  - HTMLファイルからレース情報と結果を抽出
  - 戻り値: (レース情報DataFrame, レース結果DataFrame)

- `process_race_bin_files(year: str, parallel: bool = True) -> Tuple[pd.DataFrame, pd.DataFrame]`
  - 指定年度のレースファイルを一括処理
  - 戻り値: (レース情報DataFrame, レース結果DataFrame)

- `extract_horse_ids_from_html(html_path: str) -> List[str]`
  - HTMLファイルから馬IDを抽出
  - 戻り値: 馬IDのリスト

#### HorseProcessor

馬データの処理を行うクラス。

```python
from keiba_ai_system.core.processors import HorseProcessor

processor = HorseProcessor()
```

**主要メソッド:**

- `get_rawdata_horse_info(horse_id_list: List[str], parallel: bool = True) -> pd.DataFrame`
  - 馬の基本情報を取得
  - 戻り値: 馬基本情報DataFrame

- `get_rawdata_horse_results(horse_id_list: List[str], max_files: int = None) -> pd.DataFrame`
  - 馬の過去成績を取得
  - 戻り値: 馬過去成績DataFrame

### スクレイピング (core.scrapers)

#### scrape_html_race

レースHTMLをスクレイピングする関数。

```python
from keiba_ai_system.core.scrapers import scrape_html_race

scrape_html_race(race_id_list=['202406010101'], skip=True)
```

**パラメータ:**
- `race_id_list: List[str]` - レースIDのリスト
- `skip: bool = True` - 既存ファイルをスキップするか

#### scrape_html_horse

馬HTMLをスクレイピングする関数。

```python
from keiba_ai_system.core.scrapers import scrape_html_horse

scrape_html_horse(horse_id_list=['2020104326'], skip=True)
```

**パラメータ:**
- `horse_id_list: List[str]` - 馬IDのリスト
- `skip: bool = True` - 既存ファイルをスキップするか

### 特徴量エンジニアリング (core.features)

#### FeatureEngineeringManager

特徴量の管理と計算を行うクラス。

```python
from keiba_ai_system.core.features import FeatureEngineeringManager

manager = FeatureEngineeringManager("config.yaml")
```

**主要メソッド:**

- `calculate_features(data: pd.DataFrame, dependencies: Dict = None) -> pd.DataFrame`
  - 特徴量を計算
  - 戻り値: 特徴量が追加されたDataFrame

- `add_custom_feature(feature_definition: FeatureDefinition)`
  - カスタム特徴量を追加

- `list_features(enabled_only: bool = False) -> List[str]`
  - 特徴量名のリストを取得

#### FeatureDefinition

特徴量の定義を表すクラス。

```python
from keiba_ai_system.core.features import FeatureDefinition, FeatureCategory, FeatureType

feature = FeatureDefinition(
    name='カスタム特徴量',
    category=FeatureCategory.CUSTOM,
    feature_type=FeatureType.NUMERICAL,
    description='説明',
    required_columns=['馬番', '人気'],
    calculator=my_calculator_function
)
```

## 予測システム

### LiveRacePredictor

リアルタイム予測を行うクラス。

```python
from keiba_ai_system.prediction import LiveRacePredictor

predictor = LiveRacePredictor(
    model_path="model.pkl",
    scaler_path="scaler.pkl"
)
```

**主要メソッド:**

- `predict_race_complete(race_id: str, skip_existing: bool = True) -> pd.DataFrame`
  - レース予測の完全な処理フロー
  - 戻り値: 予測結果DataFrame

- `scrape_race_data(race_id: str) -> bool`
  - レースデータをスクレイピング

- `process_race_data(race_id: str) -> Tuple[pd.DataFrame, pd.DataFrame, str]`
  - レースデータを処理
  - 戻り値: (レース情報, レース結果, レース名)

## 使用例

### 基本的な使用方法

```python
# 1. 予測システムの初期化
from keiba_ai_system.prediction import LiveRacePredictor

predictor = LiveRacePredictor()

# 2. レース予測の実行
results = predictor.predict_race_complete("202406010101")

# 3. 結果の表示
print(results[['馬名', '予測順位', '勝率予測']].head())
```

### 特徴量エンジニアリング

```python
# 1. 特徴量管理システムの初期化
from keiba_ai_system.core.features import FeatureEngineeringManager

manager = FeatureEngineeringManager()

# 2. 特徴量の計算
enhanced_data = manager.calculate_features(
    data=race_data,
    dependencies={'horse_results_df': horse_results}
)

# 3. カスタム特徴量の追加
def custom_calculator(data, **kwargs):
    return data['馬番'] * data['人気']

custom_feature = FeatureDefinition(
    name='馬番人気積',
    category=FeatureCategory.CUSTOM,
    feature_type=FeatureType.NUMERICAL,
    description='馬番と人気の積',
    required_columns=['馬番', '人気'],
    calculator=custom_calculator
)

manager.add_custom_feature(custom_feature)
```

### バッチ処理

```python
# 年度データの一括処理
from keiba_ai_system.core.processors import RaceProcessor

processor = RaceProcessor()
race_info, race_results = processor.process_race_bin_files(
    year="2024",
    parallel=True,
    max_files=100
)
```

## エラーハンドリング

システムは包括的なエラーハンドリングを提供します：

```python
try:
    results = predictor.predict_race_complete(race_id)
except Exception as e:
    logger.error(f"予測処理中にエラー: {e}")
    # エラー処理
```

## ログ設定

```python
import logging
from keiba_ai_system import setup_logging

# ログレベルを設定
setup_logging(level=logging.DEBUG)
```

## 設定ファイル

YAML形式の設定ファイルでシステムをカスタマイズできます：

```yaml
features:
  enabled_groups:
    - basic
    - performance
    - pedigree
  
  performance:
    lookback_races: 10
    min_races: 3
```

詳細な設定例は `examples/configs/` ディレクトリを参照してください。
