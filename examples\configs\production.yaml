# 本番環境用設定ファイル

# データベース設定
database:
  host: "localhost"
  port: 5432
  name: "keiba_production"
  user: "keiba_user"
  password: "${DB_PASSWORD}"  # 環境変数から取得

# スクレイピング設定
scraping:
  delay_seconds: 2.0  # リクエスト間隔
  max_retries: 3
  timeout_seconds: 30
  user_agent: "KeibaAI/1.0"
  
  # 並列処理
  parallel:
    enabled: true
    max_workers: 4

# データ処理設定
processing:
  # バッチサイズ
  batch_size: 1000
  
  # 並列処理
  parallel:
    enabled: true
    max_workers: 8
  
  # データ品質チェック
  quality_check:
    enabled: true
    min_data_points: 10
    outlier_threshold: 3.0

# 特徴量エンジニアリング設定
features:
  # 有効な特徴量グループ
  enabled_groups:
    - basic
    - performance
    - pedigree
    - jockey
    - trainer
    - race_condition
  
  # 過去成績設定
  performance:
    lookback_races: 10
    min_races: 3
    weight_decay: 0.9
  
  # キャッシュ設定
  cache:
    enabled: true
    max_size_mb: 1000
    ttl_hours: 24

# 予測設定
prediction:
  # モデル設定
  models:
    lightgbm:
      enabled: true
      model_path: "models/lightgbm_production.pkl"
      scaler_path: "models/scaler_production.pkl"
    
    tensorflow:
      enabled: false
      model_path: "models/tensorflow_ranking.h5"
  
  # 予測結果設定
  output:
    save_predictions: true
    output_dir: "predictions"
    include_probabilities: true

# ログ設定
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # ファイル出力
  file:
    enabled: true
    path: "logs/keiba_ai_production.log"
    max_size_mb: 100
    backup_count: 10
  
  # 外部ログサービス
  external:
    enabled: false
    service: "cloudwatch"
    region: "ap-northeast-1"

# パフォーマンス設定
performance:
  # メモリ使用量制限
  memory:
    max_usage_gb: 8.0
    warning_threshold_gb: 6.0
  
  # CPU使用率制限
  cpu:
    max_usage_percent: 80
  
  # タイムアウト設定
  timeouts:
    scraping_seconds: 300
    processing_seconds: 600
    prediction_seconds: 120

# セキュリティ設定
security:
  # API認証
  api:
    enabled: true
    token_expiry_hours: 24
  
  # データ暗号化
  encryption:
    enabled: true
    algorithm: "AES-256"
  
  # アクセス制御
  access_control:
    enabled: true
    allowed_ips:
      - "***********/24"
      - "10.0.0.0/8"

# 監視設定
monitoring:
  # ヘルスチェック
  health_check:
    enabled: true
    interval_seconds: 60
    endpoint: "/health"
  
  # メトリクス
  metrics:
    enabled: true
    collection_interval_seconds: 30
    retention_days: 30
  
  # アラート
  alerts:
    enabled: true
    email_recipients:
      - "<EMAIL>"
    
    # アラート条件
    conditions:
      error_rate_threshold: 0.05
      response_time_threshold_ms: 5000
      memory_usage_threshold: 0.9

# バックアップ設定
backup:
  enabled: true
  schedule: "0 2 * * *"  # 毎日午前2時
  retention_days: 30
  
  # バックアップ対象
  targets:
    - "processed_data"
    - "models"
    - "predictions"
    - "logs"
  
  # ストレージ
  storage:
    type: "s3"
    bucket: "keiba-ai-backups"
    region: "ap-northeast-1"
