#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
バッチ処理スクリプト

大量のレースデータを効率的に処理するためのスクリプト
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List, Optional

# プロジェクトのルートディレクトリをパスに追加
sys.path.append(str(Path(__file__).parent.parent.parent))

from keiba_ai_system.core.processors.race_processor import RaceProcessor
from keiba_ai_system.core.processors.horse_processor import HorseProcessor
from keiba_ai_system.core.features.manager import FeatureEngineeringManager
from keiba_ai_system.prediction.live_predictor import LiveRacePredictor

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def process_year_data(year: str, max_files: Optional[int] = None):
    """
    指定年度のデータを一括処理
    
    Parameters
    ----------
    year : str
        処理対象年度
    max_files : int, optional
        処理するファイルの最大数
    """
    logger.info(f"{year}年度のデータ処理を開始")
    
    # レースプロセッサーを初期化
    race_processor = RaceProcessor()
    
    # レースデータを処理
    race_info_df, race_results_df = race_processor.process_race_bin_files(
        year=year,
        parallel=True,
        max_files=max_files
    )
    
    logger.info(f"レース情報: {len(race_info_df)}件")
    logger.info(f"レース結果: {len(race_results_df)}件")
    
    # データを保存
    output_dir = Path(f"processed_data/{year}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    race_info_df.to_csv(output_dir / "race_info.csv", index=False, encoding='utf-8-sig')
    race_results_df.to_csv(output_dir / "race_results.csv", index=False, encoding='utf-8-sig')
    
    logger.info(f"データを保存しました: {output_dir}")


def process_multiple_races(race_ids: List[str]):
    """
    複数のレースを一括予測
    
    Parameters
    ----------
    race_ids : List[str]
        レースIDのリスト
    """
    logger.info(f"{len(race_ids)}件のレース予測を開始")
    
    # 予測システムを初期化
    predictor = LiveRacePredictor()
    
    results = []
    for race_id in race_ids:
        try:
            logger.info(f"レースID {race_id} を処理中...")
            result_df = predictor.predict_race_complete(race_id)
            if not result_df.empty:
                results.append(result_df)
                logger.info(f"レースID {race_id} の予測完了")
            else:
                logger.warning(f"レースID {race_id} の予測に失敗")
        except Exception as e:
            logger.error(f"レースID {race_id} の処理中にエラー: {e}")
    
    if results:
        # 全結果を統合
        all_results_df = pd.concat(results, ignore_index=True)
        
        # 結果を保存
        output_path = Path("batch_predictions.csv")
        all_results_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"バッチ予測結果を保存: {output_path}")


def create_features_batch(year: str):
    """
    指定年度のデータに対して特徴量を一括作成
    
    Parameters
    ----------
    year : str
        処理対象年度
    """
    logger.info(f"{year}年度の特徴量作成を開始")
    
    # データを読み込み
    data_dir = Path(f"processed_data/{year}")
    if not data_dir.exists():
        logger.error(f"データディレクトリが見つかりません: {data_dir}")
        return
    
    race_results_df = pd.read_csv(data_dir / "race_results.csv")
    
    # 特徴量エンジニアリングマネージャーを初期化
    feature_manager = FeatureEngineeringManager()
    
    # 特徴量を作成
    enhanced_df = feature_manager.calculate_features(race_results_df)
    
    # 結果を保存
    enhanced_df.to_csv(data_dir / "enhanced_features.csv", index=False, encoding='utf-8-sig')
    logger.info(f"特徴量データを保存: {data_dir / 'enhanced_features.csv'}")


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description="競馬データバッチ処理システム")
    parser.add_argument('command', choices=['process', 'predict', 'features'],
                        help='実行するコマンド')
    parser.add_argument('--year', type=str, help='処理対象年度')
    parser.add_argument('--race-ids', nargs='+', help='予測対象レースIDのリスト')
    parser.add_argument('--max-files', type=int, help='処理するファイルの最大数')
    
    args = parser.parse_args()
    
    try:
        if args.command == 'process':
            if not args.year:
                logger.error("--year パラメータが必要です")
                return
            process_year_data(args.year, args.max_files)
            
        elif args.command == 'predict':
            if not args.race_ids:
                logger.error("--race-ids パラメータが必要です")
                return
            process_multiple_races(args.race_ids)
            
        elif args.command == 'features':
            if not args.year:
                logger.error("--year パラメータが必要です")
                return
            create_features_batch(args.year)
            
    except KeyboardInterrupt:
        logger.info("処理が中断されました")
    except Exception as e:
        logger.error(f"処理中にエラーが発生しました: {e}", exc_info=True)


if __name__ == "__main__":
    # 使用例:
    # python batch_processing.py process --year 2024 --max-files 100
    # python batch_processing.py predict --race-ids 202406010101 202406010102
    # python batch_processing.py features --year 2024
    main()
