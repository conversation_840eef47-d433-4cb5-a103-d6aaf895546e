# 特徴量エンジニアリング設定ファイル

# 特徴量グループの設定
feature_groups:
  basic:
    enabled: true
    description: '基本特徴量（馬番、枠番など）'
    priority: 1
  
  performance:
    enabled: true
    description: '過去成績統計特徴量'
    priority: 2
  
  pedigree:
    enabled: true
    description: '血統特徴量'
    priority: 3
  
  jockey:
    enabled: true
    description: '騎手特徴量'
    priority: 4
  
  trainer:
    enabled: true
    description: '調教師特徴量'
    priority: 5
  
  race_condition:
    enabled: true
    description: 'レース条件特徴量'
    priority: 6
  
  advanced:
    enabled: false
    description: '高度な特徴量（実験的）'
    priority: 10

# 過去成績特徴量の設定
performance_features:
  lookback_races: 10        # 過去何レース分を見るか
  min_races: 3             # 最低必要レース数
  weight_decay: 0.9        # 古いレースの重み減衰
  exclude_current_race: true # 現在のレースを除外するか
  
  # 統計特徴量の設定
  statistics:
    - count              # 出走回数
    - mean               # 平均
    - std                # 標準偏差
    - min                # 最小値
    - max                # 最大値
    - median             # 中央値
    - win_rate           # 勝率
    - place_rate         # 連対率
    - show_rate          # 複勝率

# エンコーディング設定
encoding:
  categorical_threshold: 50      # カテゴリカル変数の閾値
  rare_category_threshold: 0.01  # 稀なカテゴリの閾値
  unknown_category: 'UNKNOWN'    # 未知カテゴリの名前
  
  # ラベルエンコーディング対象
  label_encode_columns:
    - 'jockey_id'
    - 'trainer_id'
    - 'owner_id'
    - 'breeder_id'
    - 'father_id'
    - 'mother_father_id'
  
  # ワンホットエンコーディング対象
  onehot_encode_columns:
    - '開催'
    - '芝・ダート'

# 血統特徴量の設定
pedigree_features:
  enabled: true
  depth: 3                # 血統の深さ（父、母父、母母父まで）
  
  # 血統統計特徴量
  statistics:
    - offspring_count    # 産駒数
    - win_rate          # 産駒勝率
    - avg_prize         # 産駒平均賞金
    - grade_race_wins   # 重賞勝利数

# 騎手・調教師特徴量の設定
jockey_trainer_features:
  # 統計期間（日数）
  statistics_period: 365
  
  # 最低データ数
  min_data_count: 10
  
  # 計算する統計量
  statistics:
    - win_rate          # 勝率
    - place_rate        # 連対率
    - show_rate         # 複勝率
    - avg_popularity    # 平均人気
    - avg_odds          # 平均オッズ

# レース条件特徴量の設定
race_condition_features:
  # 距離カテゴリ
  distance_categories:
    short: [1000, 1400]     # 短距離
    mile: [1401, 1800]      # マイル
    middle: [1801, 2200]    # 中距離
    long: [2201, 4000]      # 長距離
  
  # 開催場グループ
  track_groups:
    kanto: ['東京', '中山', '新潟', '福島']
    kansai: ['京都', '阪神', '中京', '小倉']

# 高度な特徴量の設定
advanced_features:
  # 交互作用特徴量
  interactions:
    enabled: false
    pairs:
      - ['馬番', '枠番']
      - ['人気', '斤量']
      - ['距離', '芝・ダート']
  
  # 時系列特徴量
  time_series:
    enabled: false
    features:
      - trend              # トレンド
      - seasonality        # 季節性
      - momentum           # モメンタム
  
  # クラスタリング特徴量
  clustering:
    enabled: false
    n_clusters: 5
    features: ['平均着順', '勝率', '平均賞金']

# データ品質チェック
data_quality:
  # 欠損値の処理
  missing_values:
    strategy: 'fill'      # 'fill', 'drop', 'ignore'
    fill_value: 0         # 数値の場合の補完値
    fill_categorical: 'UNKNOWN'  # カテゴリカルの場合の補完値
  
  # 外れ値の処理
  outliers:
    method: 'iqr'         # 'iqr', 'zscore', 'isolation'
    threshold: 3.0        # 閾値
    action: 'clip'        # 'clip', 'remove', 'ignore'
  
  # データ型チェック
  type_validation:
    enabled: true
    strict_mode: false    # 厳密モード

# キャッシュ設定
cache:
  enabled: true
  max_size_mb: 500      # 最大キャッシュサイズ（MB）
  ttl_hours: 24         # キャッシュの有効期限（時間）
  
  # キャッシュ対象
  cache_targets:
    - 'horse_results'
    - 'jockey_stats'
    - 'trainer_stats'
    - 'pedigree_stats'

# ログ設定
logging:
  level: 'INFO'         # DEBUG, INFO, WARNING, ERROR
  format: '%(asctime)s - %(levelname)s - %(message)s'
  
  # ファイル出力
  file_output:
    enabled: true
    filename: 'feature_engineering.log'
    max_size_mb: 10
    backup_count: 5

# パフォーマンス設定
performance:
  # 並列処理
  parallel:
    enabled: true
    max_workers: 4
  
  # メモリ使用量制限
  memory:
    max_usage_gb: 4.0
    chunk_size: 1000      # データ処理のチャンクサイズ
  
  # 進捗表示
  progress:
    enabled: true
    update_interval: 100  # 更新間隔

# 実験設定
experiments:
  # A/Bテスト
  ab_testing:
    enabled: false
    test_ratio: 0.1       # テスト用データの割合
  
  # 特徴量選択
  feature_selection:
    enabled: false
    method: 'mutual_info'  # 'mutual_info', 'chi2', 'f_classif'
    k_best: 50            # 選択する特徴量数
  
  # 自動特徴量生成
  auto_feature_generation:
    enabled: false
    max_features: 100     # 最大生成特徴量数
