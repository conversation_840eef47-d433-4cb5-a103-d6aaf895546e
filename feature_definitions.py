#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特徴量定義クラス

特徴量のメタデータと定義を管理するためのクラス群
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Optional, Callable, Any, Dict
import pandas as pd


class FeatureCategory(Enum):
    """特徴量のカテゴリ"""
    BASIC = "basic"                    # 基本特徴量（馬番、枠番など）
    PERFORMANCE = "performance"        # 過去成績特徴量
    PEDIGREE = "pedigree"             # 血統特徴量
    JOCKEY = "jockey"                 # 騎手特徴量
    TRAINER = "trainer"               # 調教師特徴量
    RACE_CONDITION = "race_condition" # レース条件特徴量
    INTERACTION = "interaction"       # 交互作用特徴量
    ADVANCED = "advanced"             # 高度な特徴量
    CUSTOM = "custom"                 # カスタム特徴量


class FeatureType(Enum):
    """特徴量のデータ型"""
    NUMERICAL = "numerical"           # 数値特徴量
    CATEGORICAL = "categorical"       # カテゴリカル特徴量
    BINARY = "binary"                # バイナリ特徴量
    ORDINAL = "ordinal"              # 順序特徴量
    TEXT = "text"                    # テキスト特徴量
    DATETIME = "datetime"            # 日時特徴量


class FeatureImportance(Enum):
    """特徴量の重要度"""
    CRITICAL = "critical"            # 必須特徴量
    HIGH = "high"                   # 高重要度
    MEDIUM = "medium"               # 中重要度
    LOW = "low"                     # 低重要度
    EXPERIMENTAL = "experimental"    # 実験的特徴量


@dataclass
class FeatureDefinition:
    """
    特徴量の定義を表すクラス
    
    Attributes
    ----------
    name : str
        特徴量名
    category : FeatureCategory
        特徴量のカテゴリ
    feature_type : FeatureType
        特徴量のデータ型
    description : str
        特徴量の説明
    required_columns : List[str]
        計算に必要なカラム名のリスト
    calculator : Callable
        特徴量を計算する関数
    dependencies : List[str], optional
        依存する他のデータソース
    importance : FeatureImportance, optional
        特徴量の重要度
    enabled : bool, optional
        特徴量が有効かどうか
    parameters : Dict[str, Any], optional
        計算時のパラメータ
    validation_rules : Dict[str, Any], optional
        バリデーションルール
    """
    name: str
    category: FeatureCategory
    feature_type: FeatureType
    description: str
    required_columns: List[str]
    calculator: Callable
    dependencies: List[str] = field(default_factory=list)
    importance: FeatureImportance = FeatureImportance.MEDIUM
    enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初期化後の処理"""
        # バリデーションルールのデフォルト値を設定
        if not self.validation_rules:
            self.validation_rules = self._get_default_validation_rules()
    
    def _get_default_validation_rules(self) -> Dict[str, Any]:
        """データ型に基づくデフォルトバリデーションルールを取得"""
        if self.feature_type == FeatureType.NUMERICAL:
            return {
                'min_value': None,
                'max_value': None,
                'allow_nan': True,
                'allow_inf': False
            }
        elif self.feature_type == FeatureType.CATEGORICAL:
            return {
                'max_categories': 1000,
                'allow_nan': True,
                'encoding_method': 'label'
            }
        elif self.feature_type == FeatureType.BINARY:
            return {
                'valid_values': [0, 1, True, False],
                'allow_nan': False
            }
        else:
            return {}
    
    def validate(self, data: pd.Series) -> bool:
        """
        特徴量データのバリデーション
        
        Parameters
        ----------
        data : pd.Series
            検証するデータ
            
        Returns
        -------
        bool
            バリデーション結果
        """
        try:
            if self.feature_type == FeatureType.NUMERICAL:
                return self._validate_numerical(data)
            elif self.feature_type == FeatureType.CATEGORICAL:
                return self._validate_categorical(data)
            elif self.feature_type == FeatureType.BINARY:
                return self._validate_binary(data)
            else:
                return True
        except Exception:
            return False
    
    def _validate_numerical(self, data: pd.Series) -> bool:
        """数値特徴量のバリデーション"""
        rules = self.validation_rules
        
        # NaN値のチェック
        if not rules.get('allow_nan', True) and data.isna().any():
            return False
        
        # 無限値のチェック
        if not rules.get('allow_inf', False) and np.isinf(data).any():
            return False
        
        # 値の範囲チェック
        numeric_data = data.dropna()
        if len(numeric_data) > 0:
            if rules.get('min_value') is not None and numeric_data.min() < rules['min_value']:
                return False
            if rules.get('max_value') is not None and numeric_data.max() > rules['max_value']:
                return False
        
        return True
    
    def _validate_categorical(self, data: pd.Series) -> bool:
        """カテゴリカル特徴量のバリデーション"""
        rules = self.validation_rules
        
        # NaN値のチェック
        if not rules.get('allow_nan', True) and data.isna().any():
            return False
        
        # カテゴリ数のチェック
        unique_count = data.nunique()
        if rules.get('max_categories') and unique_count > rules['max_categories']:
            return False
        
        return True
    
    def _validate_binary(self, data: pd.Series) -> bool:
        """バイナリ特徴量のバリデーション"""
        rules = self.validation_rules
        
        # NaN値のチェック
        if not rules.get('allow_nan', False) and data.isna().any():
            return False
        
        # 有効値のチェック
        valid_values = rules.get('valid_values', [0, 1])
        non_na_data = data.dropna()
        if len(non_na_data) > 0 and not non_na_data.isin(valid_values).all():
            return False
        
        return True
    
    def get_info(self) -> Dict[str, Any]:
        """特徴量の情報を辞書形式で取得"""
        return {
            'name': self.name,
            'category': self.category.value,
            'feature_type': self.feature_type.value,
            'description': self.description,
            'required_columns': self.required_columns,
            'dependencies': self.dependencies,
            'importance': self.importance.value,
            'enabled': self.enabled,
            'parameters': self.parameters,
            'validation_rules': self.validation_rules
        }


@dataclass
class FeatureGroup:
    """
    特徴量グループを表すクラス
    
    Attributes
    ----------
    name : str
        グループ名
    description : str
        グループの説明
    features : List[FeatureDefinition]
        グループに含まれる特徴量のリスト
    enabled : bool
        グループが有効かどうか
    priority : int
        計算優先度（小さいほど優先）
    """
    name: str
    description: str
    features: List[FeatureDefinition] = field(default_factory=list)
    enabled: bool = True
    priority: int = 100
    
    def add_feature(self, feature: FeatureDefinition):
        """特徴量をグループに追加"""
        self.features.append(feature)
    
    def remove_feature(self, feature_name: str):
        """特徴量をグループから削除"""
        self.features = [f for f in self.features if f.name != feature_name]
    
    def get_enabled_features(self) -> List[FeatureDefinition]:
        """有効な特徴量のリストを取得"""
        return [f for f in self.features if f.enabled]
    
    def get_feature_names(self) -> List[str]:
        """特徴量名のリストを取得"""
        return [f.name for f in self.features]
    
    def get_info(self) -> Dict[str, Any]:
        """グループの情報を辞書形式で取得"""
        return {
            'name': self.name,
            'description': self.description,
            'enabled': self.enabled,
            'priority': self.priority,
            'feature_count': len(self.features),
            'enabled_feature_count': len(self.get_enabled_features()),
            'features': [f.get_info() for f in self.features]
        }


class FeatureRegistry:
    """
    特徴量の登録と管理を行うクラス
    """
    
    def __init__(self):
        self.features: Dict[str, FeatureDefinition] = {}
        self.groups: Dict[str, FeatureGroup] = {}
    
    def register_feature(self, feature: FeatureDefinition):
        """特徴量を登録"""
        self.features[feature.name] = feature
    
    def register_group(self, group: FeatureGroup):
        """特徴量グループを登録"""
        self.groups[group.name] = group
        # グループ内の特徴量も個別に登録
        for feature in group.features:
            self.register_feature(feature)
    
    def get_feature(self, name: str) -> Optional[FeatureDefinition]:
        """特徴量を取得"""
        return self.features.get(name)
    
    def get_group(self, name: str) -> Optional[FeatureGroup]:
        """特徴量グループを取得"""
        return self.groups.get(name)
    
    def get_features_by_category(self, category: FeatureCategory) -> List[FeatureDefinition]:
        """カテゴリ別に特徴量を取得"""
        return [f for f in self.features.values() if f.category == category]
    
    def get_features_by_type(self, feature_type: FeatureType) -> List[FeatureDefinition]:
        """データ型別に特徴量を取得"""
        return [f for f in self.features.values() if f.feature_type == feature_type]
    
    def get_enabled_features(self) -> List[FeatureDefinition]:
        """有効な特徴量のリストを取得"""
        return [f for f in self.features.values() if f.enabled]
    
    def list_features(self) -> List[str]:
        """登録されている特徴量名のリストを取得"""
        return list(self.features.keys())
    
    def list_groups(self) -> List[str]:
        """登録されているグループ名のリストを取得"""
        return list(self.groups.keys())
    
    def get_summary(self) -> Dict[str, Any]:
        """特徴量レジストリの要約を取得"""
        return {
            'total_features': len(self.features),
            'enabled_features': len(self.get_enabled_features()),
            'total_groups': len(self.groups),
            'categories': {cat.value: len(self.get_features_by_category(cat)) 
                          for cat in FeatureCategory},
            'types': {ftype.value: len(self.get_features_by_type(ftype)) 
                     for ftype in FeatureType}
        }
