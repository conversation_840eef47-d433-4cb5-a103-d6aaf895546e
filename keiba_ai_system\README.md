# 競馬AI予測システム統合版

## 🎉 統合完了！

**全てのテストが成功し、統合システムが正常に動作しています！**

- ✅ ディレクトリ構造の整理
- ✅ モジュールのインポート
- ✅ 基本機能の動作確認
- ✅ 特徴量エンジニアリング
- ✅ 設定ファイルの検証

このディレクトリには、競馬予測のための全てのプログラムコードが効率的に整理され、統合テストも完了しています。

## ディレクトリ構造

```
keiba_ai_system/
├── README.md                           # このファイル
├── core/                              # コアモジュール
│   ├── __init__.py
│   ├── processors/                    # データ処理モジュール
│   │   ├── __init__.py
│   │   ├── race_processor.py         # レースデータ処理
│   │   ├── horse_processor.py        # 馬データ処理
│   │   ├── data_merger.py            # データ統合
│   │   └── comprehensive_integrator.py # 包括的データ統合
│   ├── scrapers/                     # スクレイピングモジュール
│   │   ├── __init__.py
│   │   ├── refactored_scrap.py       # リファクタリング済みスクレイピング
│   │   ├── constants.py              # スクレイピング定数
│   │   └── html_parsers.py           # HTMLパーサー
│   ├── features/                     # 特徴量エンジニアリング
│   │   ├── __init__.py
│   │   ├── manager.py                # 特徴量管理システム
│   │   ├── definitions.py            # 特徴量定義
│   │   ├── calculators.py            # 特徴量計算関数
│   │   └── config.yaml               # 特徴量設定
│   └── utils/                        # ユーティリティ
│       ├── __init__.py
│       ├── constants.py              # 共通定数
│       └── helpers.py                # ヘルパー関数
├── prediction/                       # 予測システム
│   ├── __init__.py
│   ├── live_predictor.py             # ライブ予測システム
│   ├── race_predictor.py             # レース予測（TensorFlow Ranking）
│   └── models/                       # 学習済みモデル
│       └── README.md
├── examples/                         # 使用例とノートブック
│   ├── notebooks/                    # Jupyterノートブック
│   │   ├── live_prediction_example.ipynb
│   │   ├── feature_engineering_example.ipynb
│   │   ├── comprehensive_integration_example.ipynb
│   │   └── race_horse_targeted_example.ipynb
│   ├── scripts/                      # 実行スクリプト
│   │   ├── batch_processing.py
│   │   └── data_integration.py
│   └── configs/                      # 設定ファイル例
│       ├── production.yaml
│       └── development.yaml
├── tests/                            # テストファイル
│   ├── __init__.py
│   ├── test_processors.py
│   ├── test_scrapers.py
│   ├── test_features.py
│   └── test_prediction.py
├── docs/                             # ドキュメント
│   ├── API_REFERENCE.md
│   ├── FEATURE_ENGINEERING_GUIDE.md
│   ├── PREDICTION_GUIDE.md
│   └── INTEGRATION_GUIDE.md
└── requirements.txt                  # 依存関係
```

## 主要コンポーネント

### 1. コアモジュール (core/)
- **processors/**: データ処理の中核機能
- **scrapers/**: Webスクレイピング機能
- **features/**: 特徴量エンジニアリング
- **utils/**: 共通ユーティリティ

### 2. 予測システム (prediction/)
- **live_predictor.py**: リアルタイム予測システム
- **race_predictor.py**: TensorFlow Ranking予測システム

### 3. 使用例 (examples/)
- **notebooks/**: 実際の使用例を示すJupyterノートブック
- **scripts/**: バッチ処理用スクリプト
- **configs/**: 設定ファイル例

### 4. テスト (tests/)
- 各モジュールの単体テスト

### 5. ドキュメント (docs/)
- API リファレンス
- 使用ガイド

## クイックスタート

### 1. 環境セットアップ
```bash
pip install -r requirements.txt
```

### 2. 基本的な使用方法
```python
from keiba_ai_system.prediction.live_predictor import LiveRacePredictor
from keiba_ai_system.core.features.manager import FeatureEngineeringManager

# 予測システムの初期化
predictor = LiveRacePredictor()
feature_manager = FeatureEngineeringManager()

# レース予測の実行
results = predictor.predict_race_complete("202406010101")
```

### 3. 特徴量エンジニアリング
```python
# 特徴量の計算
enhanced_data = feature_manager.calculate_features(
    data=race_data,
    dependencies={'horse_results_df': horse_results}
)
```

## 主な機能

### データ処理
- レースデータの自動スクレイピング
- 馬の過去成績データ収集
- データの統合と前処理

### 特徴量エンジニアリング
- 体系的な特徴量管理
- カスタム特徴量の簡単な追加
- 設定ファイルベースの管理

### 予測システム
- リアルタイム予測
- 複数のモデル対応
- 結果の可視化と保存

## 統合の利点

### 1. 一元管理
- 全てのコードが一箇所に整理
- 依存関係の明確化
- バージョン管理の簡素化

### 2. 再利用性
- モジュール化された設計
- 共通インターフェース
- プラグイン可能な構造

### 3. 保守性
- 明確なディレクトリ構造
- 包括的なドキュメント
- テストカバレッジ

### 4. 拡張性
- 新機能の追加が容易
- 既存機能への影響最小化
- 実験的機能の管理

## 移行ガイド

既存のコードからこの統合システムへの移行：

### 1. インポートの変更
```python
# 旧
from module.race_data_processor import RaceProcessor
from live_race_predictor import LiveRacePredictor

# 新
from keiba_ai_system.core.processors.race_processor import RaceProcessor
from keiba_ai_system.prediction.live_predictor import LiveRacePredictor
```

### 2. 設定ファイルの移行
- 既存の設定を `examples/configs/` に配置
- 新しい設定形式に合わせて調整

### 3. データパスの調整
- データディレクトリの統一
- 相対パスの調整

## 開発ガイドライン

### 1. コーディング規約
- PEP 8 準拠
- 型ヒントの使用
- docstring の記述

### 2. テスト
- 新機能には必ずテストを追加
- カバレッジ80%以上を維持

### 3. ドキュメント
- API変更時はドキュメント更新
- 使用例の提供

## ライセンス

このプロジェクトのライセンスに従います。
