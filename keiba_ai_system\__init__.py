#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬AI予測システム統合版

このパッケージは競馬予測のための包括的なシステムを提供します。

主要コンポーネント:
- core: データ処理、スクレイピング、特徴量エンジニアリング
- prediction: 予測システム
- examples: 使用例とノートブック
- tests: テストスイート
- docs: ドキュメント

使用例:
    from keiba_ai_system.prediction.live_predictor import LiveRacePredictor
    from keiba_ai_system.core.features.manager import FeatureEngineeringManager
    
    predictor = LiveRacePredictor()
    results = predictor.predict_race_complete("202406010101")
"""

__version__ = "1.0.0"
__author__ = "Keiba AI Team"
__email__ = "<EMAIL>"

# パッケージレベルのインポート
from keiba_ai_system.core import *
from keiba_ai_system.prediction import *

# ログ設定
import logging
import sys

def setup_logging(level=logging.INFO):
    """
    パッケージ全体のログ設定を行う
    
    Parameters
    ----------
    level : int
        ログレベル
    """
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('keiba_ai_system.log', encoding='utf-8')
        ]
    )

# デフォルトでログ設定を実行
setup_logging()

# パッケージ情報
__all__ = [
    'core',
    'prediction',
    'examples',
    'tests',
    'docs',
    'setup_logging'
]
