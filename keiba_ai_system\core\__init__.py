#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬AI予測システム - コアモジュール

データ処理、スクレイピング、特徴量エンジニアリングの中核機能を提供します。

サブモジュール:
- processors: データ処理モジュール
- scrapers: スクレイピングモジュール  
- features: 特徴量エンジニアリング
- utils: ユーティリティ
"""

# 主要クラスのインポート
from keiba_ai_system.core.processors.race_processor import RaceProcessor
from keiba_ai_system.core.processors.horse_processor import HorseProcessor
from keiba_ai_system.core.processors.data_merger import DataMerger
from keiba_ai_system.core.processors.comprehensive_integrator import ComprehensiveDataIntegrator

from keiba_ai_system.core.scrapers.refactored_scrap import (
    scrape_html_race,
    scrape_html_horse,
    scrape_html_ped
)

from keiba_ai_system.core.features.manager import FeatureEngineeringManager
from keiba_ai_system.core.features.definitions import (
    FeatureDefinition,
    FeatureCategory,
    FeatureType,
    FeatureImportance
)

from keiba_ai_system.core.utils.constants import LocalPaths, UrlPaths

__all__ = [
    # Processors
    'RaceProcessor',
    'HorseProcessor', 
    'DataMerger',
    'ComprehensiveDataIntegrator',
    
    # Scrapers
    'scrape_html_race',
    'scrape_html_horse',
    'scrape_html_ped',
    
    # Features
    'FeatureEngineeringManager',
    'FeatureDefinition',
    'FeatureCategory',
    'FeatureType',
    'FeatureImportance',
    
    # Utils
    'LocalPaths',
    'UrlPaths'
]
