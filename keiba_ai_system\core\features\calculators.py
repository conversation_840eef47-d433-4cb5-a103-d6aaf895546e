#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特徴量計算関数

個別の特徴量を計算するための関数群
"""

import logging
import re
from typing import Dict, List, Any, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import LabelEncoder, StandardScaler

logger = logging.getLogger(__name__)


class FeatureCalculators:
    """
    特徴量計算関数を提供するクラス
    """
    
    def __init__(self):
        self.label_encoders: Dict[str, LabelEncoder] = {}
        self.scalers: Dict[str, StandardScaler] = {}
    
    # ===== 基本特徴量計算関数 =====
    
    def identity(self, data: pd.DataFrame, column: str, **kwargs) -> pd.Series:
        """
        そのまま返す（恒等関数）
        
        Parameters
        ----------
        data : pd.DataFrame
            入力データ
        column : str
            対象カラム名
            
        Returns
        -------
        pd.Series
            そのままのデータ
        """
        if column in data.columns:
            return data[column].copy()
        else:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
    
    def encode_sex(self, data: pd.DataFrame, column: str = '性齢', **kwargs) -> pd.Series:
        """
        性別を数値化（牡=1, 牝=2, セ=3）
        
        Parameters
        ----------
        data : pd.DataFrame
            入力データ
        column : str
            性齢カラム名
            
        Returns
        -------
        pd.Series
            数値化された性別
        """
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        sex_map = {'牡': 1, '牝': 2, 'セ': 3}
        
        def extract_sex(seirei):
            if pd.isna(seirei):
                return np.nan
            seirei_str = str(seirei)
            for sex, code in sex_map.items():
                if sex in seirei_str:
                    return code
            return np.nan
        
        return data[column].apply(extract_sex)
    
    def extract_age(self, data: pd.DataFrame, column: str = '性齢', **kwargs) -> pd.Series:
        """
        性齢から年齢を抽出
        
        Parameters
        ----------
        data : pd.DataFrame
            入力データ
        column : str
            性齢カラム名
            
        Returns
        -------
        pd.Series
            年齢
        """
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        def extract_age_value(seirei):
            if pd.isna(seirei):
                return np.nan
            # 数字を抽出
            match = re.search(r'\d+', str(seirei))
            if match:
                return int(match.group())
            return np.nan
        
        return data[column].apply(extract_age_value)
    
    # ===== 過去成績特徴量計算関数 =====
    
    def calculate_race_count(self, data: pd.DataFrame, horse_results_df: pd.DataFrame, 
                           column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の出走回数を計算
        
        Parameters
        ----------
        data : pd.DataFrame
            メインデータ
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        column : str
            馬IDカラム名
            
        Returns
        -------
        pd.Series
            出走回数
        """
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        if horse_results_df.empty:
            return pd.Series(0, index=data.index)
        
        race_counts = horse_results_df.groupby('horse_id').size()
        return data[column].map(race_counts).fillna(0)
    
    def calculate_avg_rank(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の平均着順を計算
        
        Parameters
        ----------
        data : pd.DataFrame
            メインデータ
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        column : str
            馬IDカラム名
            
        Returns
        -------
        pd.Series
            平均着順
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        # 着順を数値に変換
        numeric_ranks = pd.to_numeric(horse_results_df['着順'], errors='coerce')
        horse_results_numeric = horse_results_df.copy()
        horse_results_numeric['着順_数値'] = numeric_ranks
        
        avg_ranks = horse_results_numeric.groupby('horse_id')['着順_数値'].mean()
        return data[column].map(avg_ranks).fillna(np.nan)
    
    def calculate_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の勝率（1着率）を計算
        
        Parameters
        ----------
        data : pd.DataFrame
            メインデータ
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        column : str
            馬IDカラム名
            
        Returns
        -------
        pd.Series
            勝率
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        # 1着の回数と総出走回数を計算
        horse_stats = horse_results_df.groupby('horse_id').agg({
            '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') == 1).sum()]
        })
        horse_stats.columns = ['total_races', 'wins']
        horse_stats['win_rate'] = horse_stats['wins'] / horse_stats['total_races']
        
        return data[column].map(horse_stats['win_rate']).fillna(0.0)
    
    def calculate_place_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                           column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の連対率（1-2着率）を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        horse_stats = horse_results_df.groupby('horse_id').agg({
            '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') <= 2).sum()]
        })
        horse_stats.columns = ['total_races', 'places']
        horse_stats['place_rate'] = horse_stats['places'] / horse_stats['total_races']
        
        return data[column].map(horse_stats['place_rate']).fillna(0.0)
    
    def calculate_show_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の複勝率（1-3着率）を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        horse_stats = horse_results_df.groupby('horse_id').agg({
            '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') <= 3).sum()]
        })
        horse_stats.columns = ['total_races', 'shows']
        horse_stats['show_rate'] = horse_stats['shows'] / horse_stats['total_races']
        
        return data[column].map(horse_stats['show_rate']).fillna(0.0)
    
    def calculate_avg_prize(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の平均獲得賞金を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '賞金' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        # 賞金を数値に変換
        numeric_prizes = pd.to_numeric(horse_results_df['賞金'], errors='coerce')
        avg_prizes = horse_results_df.groupby('horse_id').apply(
            lambda x: pd.to_numeric(x['賞金'], errors='coerce').mean()
        )
        
        return data[column].map(avg_prizes).fillna(0.0)
    
    def calculate_max_prize(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        過去の最高獲得賞金を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '賞金' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        max_prizes = horse_results_df.groupby('horse_id').apply(
            lambda x: pd.to_numeric(x['賞金'], errors='coerce').max()
        )
        
        return data[column].map(max_prizes).fillna(0.0)
    
    def calculate_last_rank(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', **kwargs) -> pd.Series:
        """
        前走の着順を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        # 日付でソートして最新のレースの着順を取得
        if '日付' in horse_results_df.columns:
            horse_results_sorted = horse_results_df.sort_values(['horse_id', '日付'])
            last_ranks = horse_results_sorted.groupby('horse_id')['着順'].last()
            last_ranks = pd.to_numeric(last_ranks, errors='coerce')
        else:
            # 日付がない場合は最後の行を使用
            last_ranks = horse_results_df.groupby('horse_id')['着順'].last()
            last_ranks = pd.to_numeric(last_ranks, errors='coerce')
        
        return data[column].map(last_ranks).fillna(np.nan)
    
    def calculate_days_since_last_race(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                                     column: str = 'horse_id', race_date: str = None, **kwargs) -> pd.Series:
        """
        前走からの日数を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '日付' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        # レース日付を取得
        if race_date is None:
            # データから推測（実装は省略）
            current_date = datetime.now()
        else:
            current_date = pd.to_datetime(race_date)
        
        # 各馬の最新レース日付を取得
        horse_results_df['日付_dt'] = pd.to_datetime(horse_results_df['日付'], errors='coerce')
        last_race_dates = horse_results_df.groupby('horse_id')['日付_dt'].max()
        
        # 日数差を計算
        days_since = last_race_dates.apply(lambda x: (current_date - x).days if pd.notna(x) else np.nan)
        
        return data[column].map(days_since).fillna(np.nan)
    
    # ===== 騎手・調教師特徴量計算関数 =====
    
    def calculate_jockey_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                                column: str = 'jockey_id', **kwargs) -> pd.Series:
        """
        騎手の勝率を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if 'jockey_id' not in horse_results_df.columns or '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        jockey_stats = horse_results_df.groupby('jockey_id').agg({
            '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') == 1).sum()]
        })
        jockey_stats.columns = ['total_races', 'wins']
        jockey_stats['win_rate'] = jockey_stats['wins'] / jockey_stats['total_races']
        
        return data[column].map(jockey_stats['win_rate']).fillna(0.0)
    
    def calculate_trainer_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                                 column: str = 'trainer_id', **kwargs) -> pd.Series:
        """
        調教師の勝率を計算
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if 'trainer_id' not in horse_results_df.columns or '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
        
        trainer_stats = horse_results_df.groupby('trainer_id').agg({
            '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') == 1).sum()]
        })
        trainer_stats.columns = ['total_races', 'wins']
        trainer_stats['win_rate'] = trainer_stats['wins'] / trainer_stats['total_races']
        
        return data[column].map(trainer_stats['win_rate']).fillna(0.0)
    
    # ===== レース条件特徴量計算関数 =====
    
    def encode_race_surface(self, data: pd.DataFrame, column: str = '芝・ダート', **kwargs) -> pd.Series:
        """
        レース場の芝・ダートを数値化
        """
        if column not in data.columns:
            return pd.Series(index=data.index, dtype=float)
        
        surface_map = {'芝': 1, 'ダート': 2, 'ダ': 2}
        return data[column].map(surface_map).fillna(0)
    
    def encode_race_distance_category(self, data: pd.DataFrame, column: str = '距離', **kwargs) -> pd.Series:
        """
        距離をカテゴリ化（短距離、マイル、中距離、長距離）
        """
        if column not in data.columns:
            return pd.Series(index=data.index, dtype=float)
        
        def categorize_distance(distance):
            if pd.isna(distance):
                return np.nan
            distance = int(distance)
            if distance <= 1400:
                return 1  # 短距離
            elif distance <= 1800:
                return 2  # マイル
            elif distance <= 2200:
                return 3  # 中距離
            else:
                return 4  # 長距離
        
        return data[column].apply(categorize_distance)
    
    # ===== ユーティリティ関数 =====
    
    def label_encode(self, data: pd.DataFrame, column: str, encoder_name: str = None, **kwargs) -> pd.Series:
        """
        ラベルエンコーディング
        """
        if column not in data.columns:
            return pd.Series(index=data.index, dtype=float)
        
        if encoder_name is None:
            encoder_name = column
        
        if encoder_name not in self.label_encoders:
            self.label_encoders[encoder_name] = LabelEncoder()
            # 未知の値を処理するため、'UNKNOWN'を追加
            unique_values = data[column].dropna().unique().tolist()
            if 'UNKNOWN' not in unique_values:
                unique_values.append('UNKNOWN')
            self.label_encoders[encoder_name].fit(unique_values)
        
        # 未知の値を'UNKNOWN'に置換
        encoded_data = data[column].fillna('UNKNOWN')
        encoded_data = encoded_data.apply(
            lambda x: x if x in self.label_encoders[encoder_name].classes_ else 'UNKNOWN'
        )
        
        return pd.Series(
            self.label_encoders[encoder_name].transform(encoded_data),
            index=data.index
        )
