import logging
import os
import random
import re
import socket
import time
from urllib.error import HTT<PERSON><PERSON>rror, URLError
from urllib.request import Request, urlopen

import pandas as pd
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from tqdm.auto import tqdm

from module.constants import (BASE_HEADERS, DEFAULT_IMPLICIT_WAIT, LocalPaths,
                                MAX_RETRIES, NORMAL_SLEEP_RANGE,
                                REQUEST_TIMEOUT, RETRY_SLEEP_RANGE, UrlPaths,
                                USER_AGENTS, WEBDRIVER_TIMEOUT)

logger = logging.getLogger(__name__)
#AI用指示：並列化は入れるな
def setup_logger(debug_level=False, log_file="scraping.log"):
    """
    ロガーを設定します。

    Args:
        debug_level (bool, optional): Trueの場合、ログレベルをDEBUGに設定します。デフォルトはFalse。
        log_file (str, optional): ログファイル名。デフォルトは "scraping.log"。
    """
    root_logger = logging.getLogger()
    # 既存のハンドラをすべてクリア
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # ファイルハンドラ
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(log_formatter)
        root_logger.addHandler(file_handler)
    # ストリームハンドラ（コンソール出力）
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(log_formatter)

    root_logger.addHandler(stream_handler)

    if debug_level:
        root_logger.setLevel(logging.DEBUG)
        logger.info("ログレベルをDEBUGに設定しました")
    else:
        root_logger.setLevel(logging.INFO)
    # 外部ライブラリのログレベルを調整
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("selenium.webdriver.remote.remote_connection").setLevel(logging.WARNING)
    logging.getLogger("selenium.webdriver.common.service").setLevel(logging.WARNING)
    logging.getLogger("webdriver_manager").setLevel(logging.WARNING)


setup_logger()

def prepare_chrome_driver(headless=True, page_load_timeout=180, connection_timeout=120):
    """
    Chrome WebDriverのインスタンスを準備して返します。
    パフォーマンスとタイムアウト設定を最適化しています。

    Args:
        headless (bool, optional): Trueの場合、ヘッドレスモードでWebDriverを起動します。デフォルトはTrue。
        page_load_timeout (int, optional): ページ読み込みのタイムアウト時間（秒）。デフォルトは180秒。
        connection_timeout (int, optional): 接続タイムアウト時間（秒）。デフォルトは120秒。

    Returns:
        selenium.webdriver.chrome.webdriver.WebDriver: WebDriverインスタンス。
                                                      初期化に失敗した場合はNoneを返します。
    """
    # タイムアウト設定を調整
    socket.setdefaulttimeout(connection_timeout)

    options = webdriver.ChromeOptions()

    # 基本設定
    if headless:
        options.add_argument('--headless=new')  # 新しいヘッドレスモード

    # パフォーマンス最適化
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-browser-side-navigation')
    options.add_argument('--disable-features=NetworkService')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--disable-web-security')
    options.add_argument('--allow-running-insecure-content')
    options.add_argument('--window-size=1920,1080')

    # メモリ使用量の最適化
    options.add_argument('--js-flags=--expose-gc')
    options.add_argument('--disable-infobars')
    options.add_argument('--disable-notifications')
    options.add_argument('--disable-popup-blocking')

    # ログ設定
    options.add_experimental_option('excludeSwitches', ['enable-logging'])
    options.add_argument('--log-level=3')

    # ページロード戦略
    options.page_load_strategy = 'eager'

    try:
        # WebDriverManagerを使用してChromeDriverをインストール
        service = Service(ChromeDriverManager().install())

        # ドライバーの初期化
        driver = webdriver.Chrome(service=service, options=options)

        # タイムアウト設定
        driver.set_page_load_timeout(page_load_timeout)
        driver.set_script_timeout(connection_timeout)

        logger.info(f"Chrome WebDriverを{'ヘッドレスモードで' if headless else ''}初期化しました。")
        logger.info(f"ページロードタイムアウト: {page_load_timeout}秒, スクリプトタイムアウト: {connection_timeout}秒")

        return driver
    except Exception as e:
        logger.error(f"Chrome WebDriverの初期化に失敗しました: {e}")
        logger.error("ChromeDriverがPATHに通っているか、または適切な場所に配置されているか確認してください。")
        return None

def scrape_netkeiba_race_dates(from_: str, to_: str):
    """
    指定された期間内の競馬開催日をnetkeiba.comからスクレイピングします。

    Args:
        from_ (str): 取得開始日。'yyyy-mm-dd' または 'yyyy-mm' 形式。
                     指定された月の初日から処理対象となります。
        to_ (str): 取得終了日。'yyyy-mm-dd' または 'yyyy-mm' 形式。
                   指定された月のカレンダーは取得対象外です
                   (例: to_='2023-05-10' の場合、2023年4月までのカレンダーを取得)。

    Returns:
        list[str]: 開催日のリスト (例: ['20230304', '20230305', ...])。
                   日付は 'yyyymmdd' 形式の文字列です。
                   取得できなかった場合や対象期間に開催がない場合は空のリストを返します。
    """
    logger.info(f'{from_} から {to_} までの開催日を取得します。')
    # 月初日で揃える
    try:
        from_month = pd.to_datetime(from_).replace(day=1)
        # to_の月を含まないように、to_の月の前月末までを範囲とする
        end_date_for_range = pd.to_datetime(to_).replace(day=1) - pd.Timedelta(days=1)
    except ValueError as e:
        logger.error(f"日付形式が無効です: from='{from_}', to='{to_}'. エラー: {e}")
        return []


    if from_month > end_date_for_range:
        logger.warning(f"指定された期間 ({from_} - {to_}) では取得対象となる月がありません。")
        return []

    date_range = pd.date_range(start=from_month, end=end_date_for_range, freq="MS")
    kaisai_date_list = []

    for dt in tqdm(date_range, desc="開催カレンダー取得中"):
        year = int(dt.year)
        month = int(dt.month)
        url = f"{UrlPaths.CALENDAR_URL}?year={year}&month={month}"

        for attempt in range(MAX_RETRIES):
            try:
                # ランダムなUser-Agentを選択
                user_agent = random.choice(USER_AGENTS)
                headers = {**BASE_HEADERS, 'User-Agent': user_agent}

                # attemptが0の時だけログ出力
                if attempt == 0:
                    logger.info(f"URLにアクセスします: {url}")
                    logger.debug(f"使用するUser-Agent: {user_agent}") # User-Agentはデバッグレベルで十分か

                req = Request(url, headers=headers)
                # タイムアウトを設定
                html = urlopen(req, timeout=REQUEST_TIMEOUT).read()

                # 待機時間を3〜6秒のランダムに変更
                sleep_time = random.uniform(NORMAL_SLEEP_RANGE[0], NORMAL_SLEEP_RANGE[1])
                if attempt == 0:
                    logger.info(f"{sleep_time:.2f}秒待機します。")
                time.sleep(sleep_time)

                soup = BeautifulSoup(html, "html.parser")
                calendar_table = soup.find('table', class_='Calendar_Table')
                if calendar_table is None:
                    logger.warning(f"カレンダーテーブルが見つかりませんでした: {url}")
                    logger.debug(f"HTML内容抜粋: {soup.prettify()[:500]}...")
                    continue
                a_list = calendar_table.find_all('a')
                if not a_list:
                    logger.info(f"{year}年{month}月には開催がありませんでした。 ({url})") # 開催がない場合はリトライ不要
                    break # この月の処理は終了
                for a in a_list:
                    href = a.get('href')
                    if href and 'kaisai_date=' in href:
                        match = re.search(r'kaisai_date=(\d{8})', href) # yyyymmdd形式の日付を期待
                        if match:
                            kaisai_date_list.append(match.group(1))
                        else:
                            logger.debug(f"日付形式に一致しないリンクが見つかりました: {href} ({url})")
                break  # 成功したらリトライループを抜ける
            except HTTPError as e:
                logger.error(f"HTTPエラーが発生しました ({url}): {e.code} {e.reason}")
                if e.code == 400:
                    logger.error("HTTP 400エラーが発生しました。処理を強制終了します。")
                    # 400エラーの場合はリトライせず、例外を再発生させて強制終了
                    raise e
            except URLError as e:
                logger.error(f"URLエラーが発生しました ({url}): {e.reason}")
            except socket.timeout:
                logger.error(f"リクエストがタイムアウトしました ({url})")
            except Exception as e:
                logger.error(f"スクレイピング中に予期せぬエラーが発生しました ({url}): {e.__class__.__name__} - {e}")

            if attempt < MAX_RETRIES - 1:
                logger.info(f"リトライします ({attempt + 1}/{MAX_RETRIES})。")
                retry_sleep = random.uniform(RETRY_SLEEP_RANGE[0], RETRY_SLEEP_RANGE[1])
                logger.info(f"{retry_sleep:.2f}秒待機します。")
                time.sleep(retry_sleep)
            else:
                logger.error(f"URL {url} の処理が最大リトライ回数に達しました。")

    # 重複を削除
    kaisai_date_list = sorted(list(set(kaisai_date_list)))
    logger.info(f"取得した開催日数: {len(kaisai_date_list)}")
    return kaisai_date_list


def scrape_netkeiba_race_ids(race_date_list: list, implicit_wait_time: int = DEFAULT_IMPLICIT_WAIT, debug: bool = False, connection_timeout: int = 180, max_retries: int = 3, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    指定された開催日のリストに基づき、netkeiba.comからレースIDのリストを取得します。
    Args:
        race_date_list (list[str]): 開催日のリスト。各日付は 'yyyymmdd' 形式の文字列。
        implicit_wait_time (int, optional): WebDriverが要素を見つけるまでの最大待機時間（秒）。
        debug (bool, optional): Trueでprintによるデバッグ出力を有効化。
        connection_timeout (int, optional): Seleniumの接続タイムアウト時間（秒）。デフォルトは180秒。
        sleep_range (tuple, optional): 各リクエスト後の待機時間の範囲 (min, max)。デフォルトはNORMAL_SLEEP_RANGE。
        max_retries (int, optional): 最大リトライ回数。デフォルトは3回。
    Returns:
        list[str]: レースIDのリスト。取得できなかった場合やエラー発生時は空のリストを返します。
    """

    race_id_list = []
    driver = prepare_chrome_driver(connection_timeout=connection_timeout)
    if driver is None:
        logger.error("WebDriverの初期化に失敗したため、レースIDのスクレイピングを中止します。")
        return []

    driver.implicitly_wait(implicit_wait_time)  # 引数の値を使用
    if debug: # setup_loggerで設定されるため、ここでのloggerレベル変更は不要かもしれない
        logger.info('デバッグモードで実行中 (scrape_netkeiba_race_ids)')

    logger.info('getting race_id_list')

    try:
        for kaisai_date in tqdm(race_date_list):
            try:
                query = [
                    'kaisai_date=' + str(kaisai_date)
                ]
                url = UrlPaths.RACE_LIST_URL + '?' + '&'.join(query)
                logger.info(f'scraping: {url}')

                # ランダムなUser-Agentを選択
                user_agent = random.choice(USER_AGENTS)
                logger.debug(f"使用するUser-Agent: {user_agent}")

                # User-Agent偽装
                driver.execute_cdp_cmd('Network.setUserAgentOverride', {"userAgent": user_agent})

                # Cookieをクリア
                driver.delete_all_cookies()

                # ページにアクセス
                driver.get(url)

                # WebDriverWaitを使用して要素が見つかるまで待機（明示的な待機）
                wait = WebDriverWait(driver, WEBDRIVER_TIMEOUT)

                a_list = [] # a_listを初期化
                try:
                    # 要素が見つかるまで待機（暗黙的な待機と明示的な待機の組み合わせ）
                    race_list_box = wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'RaceList_Box')))
                    a_list = race_list_box.find_elements(By.TAG_NAME, 'a')
                except Exception as e:
                    # 要素が見つからない場合はリトライ
                    logger.warning(f'RaceList_Boxが見つかりませんでした: {e}')

                    # リトライ処理
                    for i in range(max_retries):
                        try:
                            # リトライ前に待機
                            waiting_time = random.uniform(RETRY_SLEEP_RANGE[0], RETRY_SLEEP_RANGE[1])
                            logger.warning(f'retry:{i+1}/{max_retries} waiting {waiting_time:.2f} seconds')
                            time.sleep(waiting_time)
                            driver.refresh() # ページをリフレッシュしてみる

                            # 再度要素を探す
                            race_list_box = driver.find_element(By.CLASS_NAME, 'RaceList_Box')
                            if race_list_box: # 要素が見つかった場合のみa_listを更新
                                a_list = race_list_box.find_elements(By.TAG_NAME, 'a')
                                break # 成功したらリトライループを抜ける
                        except Exception as e:
                            logger.error(f'リトライ中にRaceList_Boxが見つかりませんでした: {e}')
                            if i == max_retries - 1:
                                logger.error(f'最大リトライ回数に達しました。スキップします: {url}')
                                break # リトライループを抜ける
                            continue # 次のリトライへ

                if not a_list:
                    logger.warning(f'レースIDが見つかりませんでした: {url}')
                    continue # 次の開催日へ

                for a in a_list:
                    href = a.get_attribute('href')
                    if href and 'race_id=' in href:
                        match = re.search(r'race_id=(\d+)', href)
                        if match:
                            race_id_list.append(match.group(1))
                        else:
                            logger.debug(f"レースID形式に一致しないリンクが見つかりました: {href} ({url})")
                
                # 待機時間を3〜6秒のランダムに変更
                sleep_time = random.uniform(sleep_range[0], sleep_range[1])
                logger.info(f"{sleep_time:.2f}秒待機します。")
                time.sleep(sleep_time)

            except Exception as e:
                logger.error(f"レースIDスクレイピング中に予期せぬエラーが発生しました ({url}): {e.__class__.__name__} - {e}")
                # エラー発生時も次の開催日へ進む
                continue

    finally:
        if driver:
            driver.quit()
            logger.info("WebDriverを終了しました。")

    # 重複を削除
    race_id_list = sorted(list(set(race_id_list)))
    logger.info(f"取得したレースID数: {len(race_id_list)}")
    return race_id_list


def scrape_html_by_id(id_list, url_base, save_dir, id_type="race", skip=True, validation_func=None, desc=None, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    指定されたIDリストに基づき、netkeiba.comからHTMLをスクレイピングし、ローカルに保存します。
    リトライ機構をサポートします。（並列処理は削除）

    Args:
        id_list (list): スクレイピング対象のIDのリスト（例: レースID、馬ID）。
        url_base (str): スクレイピング対象のURLのベース（例: UrlPaths.RACE_URL）。
        save_dir (str): HTMLを保存するディレクトリのパス。
        id_type (str, optional): IDのタイプ（"race", "horse", "ped"など）。ログ出力用。デフォルトは"race"。
        skip (bool, optional): 既にファイルが存在する場合にスキップするかどうか。デフォルトはTrue。
        validation_func (callable, optional): スクレイピングしたHTMLの妥当性を検証する関数。
                                             引数にHTML文字列を取り、True/Falseを返す。
        desc (str, optional): tqdmのプログレスバーに表示する説明。デフォルトはNone。
        sleep_range (tuple, optional): 各リクエスト後の待機時間の範囲 (min, max)。デフォルトはNORMAL_SLEEP_RANGE。

    Returns:
        list[str]: スクレイピングに成功したIDのリスト。
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        logger.info(f"ディレクトリを作成しました: {save_dir}")

    successful_ids = []

    for item_id in id_list:
        try:
            if _fetch_and_save_html(item_id, url_base, save_dir, id_type, skip, validation_func, sleep_range):
                successful_ids.append(item_id)
        except HTTPError as e: # HTTP400エラーをここでキャッチしてループを抜ける
            if e.code == 400:
                logger.error(f"ID {item_id} の処理中にHTTP 400エラーが発生しました。スクレイピング処理を中断します。")
                break
            else:
                logger.error(f"ID {item_id} の処理中にHTTPエラーが発生しました: {e}")
        except Exception as e:
            logger.error(f"ID {item_id} の処理中に予期せぬエラーが発生しました: {e}")
    
    logger.debug(f"スクレイピングに成功した{id_type}の数: {len(successful_ids)}")
    return successful_ids

def _fetch_and_save_html(item_id, url_base, save_dir, id_type, skip, validation_func, sleep_range_on_success: tuple = NORMAL_SLEEP_RANGE):
    """
    単一のIDに対応するHTMLをフェッチし、保存するヘルパー関数。
    順次処理のために使用されます。
    成功時には指定された範囲で待機します。
    """
    file_path = os.path.join(save_dir, f"{item_id}.bin")

    if skip and os.path.exists(file_path):
        logger.debug(f"ファイルは既に存在します。スキップします: {file_path}")
        return True # スキップ成功

    url = f"{url_base}{item_id}"
    
    for attempt in range(MAX_RETRIES):
        try:
            user_agent = random.choice(USER_AGENTS)
            headers = {**BASE_HEADERS, 'User-Agent': user_agent}
            req = Request(url, headers=headers)
            
            # タイムアウトを設定
            html_content = urlopen(req, timeout=REQUEST_TIMEOUT).read()

            # HTMLの妥当性検証
            if validation_func and not validation_func(html_content):
                logger.warning(f"ID {item_id} のHTMLコンテンツが不正です。リトライします。 ({url})")
                if attempt == MAX_RETRIES - 1:
                    logger.error(f"ID {item_id} のHTMLコンテンツが不正なため、スキップします。")
                    return False
                time.sleep(random.uniform(RETRY_SLEEP_RANGE[0], RETRY_SLEEP_RANGE[1]))
                continue # 次のリトライへ

            with open(file_path, 'wb') as f:
                f.write(html_content)
            logger.debug(f"HTMLを保存しました: {file_path}")
            
            sleep_time = random.uniform(sleep_range_on_success[0], sleep_range_on_success[1])
            logger.debug(f"ID {item_id} HTML取得成功。{sleep_time:.2f}秒待機します。")
            time.sleep(sleep_time)
            return True # 成功

        except HTTPError as e:
            logger.error(f"HTTPエラーが発生しました ({url}): {e.code} {e.reason}")
            if e.code == 400:
                logger.error(f"ID {item_id}: HTTP 400エラーが発生しました。処理を強制終了します。")
                # 400エラーの場合はリトライせず、例外を再発生させて強制終了
                raise e
        except URLError as e:
            logger.error(f"URLエラーが発生しました ({url}): {e.reason}")
        except socket.timeout:
            logger.error(f"リクエストがタイムアウトしました ({url})")
        except Exception as e:
            logger.error(f"ID {item_id} のスクレイピング中に予期せぬエラーが発生しました ({url}): {e.__class__.__name__} - {e}")

        if attempt < MAX_RETRIES - 1:
            retry_sleep = random.uniform(RETRY_SLEEP_RANGE[0], RETRY_SLEEP_RANGE[1])
            logger.info(f"ID {item_id} のリトライ ({attempt + 1}/{MAX_RETRIES})。{retry_sleep:.2f}秒待機します。")
            time.sleep(retry_sleep)
        else:
            logger.error(f"ID {item_id} のスクレイピングが最大リトライ回数に達しました。スキップします。")
            return False # 失敗

def validate_race_html(html):
    """
    レースHTMLの妥当性を検証します。
    レース結果テーブルが存在するかどうかを確認します。
    """
    try:
        soup = BeautifulSoup(html, "html.parser")
        # レース結果テーブルの存在を確認
        if soup.find('div', class_='RaceTableWrap') or soup.find('table', class_='RaceTable'):
            return True
        logger.warning("レースHTMLにRaceTableWrapまたはRaceTableが見つかりませんでした。")
        return False
    except Exception as e:
        logger.error(f"HTML検証中にエラーが発生しました: {e}")
        return False


def scrape_html_race(race_id_list: list, skip: bool = True, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    レースIDリストからレースHTMLをスクレイピングし、年代別ディレクトリに保存します。
    """
    logger.info(f"レースHTMLのスクレイピングを開始します。対象レース数: {len(race_id_list)}")
    successful_ids = []
    for race_id in tqdm(race_id_list, desc="レースHTML取得中（年代別保存）"):
        # 年代（年）をrace_idの先頭4桁から抽出
        year = str(race_id)[:4]
        save_dir = os.path.join(LocalPaths.HTML_RACE_DIR, year)
        os.makedirs(save_dir, exist_ok=True)
        if scrape_html_by_id(
            id_list=[race_id],
            url_base=UrlPaths.RACE_URL,
            save_dir=save_dir,
            id_type="race",
            skip=skip,
            validation_func=validate_race_html,
            desc=None,
            sleep_range=sleep_range
        ):
            successful_ids.append(race_id)
    logger.info(f"年代別ディレクトリに保存したレースHTML数: {len(successful_ids)}")
    return successful_ids

def scrape_html_horse(horse_id_list: list, skip: bool = True, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    馬IDリストから馬HTMLをスクレイピングし、年代別ディレクトリに保存します。
    """
    logger.info(f"馬HTMLのスクレイピングを開始します。対象馬数: {len(horse_id_list)}")
    successful_ids = []
    for horse_id in tqdm(horse_id_list, desc="馬HTML取得中（年代別保存）"):
        # 年代（年）をhorse_idの先頭4桁から抽出
        year = str(horse_id)[:4]
        save_dir = os.path.join(LocalPaths.HTML_HORSE_DIR, year)
        os.makedirs(save_dir, exist_ok=True)
        if scrape_html_by_id(
            id_list=[horse_id],
            url_base=UrlPaths.HORSE_URL,
            save_dir=save_dir,
            id_type="horse",
            skip=skip,
            desc=None,
            sleep_range=sleep_range
        ):
            successful_ids.append(horse_id)
    logger.info(f"年代別ディレクトリに保存した馬HTML数: {len(successful_ids)}")
    return successful_ids

def scrape_html_ped(horse_id_list: list, skip: bool = True, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    馬IDリストから血統HTMLをスクレイピングし、年代別ディレクトリに保存します。
    """
    logger.info(f"血統HTMLのスクレイピングを開始します。対象馬数: {len(horse_id_list)}")
    successful_ids = []
    for horse_id in tqdm(horse_id_list, desc="血統HTML取得中（年代別保存）"):
        year = str(horse_id)[:4]
        save_dir = os.path.join(LocalPaths.HTML_PED_DIR, year)
        os.makedirs(save_dir, exist_ok=True)
        if scrape_html_by_id(
            id_list=[horse_id],
            url_base=UrlPaths.PED_URL,
            save_dir=save_dir,
            id_type="ped",
            skip=skip,
            desc=None,
            sleep_range=sleep_range
        ):
            successful_ids.append(horse_id)
    logger.debug(f"年代別ディレクトリに保存した血統HTML数: {len(successful_ids)}")
    return successful_ids


def scrape_html_horse_with_master(horse_id_list: list, skip: bool = True, sleep_range: tuple = NORMAL_SLEEP_RANGE):
    """
    馬IDリストから馬HTMLをスクレイピングし、マスターファイルを更新します。
    """
    logger.info(f"馬HTMLのスクレイピングとマスターファイル更新を開始します。対象馬数: {len(horse_id_list)}")
    successful_horse_ids = scrape_html_horse(horse_id_list, skip=skip, sleep_range=sleep_range)
    if successful_horse_ids:
        update_horse_master_file(successful_horse_ids)
    return successful_horse_ids

def update_horse_master_file(horse_ids_to_update: list):
    """
    スクレイピングに成功した馬IDをマスターファイルに追記します。
    ファイルが存在しない場合はヘッダー付きで作成します。
    重複する馬IDがある場合は、最新の更新日時で上書きします。
    """
    master_file_path = LocalPaths.MASTER_RAW_HORSE_RESULTS_PATH
    try:
        # ファイルが存在しない場合はヘッダー付きで作成
        if not os.path.exists(master_file_path):
            df_master = pd.DataFrame(columns=['horse_id', 'updated_at'])
            df_master.to_csv(master_file_path, index=False, encoding='utf-8')
            logger.info(f"マスターファイルを作成しました: {master_file_path}")

        # 既存のマスターファイルを読み込む
        df_master = pd.read_csv(master_file_path, encoding='utf-8', dtype={'horse_id': str}) # horse_idを文字列として読み込む
        
        # 新しい馬IDと現在の日時を追加
        current_time = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        # horse_ids_to_update の要素も文字列に変換
        new_data = [{'horse_id': str(hid), 'updated_at': current_time} for hid in horse_ids_to_update]
        df_new = pd.DataFrame(new_data)
        
        # 既存データと新しいデータを結合し、重複を削除（horse_idで最新のものを残す）
        # horse_idの型を文字列に統一してから結合
        df_master['horse_id'] = df_master['horse_id'].astype(str)
        df_new['horse_id'] = df_new['horse_id'].astype(str)
        
        df_combined = pd.concat([df_master, df_new]).drop_duplicates(subset=['horse_id'], keep='last')
        
        # マスターファイルを更新
        df_combined.to_csv(master_file_path, index=False, encoding='utf-8')
        logger.info(f"マスターファイルを更新しました。追加/更新された馬ID数: {len(horse_ids_to_update)}")
    except Exception as e:
        logger.error(f"マスターファイルの更新中にエラーが発生しました: {e}", exc_info=True)


import argparse
import json # IDリストの保存/読み込み用

# --- main関数内の処理をアクションごとに分割 ---
def _handle_dates_action(args):
    if not (args.from_date and args.to_date):
        logger.error("--from-date と --to-date は dates アクションに必須です。")
        return
    race_dates = scrape_netkeiba_race_dates(args.from_date, args.to_date)
    if race_dates and args.output_file:
        try:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                json.dump(race_dates, f, indent=2, ensure_ascii=False)
            logger.info(f"開催日リストを {args.output_file} に保存しました。")
        except IOError as e:
            logger.error(f"ファイルへの書き込みに失敗しました: {args.output_file}, エラー: {e}")
    elif race_dates:
        logger.info(f"取得した開催日: {race_dates}")

def _load_id_list_from_file(id_file_path: str) -> list:
    """JSONファイルからIDリストを読み込む"""
    try:
        with open(id_file_path, 'r', encoding='utf-8') as f:
            id_list = json.load(f)
        if not isinstance(id_list, list):
            raise ValueError("IDファイルはJSON形式のリストである必要があります。")
        logger.info(f"{id_file_path} からIDリストを読み込みました。対象数: {len(id_list)}")
        return id_list
    except FileNotFoundError:
        logger.error(f"IDファイルが見つかりません: {id_file_path}")
    except json.JSONDecodeError as e:
        logger.error(f"IDファイルのJSON形式が不正です: {id_file_path}, エラー: {e}")
    except ValueError as e:
        logger.error(f"IDファイルの形式エラー: {e}")
    except IOError as e:
        logger.error(f"IDファイルの読み込みに失敗しました: {id_file_path}, エラー: {e}")
    return []

def _handle_race_ids_action(args, sleep_range):
    race_dates_for_ids = []
    if args.id_file:
        race_dates_for_ids = _load_id_list_from_file(args.id_file)
        if not race_dates_for_ids: return
    elif args.from_date and args.to_date:
        logger.info("開催日リストが指定されていないため、まず開催日を取得します。")
        race_dates_for_ids = scrape_netkeiba_race_dates(args.from_date, args.to_date)
        if not race_dates_for_ids:
            logger.warning("レースID取得のための開催日が見つかりませんでした。")
            return
    else:
        logger.error("--id-file (開催日リスト) または (--from-date と --to-date) は race_ids アクションに必須です。")
        return

    race_ids = scrape_netkeiba_race_ids(race_dates_for_ids, sleep_range=sleep_range, debug=args.debug)
    if race_ids and args.output_file:
        try:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                json.dump(race_ids, f, indent=2, ensure_ascii=False)
            logger.info(f"レースIDリストを {args.output_file} に保存しました。")
        except IOError as e:
            logger.error(f"ファイルへの書き込みに失敗しました: {args.output_file}, エラー: {e}")
    elif race_ids:
        logger.info(f"取得したレースID数: {len(race_ids)}")

def _handle_html_scraping_action(args, sleep_range):
    if not args.id_file:
        logger.error(f"--id-file は {args.action} アクションに必須です。")
        return
    id_list_to_scrape = _load_id_list_from_file(args.id_file)
    if not id_list_to_scrape:
        logger.info("処理対象のIDがありません。")
        return

    if args.action == "race_html":
        scrape_html_race(id_list_to_scrape, skip=not args.no_skip, sleep_range=sleep_range)
    elif args.action == "horse_html":
        scrape_html_horse(id_list_to_scrape, skip=not args.no_skip, sleep_range=sleep_range)
    elif args.action == "ped_html":
        scrape_html_ped(id_list_to_scrape, skip=not args.no_skip, sleep_range=sleep_range)
    elif args.action == "horse_master":
        scrape_html_horse_with_master(id_list_to_scrape, skip=not args.no_skip, sleep_range=sleep_range)

def _handle_all_races_action(args, sleep_range):
    if not (args.from_date and args.to_date):
        logger.error("--from-date と --to-date は all_races アクションに必須です。")
        return
    
    logger.info(f"ステップ1: 開催日取得 ({args.from_date} - {args.to_date})")
    race_dates = scrape_netkeiba_race_dates(args.from_date, args.to_date)
    if not race_dates:
        logger.warning("開催日が見つかりませんでした。処理を終了します。")
        return
    logger.info(f"取得した開催日数: {len(race_dates)}")
    if args.output_file:
        dates_output_file = args.output_file.replace(".json", "") + "_dates.json"
        try:
            with open(dates_output_file, 'w', encoding='utf-8') as f:
                json.dump(race_dates, f, indent=2, ensure_ascii=False)
            logger.info(f"開催日リストを {dates_output_file} に保存しました。")
        except IOError as e:
            logger.error(f"ファイルへの書き込みに失敗しました: {dates_output_file}, エラー: {e}")


    logger.info("ステップ2: レースID取得")
    race_ids = scrape_netkeiba_race_ids(race_dates, sleep_range=sleep_range, debug=args.debug)
    if not race_ids:
        logger.warning("レースIDが見つかりませんでした。処理を終了します。")
        return
    logger.info(f"取得したレースID数: {len(race_ids)}")
    if args.output_file:
        race_ids_output_file = args.output_file.replace(".json", "") + "_race_ids.json"
        try:
            with open(race_ids_output_file, 'w', encoding='utf-8') as f:
                json.dump(race_ids, f, indent=2, ensure_ascii=False)
            logger.info(f"レースIDリストを {race_ids_output_file} に保存しました。")
        except IOError as e:
            logger.error(f"ファイルへの書き込みに失敗しました: {race_ids_output_file}, エラー: {e}")

    logger.info("ステップ3: レースHTML取得")
    scrape_html_race(race_ids, skip=not args.no_skip, sleep_range=sleep_range)
    logger.info("all_races アクションが完了しました。")

def _handle_all_horses_action(args, sleep_range):
    if not args.id_file:
        logger.error(f"--id-file (馬IDリスト) は all_horses アクションに必須です。")
        return
    horse_id_list = _load_id_list_from_file(args.id_file)
    if not horse_id_list:
        logger.info("処理対象の馬IDがありません。")
        return

    logger.info("ステップ1: 馬HTML取得とマスター更新")
    scrape_html_horse_with_master(horse_id_list, skip=not args.no_skip, sleep_range=sleep_range)

    logger.info("ステップ2: 血統HTML取得")
    scrape_html_ped(horse_id_list, skip=not args.no_skip, sleep_range=sleep_range)
    logger.info("all_horses アクションが完了しました。")


def main():
    parser = argparse.ArgumentParser(description="netkeiba.comから競馬データをスクレイピングします。")
    parser.add_argument(
        "action",
        choices=["dates", "race_ids", "race_html", "horse_html", "ped_html", "horse_master", "all_races", "all_horses"],
        help="実行するスクレイピングアクション。\n"
             " dates: 開催日取得,\n"
             " race_ids: レースID取得,\n"
             " race_html: レースHTML取得,\n"
             " horse_html: 馬HTML取得,\n"
             " ped_html: 血統HTML取得,\n"
             " horse_master: 馬HTML取得とマスター更新,\n"
             " all_races: 開催日取得からレースHTML取得までを一括実行,\n"
             " all_horses: 指定された馬IDリストから馬HTMLと血統HTMLを取得しマスター更新までを一括実行"
    )
    parser.add_argument("--from-date", help="取得開始日 (YYYY-MM-DD or YYYY-MM)")
    parser.add_argument("--to-date", help="取得終了日 (YYYY-MM-DD or YYYY-MM)")
    parser.add_argument("--id-file", help="処理対象のIDが記載されたファイル (JSON形式のリスト)")
    parser.add_argument("--output-file", help="取得したIDリストを保存するファイル (JSON形式)")
    parser.add_argument("--no-skip", action="store_true", help="既存のHTMLファイルをスキップしない")
    parser.add_argument("--sleep-min", type=float, default=NORMAL_SLEEP_RANGE[0], help=f"リクエスト間の最小待機時間（秒）。デフォルト: {NORMAL_SLEEP_RANGE[0]}")
    parser.add_argument("--sleep-max", type=float, default=NORMAL_SLEEP_RANGE[1], help=f"リクエスト間の最大待機時間（秒）。デフォルト: {NORMAL_SLEEP_RANGE[1]}")
    parser.add_argument("--debug", action="store_true", help="デバッグログを有効にする")
    parser.add_argument("--log-file", default="scraping.log", help="ログファイル名。指定しない場合は scraping.log。空文字列でファイル出力を無効化。")


    args = parser.parse_args()

    setup_logger(debug_level=args.debug, log_file=args.log_file if args.log_file else None)


    sleep_range = (args.sleep_min, args.sleep_max)
    if sleep_range[0] < 0 or sleep_range[1] < 0 or sleep_range[0] > sleep_range[1]:
        logger.error("待機時間の設定が不正です。sleep_min >= 0, sleep_max >= 0, sleep_min <= sleep_max である必要があります。")
        return # 不正な場合は処理を終了

    logger.info(f"待機時間範囲: {sleep_range[0]} - {sleep_range[1]}秒")

    try:
        if args.action == "dates":
            _handle_dates_action(args)
        elif args.action == "race_ids":
            _handle_race_ids_action(args, sleep_range)
        elif args.action in ["race_html", "horse_html", "ped_html", "horse_master"]:
            _handle_html_scraping_action(args, sleep_range)
        elif args.action == "all_races":
            _handle_all_races_action(args, sleep_range)
        elif args.action == "all_horses":
            _handle_all_horses_action(args, sleep_range)

    except HTTPError as e:
        if e.code == 400:
            logger.critical(f"HTTP 400エラーにより処理が強制終了されました。IPブロックの可能性があります。しばらく待ってから再試行してください。 ({hasattr(e, 'url') and e.url or 'N/A'})")
        else:
            logger.critical(f"予期せぬHTTPエラーが発生しました: {e}")
    except Exception as e:
        logger.critical(f"メイン処理中に予期せぬエラーが発生しました: {e}", exc_info=True)

if __name__ == "__main__":
    main()
