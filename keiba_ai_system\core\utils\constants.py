# f:\keiba__AI_2025\scraping_constants.py
# --- スクレイピング関連の定数 ---
MAX_RETRIES = 3
REQUEST_TIMEOUT = 30  # 秒
# 通常アクセスの待機時間（秒）
NORMAL_SLEEP_RANGE = (3, 6)
# リトライ時の待機時間（秒）
RETRY_SLEEP_RANGE = (15, 20)

# WebDriverの暗黙的な待機時間のデフォルト値
DEFAULT_IMPLICIT_WAIT = 15
# WebDriverWaitのタイムアウト時間（秒）
WEBDRIVER_TIMEOUT = 30

# 一般的なUser-Agentのリスト
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
]

# User-Agent以外の共通ヘッダー
BASE_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Referer': 'https://race.netkeiba.com/top/', # 固定のリファラ
    'Connection': 'keep-alive',
    'Cache-Control': 'max-age=0',
    'DNT': '1' # Do Not Track
}
# スクレイピング対象のURL
# netkeibaのURLパス
class UrlPaths:
    BASE_URL = "https://race.netkeiba.com"
    CALENDAR_URL = f"{BASE_URL}/top/calendar.html"
    RACE_LIST_URL = f"{BASE_URL}/top/race_list.html"
    RACE_URL = f"{BASE_URL}/race/shutuba.html"
    RESULT_URL = f"{BASE_URL}/race/result.html"

    # DB系のURL
    DB_BASE_URL = "https://db.netkeiba.com"
    RACE_DB_URL = f"{DB_BASE_URL}/race/"
    HORSE_URL = f"{DB_BASE_URL}/horse/"
    PED_URL = f"{DB_BASE_URL}/horse/ped/"

# ローカルのパス設定
class LocalPaths:
    # データ保存用のディレクトリ
    DATA_DIR = "data/"
    HTML_DIR = f"{DATA_DIR}/html"

    # レース情報のHTML保存先
    HTML_RACE_DIR = f"{HTML_DIR}/race/race_by_year"

    # 馬情報のHTML保存先
    HTML_HORSE_DIR = f"{HTML_DIR}/horse/horse_by_year"

    # 血統情報のHTML保存先
    HTML_PED_DIR = f"{HTML_DIR}/pedigree/pedigree_by_year"

    # マスターデータ保存先
    MASTER_DIR = f"{DATA_DIR}/master"
    MASTER_RAW_HORSE_RESULTS_PATH = f"{MASTER_DIR}/horse_results_updated_at.csv"

# --- レース情報・結果テーブルの列名定数 ---
import dataclasses

# from typing import List # Optional は ComprehensiveIntegratorConfig では未使用
from typing import Dict, List, Any, Optional # Optional を追加
import os
import datetime

@dataclasses.dataclass(frozen=True) # ResultsCols にも dataclass を適用 (元々そうだったらすみません)
class ResultsCols: # Pydantic BaseModel の継承は元々ない
    """
    レース結果テーブルの列名を定数として持つ
    """
    RANK: str = '着順'
    WAKUBAN: str = '枠番'
    UMABAN: str = '馬番'
    HORSE_NAME: str = '馬名'
    SEX_AGE: str = '性齢'
    KINRYO: str = '斤量'
    JOCKEY: str = '騎手'
    TIME: str = 'タイム'
    RANK_DIFF: str = '着差'
    TANSHO_ODDS: str = '単勝' # netkeibaの表記に合わせる
    POPULARITY: str = '人気'
    WEIGHT_AND_DIFF: str = '馬体重' # netkeibaの表記に合わせる
    TRAINER: str = '調教師'
    HORSE_ID: str = 'horse_id' # 内部で使用するキー
    JOCKEY_ID: str = 'jockey_id' # 内部で使用するキー
    TRAINER_ID: str = 'trainer_id' # 内部で使用するキー

@dataclasses.dataclass(frozen=True) # RaceInfoCols にも dataclass を適用
class RaceInfoCols: # Pydantic BaseModel の継承は元々ない
    """
    レース情報テーブルの列名を定数として持つ
    """
    RACE_ID: str = 'race_id' # 内部で使用するキー
    DATE: str = 'date'
    VENUE: str = '開催' # netkeibaの表記に合わせる
    WEATHER: str = 'weather'
    RACE_TYPE: str = 'race_type'
    GROUND_STATE: str = 'ground_state'
    DISTANCE: str = 'course_len' # 内部で使用するキー (コース長)
    AROUND: str = 'around' # '回り' とどちらが良いか検討
    RACE_CLASS: str = 'race_class'
    RACE_NAME: str = 'レース名'
    
@dataclasses.dataclass(frozen=True) # Master にも dataclass を適用
class Master: # Pydantic BaseModel の継承は元々ない
    """
    マスターデータを定数として持つ
    """
    WEATHER_LIST: tuple = ('晴', '曇', '小雨', '雨', '小雪', '雪')
    GROUND_STATE_LIST: tuple = ('良', '稍重', '重', '不良')
    SEX_LIST: tuple = ('牡', '牝', 'セ') # '牡'、'牝'、'セ'（せん馬）
    AROUND_LIST: tuple = ('右', '左', '直線', '障害') # レースの回り方
    RACE_CLASS_LIST: tuple = ('新馬', '未勝利', '1勝クラス', '2勝クラス', '3勝クラス', 'オープン', 'G3', 'G2', 'G1', '障害') # レースクラス
    RACE_TYPE_DICT: dict = dataclasses.field(default_factory=lambda: {'芝': '芝', 'ダ': 'ダート', '障': '障害'}) # レース種別辞書

@dataclasses.dataclass(frozen=True) # DataMergerConstants にも dataclass を適用
class DataMergerConstants: # Pydantic BaseModel の継承は元々ない
    """
    DataMergerクラスで使用する定数
    """
    DEFAULT_TARGET_COLS: tuple = ('着順', '人気', '単勝')
    DEFAULT_GROUP_COLS: tuple = ('venue', 'race_type', 'distance')

@dataclasses.dataclass(frozen=True) # BaseModelの継承を削除
class ComprehensiveIntegratorConfig:
    """
    ComprehensiveDataIntegratorクラスの設定値を定義する dataclass
    """
    max_horse_info_files_no_target: int = 1000
    max_horse_info_files_fallback: int = 1000
    max_years_to_search_target_horse: int = 3
    target_horse_file_found_ratio_threshold: float = 0.3
    max_horse_results_files_no_target: int = 1000
    include_race_info: bool = True
    include_horse_info: bool = True
    include_past_performance: bool = True
    performance_window_races: List[int] = dataclasses.field(default_factory=lambda: [5, 10])
    parallel: bool = True
    max_workers: int = os.cpu_count() or 1
    save_output: bool = False
    race_id: Optional[str] = None # 特定のレースIDを処理する場合
    filename_prefix: str = "comprehensive_data"
    default_year: Optional[str] = str(datetime.datetime.now().year) # Optional に変更
    default_years: Optional[List[str]] = dataclasses.field(default_factory=lambda: [str(datetime.datetime.now().year)]) # Optional に変更

    # Pickle読み込みに関する設定
    use_pickle_source: bool = False
    pickle_base_dir: Optional[str] = None

    # RaceProcessorが使用するpickleファイル名のテンプレート
    race_info_filename_template: str = "race_info_{year}.pickle"
    race_results_filename_template: str = "race_results_{year}.pickle"
    
    # Pickleソース用のファイル名テンプレート設定
    horse_results_pickle_filename_template: str = "horse_results_{year}.pickle"
    horse_info_pickle_filename_template: str = "horse_info_{year}.pickle"

    # 新しいpickleソース用の設定 (例: カスタム特徴量)
    enable_custom_feature_pickle: bool = False
    custom_feature_pickle_filename_template: str = "custom_features_{year}.pickle"

    def __post_init__(self):
        if not all(isinstance(w, int) and w > 0 for w in self.performance_window_races):
            raise ValueError("performance_window_racesは正の整数のリストである必要があります")

import re # re をインポート

class RaceProcessorConstants:
    """
    RaceProcessorクラスで使用する定数
    """
    # レース名セレクタ
    RACE_NAME_SELECTORS: tuple = (".RaceName", ".data_intro h1", ".mainrace_data h1")
    # レースデータセレクタ（バックアップ用）
    RACE_DATA_SELECTORS: tuple = (".RaceData01", ".racedata span", ".diary_snap_cut span")

    # RaceProcessor で使用する正規表現
    _RE_HORSE_ID = re.compile(r"/horse/(\d+)")
    _RE_JOCKEY_ID = re.compile(r"/jockey/result/recent/(\w+)/")
    _RE_TRAINER_ID = re.compile(r"/trainer/result/recent/(\w+)/")
    _RE_DATE_IN_DETAILS = re.compile(r"(\d{4})年(\d{1,2})月(\d{1,2})日")
    _RE_VENUE_IN_DETAILS = re.compile(r"([^\s]+)\s*(?:\d+回|\d+日)") # 開催場所 (例: 東京, 阪神)
    _RE_WEATHER_IN_DETAILS = re.compile(r"天候:([^\s]+)")
    _RE_TRACK_CONDITION_IN_DETAILS = re.compile(r"馬場:([^\s]+)")
    _RE_TABLE_SUMMARY = re.compile("レース結果|出馬表")

@dataclasses.dataclass(frozen=True) # HorseResultsCols にも dataclass を適用
class HorseResultsCols:
    """
    馬の過去成績テーブルの列名を定数として持つ
    """
    DATE: str = '日付'
    PLACE: str = '開催'
    WEATHER: str = '天気' # RaceInfoCols.WEATHER と同じ 'weather' に統一するか検討
    R: str = 'R'
    RACE_NAME: str = 'レース名'
    N_HORSES: str = '頭数'
    WAKUBAN: str = '枠番'
    UMABAN: str = '馬番'
    TANSHO_ODDS: str = 'オッズ' # ResultsCols.TANSHO_ODDS と同じ '単勝' に統一するか検討
    POPULARITY: str = '人気'
    RANK: str = '着順'
    JOCKEY: str = '騎手'
    KINRYO: str = '斤量'
    RACE_TYPE_COURSE_LEN: str = '距離'
    GROUND_STATE: str = '馬場'
    TIME: str = 'タイム'
    RANK_DIFF: str = '着差'
    CORNER: str = '通過'
    PACE: str = 'ペース'
    NOBORI: str = '上り'
    WEIGHT_AND_DIFF: str = '馬体重'
    PRIZE: str = '賞金'
    SEX_AGE: str = '性齢'


@dataclasses.dataclass(frozen=True) # HorseInfoCols にも dataclass を適用
class HorseInfoCols: # Pydantic BaseModel の継承は元々ない
    """
    馬の基本情報テーブルの列名を定数として持つ
    """
    HORSE_NAME: str = 'horse_name'
    BIRTHDAY: str = '生年月日'
    TRAINER: str = '調教師'
    OWNER: str = '馬主'
    REC_INFO: str = '募集情報'
    BREEDER: str = '生産者'
    ORIGIN: str = '産地'
    PRICE: str = 'セリ取引価格'
    WINNING_PRIZE: str = '獲得賞金'
    TOTAL_RESULTS: str = '通算成績'
    VICTORY_RACE: str = '主な勝鞍'
    # 血統情報
    FATHER_NAME: str = 'father_name'
    MOTHER_NAME: str = 'mother_name'
    MOTHER_FATHER_NAME: str = 'mother_father_name'  # 母父名
    FATHER_ID: str = 'father_id'
    MOTHER_ID: str = 'mother_id'
    MOTHER_FATHER_ID: str = 'mother_father_id'  # 母父ID
    SIBLING_IDS: str = 'sibling_ids'  # 近親馬のIDリスト
    RELATIVE_HORSE: str = '近親馬'
