# 実際の競馬レース予測システム

このシステムは、実際の競馬レースをスクレイピングして、データフレームに処理し、予測まで行う包括的なツールです。

## ファイル構成

- `live_race_predictor.py` - メインの予測システム
- `live_race_prediction_example.ipynb` - 使用例を示すJupyterノートブック
- `LIVE_RACE_PREDICTOR_README.md` - このファイル

## 主な機能

1. **レースデータのスクレイピング**
   - 指定されたレースIDの出馬表を自動取得
   - 出走馬の過去成績データを収集

2. **データ処理**
   - RaceProcessorとHorseProcessorを使用した高度なデータ処理
   - 特徴量エンジニアリング（過去成績統計、血統情報など）

3. **予測**
   - LightGBMモデルによる着順予測
   - 勝率スコアの計算

4. **結果出力**
   - 予測結果の見やすい表示
   - CSVファイルへの自動保存

## 使用方法

### 1. コマンドライン実行

```bash
# 基本的な使用方法
python live_race_predictor.py 202406010101

# 学習済みモデルを指定
python live_race_predictor.py 202406010101 --model model.pkl --scaler scaler.pkl

# 結果を保存しない場合
python live_race_predictor.py 202406010101 --no-save
```

### 2. Pythonスクリプトでの使用

```python
from keiba_ai_system.prediction.live_predictor import LiveRacePredictor

# 予測システムを初期化
predictor = LiveRacePredictor(
    model_path="path/to/model.pkl",
    scaler_path="path/to/scaler.pkl"
)

# 予測実行
results_df = predictor.predict_race_complete(
    race_id="202406010101",
    skip_existing=True,
    save_results=True
)

# 結果表示
if not results_df.empty:
    print("予測完了")
    print(results_df[['馬名', '予測順位', '勝率予測']].head())
```

### 3. Jupyterノートブックでの使用

`live_race_prediction_example.ipynb`を参照してください。ステップバイステップでの実行例が含まれています。

## 処理フロー

1. **レースHTMLスクレイピング** - netkeiba.comから出馬表を取得
2. **馬IDの抽出** - 出走馬のIDリストを生成
3. **馬データスクレイピング** - 各馬の詳細情報と過去成績を取得
4. **データ処理** - HTMLをDataFrameに変換
5. **特徴量作成** - 予測に必要な特徴量を生成
6. **予測実行** - 学習済みモデルで着順を予測
7. **結果出力** - 予測結果を表示・保存

## 必要な依存関係

- pandas
- numpy
- lightgbm
- scikit-learn
- joblib
- beautifulsoup4
- その他、プロジェクトの既存モジュール

## 設定

### レースID形式

レースIDは以下の形式で指定してください：
- 例: `202406010101`
- 形式: `YYYYMMDDRRNN`
  - YYYY: 年
  - MM: 月
  - DD: 日
  - RR: 競馬場コード
  - NN: レース番号

### モデルファイル

学習済みモデルは以下の形式で保存されている必要があります：
- LightGBMモデル: `.pkl`形式（joblibで保存）
- StandardScaler: `.pkl`形式（joblibで保存）

## 出力ファイル

### 予測結果CSV

`predictions/`ディレクトリに以下の形式で保存されます：
- ファイル名: `prediction_{race_id}_{timestamp}.csv`
- 内容: 馬名、馬番、予測順位、勝率予測など

### ログファイル

`live_race_predictor.log`に処理ログが記録されます。

## 注意事項

1. **スクレイピングの制限**
   - 過度なアクセスは避けてください
   - `skip_existing=True`で既存ファイルの再取得を防げます

2. **モデルの互換性**
   - 学習時と同じ特徴量セットが必要です
   - モデルがない場合はデータ取得のみ実行されます

3. **データの品質**
   - 古いレースや特殊なレースでは正常に動作しない場合があります
   - エラーログを確認して問題を特定してください

4. **免責事項**
   - 予測結果は参考程度に留めてください
   - 実際の投資判断には使用しないでください

## トラブルシューティング

### よくあるエラー

1. **HTMLファイルが見つからない**
   - レースIDが正しいか確認
   - スクレイピングが正常に完了したか確認

2. **馬データが取得できない**
   - ネットワーク接続を確認
   - レースが古すぎる可能性

3. **予測に失敗**
   - モデルファイルのパスを確認
   - 特徴量の互換性を確認

### ログの確認

詳細なエラー情報は`live_race_predictor.log`ファイルで確認できます。

## 拡張可能性

このシステムは以下のように拡張できます：

1. **新しい特徴量の追加**
   - `create_features`メソッドを修正
   - 血統データ、調教データなどの追加

2. **異なるモデルの対応**
   - `predict_race`メソッドを修正
   - TensorFlow、PyTorchモデルなどの対応

3. **リアルタイム予測**
   - 定期実行スクリプトの作成
   - Webアプリケーションとの連携

## ライセンス

このプロジェクトのライセンスに従います。
