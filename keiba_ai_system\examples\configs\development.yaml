# 開発環境用設定ファイル

# データベース設定
database:
  host: "localhost"
  port: 5432
  name: "keiba_development"
  user: "dev_user"
  password: "dev_password"

# スクレイピング設定
scraping:
  delay_seconds: 1.0  # 開発環境では短い間隔
  max_retries: 2
  timeout_seconds: 15
  user_agent: "KeibaAI-Dev/1.0"
  
  # 並列処理（開発環境では控えめ）
  parallel:
    enabled: true
    max_workers: 2

# データ処理設定
processing:
  # バッチサイズ（開発環境では小さく）
  batch_size: 100
  
  # 並列処理
  parallel:
    enabled: true
    max_workers: 2
  
  # データ品質チェック（緩い設定）
  quality_check:
    enabled: true
    min_data_points: 5
    outlier_threshold: 4.0

# 特徴量エンジニアリング設定
features:
  # 開発用に一部の特徴量グループのみ有効
  enabled_groups:
    - basic
    - performance
    - race_condition
  
  # 過去成績設定（開発用に少なく）
  performance:
    lookback_races: 5
    min_races: 2
    weight_decay: 0.8
  
  # キャッシュ設定（開発用に小さく）
  cache:
    enabled: true
    max_size_mb: 100
    ttl_hours: 6

# 予測設定
prediction:
  # モデル設定
  models:
    lightgbm:
      enabled: true
      model_path: "models/lightgbm_dev.pkl"
      scaler_path: "models/scaler_dev.pkl"
    
    tensorflow:
      enabled: false  # 開発環境では無効
  
  # 予測結果設定
  output:
    save_predictions: true
    output_dir: "dev_predictions"
    include_probabilities: true

# ログ設定（詳細なデバッグ情報）
logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  
  # ファイル出力
  file:
    enabled: true
    path: "logs/keiba_ai_development.log"
    max_size_mb: 50
    backup_count: 3

# 開発専用設定
development:
  # デバッグモード
  debug_mode: true
  
  # テストデータ使用
  use_test_data: true
  test_data_size: 1000
  
  # 自動リロード
  auto_reload: true
