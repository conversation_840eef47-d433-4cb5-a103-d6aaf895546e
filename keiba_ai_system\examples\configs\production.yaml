# 本番環境用設定ファイル

# データベース設定
database:
  host: "localhost"
  port: 5432
  name: "keiba_production"
  user: "keiba_user"
  password: "${DB_PASSWORD}"  # 環境変数から取得

# スクレイピング設定
scraping:
  delay_seconds: 2.0  # リクエスト間隔
  max_retries: 3
  timeout_seconds: 30
  user_agent: "KeibaAI/1.0"
  
  # 並列処理
  parallel:
    enabled: true
    max_workers: 4

# データ処理設定
processing:
  # バッチサイズ
  batch_size: 1000
  
  # 並列処理
  parallel:
    enabled: true
    max_workers: 8
  
  # データ品質チェック
  quality_check:
    enabled: true
    min_data_points: 10
    outlier_threshold: 3.0

# 特徴量エンジニアリング設定
features:
  # 有効な特徴量グループ
  enabled_groups:
    - basic
    - performance
    - pedigree
    - jockey
    - trainer
    - race_condition
  
  # 過去成績設定
  performance:
    lookback_races: 10
    min_races: 3
    weight_decay: 0.9
  
  # キャッシュ設定
  cache:
    enabled: true
    max_size_mb: 1000
    ttl_hours: 24

# 予測設定
prediction:
  # モデル設定
  models:
    lightgbm:
      enabled: true
      model_path: "models/lightgbm_production.pkl"
      scaler_path: "models/scaler_production.pkl"
    
    tensorflow:
      enabled: false
      model_path: "models/tensorflow_ranking.h5"
  
  # 予測結果設定
  output:
    save_predictions: true
    output_dir: "predictions"
    include_probabilities: true

# ログ設定
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # ファイル出力
  file:
    enabled: true
    path: "logs/keiba_ai_production.log"
    max_size_mb: 100
    backup_count: 10
