{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 特徴量エンジニアリング管理システム使用例\n", "\n", "このノートブックでは、新しい特徴量エンジニアリング管理システムの使用方法を示します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# プロジェクトのルートディレクトリをパスに追加\n", "sys.path.append('.')\n", "\n", "from keiba_ai_system.core.features.manager import FeatureEngineeringManager\n", "from keiba_ai_system.core.features.definitions import FeatureDefinition, FeatureCategory, FeatureType\n", "from keiba_ai_system.prediction.live_predictor import LiveRacePredictor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 特徴量エンジニアリング管理システムの初期化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量管理システムを初期化\n", "feature_manager = FeatureEngineeringManager(config_path=\"feature_config.yaml\")\n", "\n", "print(\"特徴量エンジニアリング管理システムを初期化しました\")\n", "print(f\"設定ファイル: {feature_manager.config_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 登録されている特徴量の確認"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量の要約情報を表示\n", "summary = feature_manager.get_feature_summary()\n", "print(\"=== 特徴量要約 ===\")\n", "for key, value in summary.items():\n", "    print(f\"{key}: {value}\")\n", "\n", "print(\"\\n=== カテゴリ別特徴量数 ===\")\n", "for category, count in summary['category_counts'].items():\n", "    if count > 0:\n", "        print(f\"{category}: {count}個\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 有効な特徴量のリストを表示\n", "enabled_features = feature_manager.list_features(enabled_only=True)\n", "print(f\"有効な特徴量（{len(enabled_features)}個）:\")\n", "for i, feature_name in enumerate(enabled_features, 1):\n", "    print(f\"{i:2d}. {feature_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. サンプルデータでの特徴量計算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# サンプルデータを作成\n", "sample_data = pd.DataFrame({\n", "    'horse_id': ['horse_001', 'horse_002', 'horse_003'],\n", "    '馬番': [1, 2, 3],\n", "    '枠番': [1, 1, 2],\n", "    '斤量': [55.0, 56.0, 54.0],\n", "    '人気': [1, 3, 2],\n", "    '性齢': ['牡4', '牝3', 'セ5'],\n", "    'jockey_id': ['jockey_001', 'jockey_002', 'jockey_003'],\n", "    'trainer_id': ['trainer_001', 'trainer_002', 'trainer_003'],\n", "    'father_id': ['father_001', 'father_002', 'father_003'],\n", "    'mother_father_id': ['mf_001', 'mf_002', 'mf_003'],\n", "    '距離': [1600, 2000, 1200],\n", "    '芝・ダート': ['芝', 'ダート', '芝'],\n", "    '開催': ['東京', '中山', '京都']\n", "})\n", "\n", "print(\"サンプルデータ:\")\n", "display(sample_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 過去成績データのサンプル\n", "sample_horse_results = pd.DataFrame({\n", "    'horse_id': ['horse_001', 'horse_001', 'horse_002', 'horse_002', 'horse_003'],\n", "    '着順': [1, 3, 2, 1, 4],\n", "    '賞金': [1000, 200, 500, 800, 100],\n", "    '人気': [2, 4, 1, 2, 5],\n", "    '日付': ['2024-01-01', '2024-02-01', '2024-01-15', '2024-02-15', '2024-01-20']\n", "})\n", "\n", "print(\"過去成績サンプルデータ:\")\n", "display(sample_horse_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基本特徴量のみを計算\n", "basic_features = feature_manager.list_features(category=FeatureCategory.BASIC, enabled_only=True)\n", "print(f\"計算する基本特徴量: {basic_features}\")\n", "\n", "# 特徴量を計算\n", "result_basic = feature_manager.calculate_features(\n", "    data=sample_data,\n", "    feature_names=basic_features\n", ")\n", "\n", "print(\"\\n基本特徴量計算結果:\")\n", "display(result_basic[['horse_id', '馬番', '枠番', '性別_数値', '年齢']].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 過去成績特徴量を計算\n", "performance_features = feature_manager.list_features(category=FeatureCategory.PERFORMANCE, enabled_only=True)\n", "print(f\"計算する過去成績特徴量: {performance_features}\")\n", "\n", "# 依存データを準備\n", "dependencies = {\n", "    'horse_results_df': sample_horse_results\n", "}\n", "\n", "# 特徴量を計算\n", "result_performance = feature_manager.calculate_features(\n", "    data=sample_data,\n", "    feature_names=performance_features,\n", "    dependencies=dependencies\n", ")\n", "\n", "print(\"\\n過去成績特徴量計算結果:\")\n", "performance_cols = ['horse_id', '出走回数', '平均着順', '勝率', '連対率', '複勝率']\n", "available_cols = [col for col in performance_cols if col in result_performance.columns]\n", "display(result_performance[available_cols].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 全特徴量の一括計算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 全ての有効な特徴量を計算\n", "all_features_result = feature_manager.calculate_features(\n", "    data=sample_data,\n", "    dependencies=dependencies\n", ")\n", "\n", "print(f\"計算された特徴量数: {all_features_result.shape[1]}\")\n", "print(f\"元データのカラム数: {sample_data.shape[1]}\")\n", "print(f\"追加された特徴量数: {all_features_result.shape[1] - sample_data.shape[1]}\")\n", "\n", "print(\"\\n計算された全特徴量:\")\n", "print(list(all_features_result.columns))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. カスタム特徴量の追加"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# カスタム特徴量計算関数を定義\n", "def calculate_weight_popularity_score(data, **kwargs):\n", "    \"\"\"斤量と人気を組み合わせたスコアを計算\"\"\"\n", "    if '斤量' in data.columns and '人気' in data.columns:\n", "        # 斤量が重く、人気が高い（数値が小さい）ほど高スコア\n", "        return (data['斤量'] - 50) * (10 - data['人気'])\n", "    else:\n", "        return pd.Series(index=data.index, dtype=float)\n", "\n", "# カスタム特徴量を定義\n", "custom_feature = FeatureDefinition(\n", "    name='斤量人気スコア',\n", "    category=FeatureCategory.CUSTOM,\n", "    feature_type=FeatureType.NUMERICAL,\n", "    description='斤量と人気を組み合わせたカスタムスコア',\n", "    required_columns=['斤量', '人気'],\n", "    calculator=calculate_weight_popularity_score\n", ")\n", "\n", "# カスタム特徴量を追加\n", "feature_manager.add_custom_feature(custom_feature)\n", "\n", "# カスタム特徴量を計算\n", "custom_result = feature_manager.calculate_features(\n", "    data=sample_data,\n", "    feature_names=['斤量人気スコア']\n", ")\n", "\n", "print(\"カスタム特徴量計算結果:\")\n", "display(custom_result[['horse_id', '斤量', '人気', '斤量人気スコア']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 特徴量の管理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量の有効/無効切り替え\n", "print(\"高度な特徴量を有効化...\")\n", "feature_manager.config['feature_groups']['advanced']['enabled'] = True\n", "\n", "# 設定を再読み込み\n", "feature_manager._initialize_feature_definitions()\n", "\n", "# 更新後の要約を表示\n", "updated_summary = feature_manager.get_feature_summary()\n", "print(f\"\\n更新後の有効特徴量数: {updated_summary['enabled_features']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量重要度ランキングを表示\n", "importance_ranking = feature_manager.get_feature_importance_ranking()\n", "\n", "print(\"特徴量重要度ランキング:\")\n", "display(importance_ranking[['feature_name', 'category', 'type', 'importance_level', 'enabled']].head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 実際のレースデータでの使用例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LiveRacePredictorと組み合わせた使用例\n", "predictor = LiveRacePredictor()\n", "\n", "# 実際のレースIDを指定（例）\n", "race_id = \"202406010101\"  # 実際のレースIDに置き換えてください\n", "\n", "print(f\"レースID {race_id} のデータを処理中...\")\n", "\n", "# レースデータを取得（エラーが発生する可能性があります）\n", "try:\n", "    # レースデータを処理\n", "    race_info_df, race_results_df, race_name = predictor.process_race_data(race_id)\n", "    \n", "    if not race_results_df.empty:\n", "        print(f\"レース名: {race_name}\")\n", "        print(f\"出走頭数: {len(race_results_df)}\")\n", "        \n", "        # 特徴量エンジニアリングを適用\n", "        enhanced_features = feature_manager.calculate_features(\n", "            data=race_results_df,\n", "            dependencies={'horse_results_df': pd.DataFrame()}  # 実際の過去成績データを使用\n", "        )\n", "        \n", "        print(f\"\\n特徴量追加後のカラム数: {enhanced_features.shape[1]}\")\n", "        print(\"追加された特徴量の例:\")\n", "        new_columns = [col for col in enhanced_features.columns if col not in race_results_df.columns]\n", "        print(new_columns[:10])  # 最初の10個を表示\n", "        \n", "    else:\n", "        print(\"レースデータが取得できませんでした\")\n", "        \n", "except Exception as e:\n", "    print(f\"エラーが発生しました: {e}\")\n", "    print(\"サンプルデータでの動作確認は上記のセルで完了しています\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 特徴量情報の保存と読み込み"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量情報をファイルに保存\n", "feature_manager.save_features_info('features_info.yaml')\n", "\n", "# 設定ファイルも保存\n", "feature_manager.save_config()\n", "\n", "print(\"特徴量情報と設定を保存しました\")\n", "print(\"- features_info.yaml: 特徴量の詳細情報\")\n", "print(\"- feature_config.yaml: 設定ファイル\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. ま<PERSON><PERSON>\n", "\n", "この特徴量エンジニアリング管理システムの主な機能:\n", "\n", "1. **体系的な特徴量管理**: カテゴリ別に特徴量を整理\n", "2. **柔軟な設定**: YAML設定ファイルで簡単にカスタマイズ\n", "3. **自動計算**: 依存関係を考慮した特徴量の自動計算\n", "4. **バリデーション**: データ品質チェック機能\n", "5. **拡張性**: カスタム特徴量の簡単な追加\n", "6. **統合性**: 既存のLiveRacePredictorとの連携\n", "\n", "### 使用上の利点\n", "\n", "- 特徴量の追加・削除が簡単\n", "- 実験的特徴量の管理が容易\n", "- コードの再利用性が向上\n", "- 特徴量の品質管理が自動化\n", "- チーム開発での一貫性確保"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}