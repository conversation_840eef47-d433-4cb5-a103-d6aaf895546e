# 競馬予測プログラム設計図

このドキュメントは、競馬の実際のレースのスクレイピングから予測までを行うPythonプログラムの設計図を記述します。

## 全体フロー図

```mermaid
graph TD
    A[開始] --> B[データ収集 (スクレイピング)]
    B --> D[生データ (HTML/JSON)]
    D --> E[データ前処理]
    E --> F[前処理済みデータ (DataFrame)]
    F --> G[予測実行]
    G --> H[予測結果 (DataFrame)]
    H --> I[コンソール表示]
    H --> J[CSVファイル出力]
    J --> K[終了]
    I --> K

    subgraph モデル管理
        L[過去レースデータ] --> M[モデル学習・再学習]
        M --> N[学習済みモデル & 関連オブジェクト]
        N --> G
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
    style I fill:#ccf,stroke:#333,stroke-width:2px
    style J fill:#ccf,stroke:#333,stroke-width:2px
    style K fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#ccf,stroke:#333,stroke-width:2px
    style M fill:#bbf,stroke:#333,stroke-width:2px
    style N fill:#ccf,stroke:#333,stroke-width:2px
```

## 各コンポーネントの詳細

### 1. データ収集 (スクレイピング)

*   **担当モジュール**: [`module/refactored_scrap.py`](module/refactored_scrap.py)
*   **機能**:
    *   レース開催日の特定と、その日のレースIDの取得。
    *   各レースIDに対応する出馬表HTMLのスクレイピング。
    *   出走馬の過去成績、血統情報などの関連HTMLのスクレイピング（予測に必要な情報に応じて）。
*   **実行頻度**: 毎日。自動実行のために、OSのスケジューラ（例: Windows Task Scheduler, cron）と連携することを想定します。
*   **出力**: 生のHTMLファイル、またはパース済みのDataFrame（一時ファイルとして保存）。

### 2. データ前処理

*   **担当モジュール**: [`module/race_data_processor.py`](module/race_data_processor.py), [`module/horse_processor.py`](module/horse_processor.py), [`module/data_merger.py`](module/data_merger.py), [`module/comprehensive_data_integrator.py`](module/comprehensive_data_integrator.py) (既存モジュールを統合・拡張)
*   **機能**:
    *   スクレイピングしたHTMLデータから必要な情報を抽出し、Pandas DataFrameにパース。
    *   複数のデータソース（レース情報、馬情報、騎手情報、血統情報など）を結合し、単一のデータセットを作成。
    *   特徴量エンジニアリング: 馬の過去成績からの統計量計算、血統データの数値化、レース条件の数値化など。
    *   欠損値の補完、カテゴリカル変数のエンコーディング、数値特徴量のスケーリング。
*   **出力**: 予測モデルが直接入力として使用できる形式のDataFrame。

### 3. モデル学習・再学習

*   **担当モジュール**: 新規作成または既存の [`race_predictor.py`](race_predictor.py) を拡張。[`module/training`](module/training) ディレクトリ内の既存モジュールも参考にします。
*   **機能**:
    *   過去のレース結果データ（学習データ）をロード。
    *   データセットを訓練用と検証用に分割。
    *   機械学習モデル（例: LightGBM, RandomForest, Neural Network）の訓練。
    *   訓練済みモデル、スケーラー、特徴量リストなどの関連オブジェクトを保存（`ranking_model/` ディレクトリに保存）。
*   **実行頻度**: 月に一度。
*   **出力**: 学習済みモデルファイル、スケーラーオブジェクト、特徴量情報ファイル。

### 4. 予測実行

*   **担当モジュール**: [`race_predictor.py`](race_predictor.py)
*   **機能**:
    *   最新の出馬表データ（前処理済み）をロード。
    *   学習済みモデルと関連オブジェクトをロード。
    *   前処理済みデータに対して予測を実行し、各出走馬の勝率や着順スコアなどを算出。
*   **実行頻度**: 毎日、データ収集と前処理の後に実行。
*   **出力**: 各出走馬の予測スコアを含むDataFrame。

### 5. 結果出力

*   **担当モジュール**: [`race_predictor.py`](race_predictor.py)
*   **機能**:
    *   予測結果を整形し、コンソールに分かりやすく表示。
    *   予測結果をCSVファイルとして指定されたパスに保存。
*   **出力形式**:
    *   **コンソール**: レース名、出走馬名、予測スコア、推奨馬など。
    *   **CSV**: レースID、馬ID、予測スコア、その他の関連情報。

## 実装における考慮事項

*   **エラーハンドリング**: スクレイピング時のネットワークエラー、HTML構造の変更、データ処理時のエラーなど、各段階での堅牢なエラーハンドリングを実装します。
*   **ロギング**: 各処理の開始・終了、エラー、重要な情報などをログに出力し、問題発生時のデバッグを容易にします。
*   **設定ファイル**: スクレイピング対象のURL、保存パス、モデルのパス、予測結果の出力パスなどを設定ファイル（例: [`module/config.json`](module/config.json)）で管理し、柔軟性を高めます。
*   **依存関係管理**: `requirements.txt` を適切に管理し、必要なライブラリがインストールされるようにします。
*   **データ管理**: スクレイピングした生データ、前処理済みデータ、予測結果などを適切に保存・管理するディレクトリ構造を確立します。