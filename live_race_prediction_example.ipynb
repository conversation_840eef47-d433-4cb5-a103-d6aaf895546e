{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 実際の競馬レース予測システム使用例\n", "\n", "このノートブックでは、`live_race_predictor.py`を使用して実際の競馬レースの予測を行う方法を示します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# プロジェクトのルートディレクトリをパスに追加\n", "sys.path.append('.')\n", "\n", "from live_race_predictor import LiveRacePredictor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 予測システムの初期化\n", "\n", "学習済みモデルとスケーラーがある場合は、それらを指定して初期化します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 予測システムを初期化\n", "# モデルとスケーラーのパスは実際のファイルパスに置き換えてください\n", "predictor = LiveRacePredictor(\n", "    model_path=None,  # 'path/to/your/model.pkl'\n", "    scaler_path=None  # 'path/to/your/scaler.pkl'\n", ")\n", "\n", "print(\"予測システムを初期化しました\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 単一レースの予測（完全な処理フロー）\n", "\n", "レースIDを指定して、スクレイピングから予測まで一括で実行します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 予測したいレースIDを指定\n", "race_id = \"202406010101\"  # 実際のレースIDに置き換えてください\n", "\n", "# 完全な予測処理を実行\n", "results_df = predictor.predict_race_complete(\n", "    race_id=race_id,\n", "    skip_existing=True,  # 既存ファイルをスキップ\n", "    save_results=True    # 結果をCSVに保存\n", ")\n", "\n", "if not results_df.empty:\n", "    print(f\"\\n予測結果のDataFrame形状: {results_df.shape}\")\n", "    print(\"\\n予測結果の上位5頭:\")\n", "    display(results_df.head())\n", "else:\n", "    print(\"予測処理に失敗しました\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. ステップバイステップでの予測処理\n", "\n", "各ステップを個別に実行して、処理の詳細を確認できます。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ1: レースデータのスクレイピング\n", "race_id = \"202406010102\"  # 別のレースIDで試す\n", "\n", "success = predictor.scrape_race_data(race_id, skip_existing=True)\n", "print(f\"レースデータスクレイピング: {'成功' if success else '失敗'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ2: レースデータの処理\n", "race_info_df, race_results_df, race_name = predictor.process_race_data(race_id)\n", "\n", "print(f\"レース名: {race_name}\")\n", "print(f\"レース情報: {race_info_df.shape}\")\n", "print(f\"出馬表: {race_results_df.shape}\")\n", "\n", "if not race_results_df.empty:\n", "    print(\"\\n出馬表の例:\")\n", "    display(race_results_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ3: 出走馬のID抽出\n", "horse_ids = predictor.extract_horse_ids(race_id)\n", "print(f\"抽出された馬ID数: {len(horse_ids)}\")\n", "print(f\"馬ID例: {horse_ids[:5] if horse_ids else 'なし'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ4: 馬データのスクレイピング\n", "if horse_ids:\n", "    success = predictor.scrape_horse_data(horse_ids[:5], skip_existing=True)  # 最初の5頭のみテスト\n", "    print(f\"馬データスクレイピング: {'成功' if success else '失敗'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ5: 馬データの処理\n", "if horse_ids:\n", "    horse_info_df, horse_results_df = predictor.process_horse_data(horse_ids[:5])\n", "    print(f\"馬基本情報: {horse_info_df.shape}\")\n", "    print(f\"馬過去成績: {horse_results_df.shape}\")\n", "    \n", "    if not horse_info_df.empty:\n", "        print(\"\\n馬基本情報の例:\")\n", "        display(horse_info_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ステップ6: 特徴量作成\n", "if not race_results_df.empty:\n", "    features_df = predictor.create_features(\n", "        race_info_df, race_results_df, \n", "        horse_info_df if 'horse_info_df' in locals() else pd.DataFrame(),\n", "        horse_results_df if 'horse_results_df' in locals() else pd.DataFrame()\n", "    )\n", "    \n", "    print(f\"特徴量DataFrame: {features_df.shape}\")\n", "    if not features_df.empty:\n", "        print(f\"\\n特徴量カラム: {list(features_df.columns)}\")\n", "        display(features_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. モデルを使った予測（モデルがある場合）\n", "\n", "学習済みモデルがロードされている場合の予測例です。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# モデルがロードされている場合の予測\n", "if predictor.model is not None and 'features_df' in locals() and not features_df.empty:\n", "    prediction_results = predictor.predict_race(features_df)\n", "    \n", "    print(\"予測結果:\")\n", "    display(prediction_results[['馬名', '馬番', '予測順位', '勝率予測']].head(10))\n", "else:\n", "    print(\"モデルがロードされていないか、特徴量データがありません\")\n", "    print(\"モデルなしでも、データの取得と前処理は確認できます\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 結果の分析\n", "\n", "取得したデータの基本的な分析を行います。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# データの基本統計\n", "if 'features_df' in locals() and not features_df.empty:\n", "    print(\"=== データ分析 ===\")\n", "    print(f\"出走頭数: {len(features_df)}\")\n", "    \n", "    # 数値カラムの統計\n", "    numeric_cols = features_df.select_dtypes(include=['number']).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f\"\\n数値特徴量数: {len(numeric_cols)}\")\n", "        print(\"\\n基本統計:\")\n", "        display(features_df[numeric_cols].describe())\n", "    \n", "    # カテゴリカルカラムの情報\n", "    categorical_cols = features_df.select_dtypes(include=['object']).columns\n", "    if len(categorical_cols) > 0:\n", "        print(f\"\\nカテゴリカル特徴量数: {len(categorical_cols)}\")\n", "        for col in categorical_cols[:5]:  # 最初の5つのみ表示\n", "            unique_values = features_df[col].nunique()\n", "            print(f\"{col}: {unique_values}種類\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 使用上の注意点\n", "\n", "- レースIDは実際に存在するものを使用してください\n", "- スクレイピングには時間がかかる場合があります\n", "- 学習済みモデルがない場合は、データの取得と前処理のみ実行されます\n", "- 予測結果は参考程度に留め、実際の投資判断には使用しないでください"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}