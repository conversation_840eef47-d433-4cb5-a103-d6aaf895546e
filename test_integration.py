#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
統合システムの動作確認スクリプト

新しい統合システムが正常に動作するかテストします。
"""

import sys
import logging
from pathlib import Path

# プロジェクトのルートディレクトリをパスに追加
sys.path.append('.')

def test_imports():
    """インポートテスト"""
    print("=== インポートテスト ===")
    
    try:
        # コアモジュール
        from keiba_ai_system.core.processors import RaceProcessor, HorseProcessor
        print("✓ プロセッサーのインポート成功")
        
        from keiba_ai_system.core.features import FeatureEngineeringManager
        print("✓ 特徴量エンジニアリングのインポート成功")
        
        from keiba_ai_system.core.utils import LocalPaths, UrlPaths
        print("✓ ユーティリティのインポート成功")
        
        # 予測システム
        from keiba_ai_system.prediction import LiveRacePredictor
        print("✓ 予測システムのインポート成功")
        
        print("✓ 全てのインポートが成功しました")
        return True
        
    except ImportError as e:
        print(f"✗ インポートエラー: {e}")
        return False

def test_basic_functionality():
    """基本機能テスト"""
    print("\n=== 基本機能テスト ===")
    
    try:
        # プロセッサーの初期化
        from keiba_ai_system.core.processors import RaceProcessor
        race_processor = RaceProcessor()
        print("✓ RaceProcessorの初期化成功")
        
        # 特徴量管理システムの初期化
        from keiba_ai_system.core.features import FeatureEngineeringManager
        feature_manager = FeatureEngineeringManager()
        print("✓ FeatureEngineeringManagerの初期化成功")
        
        # 特徴量リストの取得
        features = feature_manager.list_features(enabled_only=True)
        print(f"✓ 有効な特徴量数: {len(features)}")
        
        # 予測システムの初期化
        from keiba_ai_system.prediction import LiveRacePredictor
        predictor = LiveRacePredictor()
        print("✓ LiveRacePredictorの初期化成功")
        
        print("✓ 基本機能テストが成功しました")
        return True
        
    except Exception as e:
        print(f"✗ 基本機能テストエラー: {e}")
        return False

def test_feature_engineering():
    """特徴量エンジニアリングテスト"""
    print("\n=== 特徴量エンジニアリングテスト ===")
    
    try:
        import pandas as pd
        from keiba_ai_system.core.features import FeatureEngineeringManager
        
        # サンプルデータを作成
        sample_data = pd.DataFrame({
            'horse_id': ['horse_001', 'horse_002'],
            '馬番': [1, 2],
            '枠番': [1, 1],
            '斤量': [55.0, 56.0],
            '人気': [1, 3],
            '性齢': ['牡4', '牝3']
        })
        
        # 特徴量管理システムを初期化
        manager = FeatureEngineeringManager()
        
        # 基本特徴量を計算
        from keiba_ai_system.core.features import FeatureCategory
        basic_features = manager.list_features(category=FeatureCategory.BASIC, enabled_only=True)
        
        if basic_features:
            result = manager.calculate_features(
                data=sample_data,
                feature_names=basic_features[:3]  # 最初の3つのみテスト
            )
            print(f"✓ 特徴量計算成功: {result.shape}")
        else:
            print("! 基本特徴量が見つかりません")
        
        print("✓ 特徴量エンジニアリングテストが成功しました")
        return True
        
    except Exception as e:
        print(f"✗ 特徴量エンジニアリングテストエラー: {e}")
        return False

def test_directory_structure():
    """ディレクトリ構造テスト"""
    print("\n=== ディレクトリ構造テスト ===")
    
    required_dirs = [
        'keiba_ai_system',
        'keiba_ai_system/core',
        'keiba_ai_system/core/processors',
        'keiba_ai_system/core/features',
        'keiba_ai_system/core/utils',
        'keiba_ai_system/prediction',
        'keiba_ai_system/examples',
        'keiba_ai_system/docs'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path} が見つかりません")
            all_exist = False
    
    if all_exist:
        print("✓ ディレクトリ構造テストが成功しました")
    else:
        print("✗ 一部のディレクトリが見つかりません")
    
    return all_exist

def test_config_files():
    """設定ファイルテスト"""
    print("\n=== 設定ファイルテスト ===")
    
    config_files = [
        'keiba_ai_system/core/features/config.yaml',
        'keiba_ai_system/examples/configs/production.yaml',
        'keiba_ai_system/examples/configs/development.yaml'
    ]
    
    all_exist = True
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✓ {config_file}")
            
            # YAML形式の確認
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                print(f"  ✓ YAML形式が正しい")
            except Exception as e:
                print(f"  ✗ YAML形式エラー: {e}")
                all_exist = False
        else:
            print(f"✗ {config_file} が見つかりません")
            all_exist = False
    
    if all_exist:
        print("✓ 設定ファイルテストが成功しました")
    else:
        print("✗ 一部の設定ファイルに問題があります")
    
    return all_exist

def main():
    """メイン関数"""
    print("競馬AI予測システム統合版 - 動作確認テスト")
    print("=" * 50)
    
    # ログ設定
    logging.basicConfig(level=logging.WARNING)  # テスト中はWARNING以上のみ表示
    
    tests = [
        ("ディレクトリ構造", test_directory_structure),
        ("インポート", test_imports),
        ("基本機能", test_basic_functionality),
        ("特徴量エンジニアリング", test_feature_engineering),
        ("設定ファイル", test_config_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}テスト中に予期しないエラー: {e}")
            results.append((test_name, False))
    
    # 結果サマリー
    print("\n" + "=" * 50)
    print("テスト結果サマリー:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n合計: {passed}/{len(results)} テストが成功")
    
    if passed == len(results):
        print("\n🎉 全てのテストが成功しました！")
        print("統合システムは正常に動作しています。")
    else:
        print(f"\n⚠️  {len(results) - passed}個のテストが失敗しました。")
        print("問題を修正してから再度テストしてください。")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
